export default [
  {
    name: 'vibe-marketing-init',
    path: 'vibe-marketing/index',
    redirect: { name: 'vibe-marketing-plan' },
    component: () => import('@/pages/vibe-marketing/index.vue'),
    meta: {
      auth: [],
    },
    children: [
      {
        path: 'plan',
        name: 'vibe-marketing-plan',
        component: () => import('@/pages/vibe-marketing/pages/plan/index.vue'),
        meta: {
          auth: [],
        },
      },
      {
        path: 'workspace',
        name: 'vibe-marketing-workspace',
        component: () => import('@/pages/vibe-marketing/pages/workspace/index.vue'),
        meta: {
          auth: [],
        },
      },
      {
        path: 'idea',
        name: 'vibe-marketing-idea',
        component: () => import('@/pages/vibe-marketing/pages/idea/index.vue'),
        meta: {
          auth: [],
        },
      },
      {
        path: 'setting',
        name: 'vibe-marketing-setting',
        component: () => import('@/pages/vibe-marketing/pages/setting/index.vue'),
        meta: {
          auth: [],
        },
      },
    ],
  },
  {
    name: 'vibe-marketing-create-tmp',
    path: 'vibe-marketing/create-tmp',
    component: () => import('@/pages/vibe-marketing/pages/create-tmp/index.vue'),
    meta: {
      auth: [],
    },
  },
  {
    name: 'vibe-marketing-apply-tmp',
    path: 'vibe-marketing/apply-tmp',
    component: () => import('@/pages/vibe-marketing/pages/apply-tmp/index.vue'),
    meta: {
      auth: [],
    },
  },
  {
    name: 'vibe-marketing-agent',
    path: 'vibe-marketing/agent',
    component: () => import('@/pages/vibe-marketing/pages/agent/index.vue'),
    meta: {
      auth: [],
    },
  },
  // {
  //   name: 'vibe-marketing-session-list',
  //   path: 'vibe-marketing/session-list',
  //   component: () => import('@/pages/vibe-marketing/pages/session-list/index.vue'),
  //   meta: {
  //     auth: [],
  //   },
  // },
]
