<template>
  <Dialog
    v-if="visible"
    class="qrposter-create-dialog"
    width="1000px"
    :title="$t('marketing.commons.xjhb_0844db')"
    append-to-body
    :close-on-click-modal="false"
    :visible="visible"
    :loading="isLoading"
    @onSubmit="handleSave"
    @onClose="handleCloseDialog"
  > 
    <QrposterCreateContent 
      ref="qrposterCreateContent"
      :defaultMarketingEventId="defaultMarketingEventId"
      :defaultMarketingEventName="defaultMarketingEventName"
      :defaultMarketingActivityId="defaultMarketingActivityId"
      :groupId="groupId"
      @update:list="handleUpdateList"
      @created="handleCreated"
      @update:visible="handleUpdateVisible"
      @setLoading="setLoading"
     />
  </Dialog>
</template>
<script>
import Dialog from '@/components/dialog/index.vue'
import QrposterCreateContent from './qrposter-create-content.vue'

export default {
  components: {
    QrposterCreateContent,
    Dialog
  },
  props: {
    visible: {
      type: <PERSON><PERSON>an,
      default: false,
    },
    defaultMarketingEventId: {
      type: String,
      default: '',
    }, // 市场活动ID
    defaultMarketingEventName: {
      type: String,
      default: '',
    }, // 市场活动名称
    defaultMarketingActivityId: {
      type: String,
      default: '',
    }, // 营销活动ID
    groupId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isLoading: false,
    }
  },
  computed: {},
  watch: {},
  beforeCreate() {
  },
  mounted() {  },
  methods: {
    setLoading(loading) {
      this.isLoading = loading
    },
    handleSave() {
      if (this.$refs.qrposterCreateContent) {
        this.$refs.qrposterCreateContent.handleSave()
      }
    },
    handleCloseDialog() {
      if (this.$refs.qrposterCreateContent) {
        this.$refs.qrposterCreateContent.handleCloseDialog()
      }
      this.$emit('update:visible', false)
    },
    handleUpdateList() {
      this.$emit('update:list')
    },
    handleCreated() {
      this.$emit('created')
    },
    handleUpdateVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
}
</script>
<style lang="less" scoped>
</style>
