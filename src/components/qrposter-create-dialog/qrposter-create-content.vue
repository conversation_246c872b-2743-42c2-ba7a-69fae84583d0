<template>
  <div class="dialog-body-wrapper">
      <div class="wrapper__left">
        <div class="qrposter-preview__label">
          {{ $t('marketing.commons.yl_645dbc') }}
        </div>
        <div class="qrposter-preview__poster">
          <VPosterEditor
            ref="$pe"
            :background-image="backgroundImage"
            :show-logo="data_jumpType === 2"
            :fonts="model_fonts"
            :qrcode="model_qrcode"
            :qrcode-radius="data_codeType === 'miniappQrcode'"
          />
          <!-- <div class="poster__cover" :style="{ 'background-image': `url(${showingImage})` }">
            <div class="cover__emptytips" v-if="!showingImage">
              <div class="emptytips__bg"></div>
            </div>
          </div>-->
        </div>
      </div>
      <div class="wrapper__right">
        <div
          v-show="!defaultMarketingEventId"
          class="edit__inline"
        >
          <div class="label">
            <em>*</em>
            <div class="label__title">
              {{ $t('marketing.commons.schd_833ba0') }}
            </div>
          </div>
          <VMarketingEventSelector
            v-model="model_marketingEvent"
            class="input"
          />
          <!-- <Input
            class="input"
            type="text"
            v-model="marketingEventName"
            maxlength="30"
            placeholder="请重试"
            :disabled="true"
          ></Input> -->
        </div>
        <div class="edit__inline">
          <div class="label">
            <em>*</em>
            <div class="label__title">
              {{ $t('marketing.commons.hbmc_765651') }}
            </div>
          </div>
          <Input
            v-model="model_title"
            class="input"
            type="text"
            maxlength="30"
            :placeholder="$t('marketing.commons.qsrhbmc_c02d2c')"
          />
        </div>
        <div class="edit__block">
          <div class="label">
            <em>*</em>
            <div class="label__title">
              {{ $t('marketing.commons.hbmb_9450e0') }}
            </div>
            <div class="label__tips">
              {{ $t('marketing.components.qrposter_create_dialog.bdsczcgszd_af6db3') }}<br>{{ $t('marketing.commons.zzhbjhtzck_5bb6e1') }}
            </div>
          </div>
          <VAddBackground @change="handleBackgroundChange" />
          <!-- <div class="" style="margin: 10px 110px;">
            <fx-button size="small" type="primary">制作海报</fx-button>
            <fx-button size="small">本地上传</fx-button>
          </div>  -->
        </div>
        <div class="edit__inline">
          <div class="label">
            <em>*</em>
            <div class="label__title">
              {{ $t('marketing.components.qrposter_create_dialog.ewmscms_cbee6f') }}
            </div>
          </div>
          <el-radio-group
            v-model="data_renderType"
            class="input"
            @change="handleAddQr"
          >
            <el-radio :label="'yxt'">
              {{ $t('marketing.components.qrposter_create_dialog.yxtsc_e1eb4d') }}
            </el-radio>
            <el-radio
              v-if="isExternalQrcodeOpen"
              :label="'external'"
            >
              {{ $t('marketing.components.qrposter_create_dialog.wbxtsc_68ee83') }}
              <a
                class="external_question_icon"
                href="https://help.fxiaoke.com/93d5/0f81/935b"
                target="_blank"
              />
            </el-radio>
          </el-radio-group>
        </div>
        <div
          v-if="data_renderType === 'external'"
          class="edit__inline"
        >
          <div
            class="label"
            style="align-self: flex-start;"
          >
            <div class="label__title">
              {{ $t('marketing.commons.cssz_9e4ab3') }}<QuestionTooltip
                class="external_question"
                effect="dark"
              >
                <div slot="question-content">
                  {{ $t('marketing.components.qrposter_create_dialog.ccszdcshcd_28e02e') }}
                </div>
              </QuestionTooltip>
            </div>
          </div>
          <div class="param__setting">
            <div
              v-for="(item, index) in external_params"
              :key="index"
              class="param__item"
            >
              <Input
                class="param__input"
                type="text"
                :value="item.key"
                :placeholder="$t('marketing.components.qrposter_create_dialog.srywcsm_414eb5')"
                size="small"
                @input="(val) => handleParamInput({
                  index,
                  key: 'key',
                  value: val
                })"
              />
              <Input
                class="param__input"
                type="text"
                :value="item.value"
                :placeholder="$t('marketing.components.qrposter_create_dialog.srcsmrz_04ec2c')"
                size="small"
                @input="(val) => handleParamInput({
                  index,
                  key: 'value',
                  value: val
                })"
              />
              <div class="param__opts">
                <fx-link
                  type="standard"
                  size="small"
                  style="padding: 0 8px"
                  @click="handleParamDel(index)"
                >
                  {{ $t('marketing.commons.sc_2f4aad') }}
                </fx-link>
              </div>
            </div>
            <fx-link
              class="param__add"
              type="standard"
              size="small"
              style="padding: 0 8px"
              @click="handleParamAdd"
            >
              <i
                class="el-icon-plus"
              />{{ $t('marketing.components.qrposter_create_dialog.xzcs_4c0eea') }}
            </fx-link>
          </div>
        </div>
        <div
          v-if="data_renderType === 'yxt'"
          class="edit__inline"
        >
          <div class="label">
            <em>*</em>
            <div class="label__title">
              {{ $t('marketing.commons.smhtz_ad0c8b') }}
            </div>
          </div>
          <VJumpSelector
            v-loading="loading_vjumpselector"
            class="input"
            :jump-model-type-config="{ model_type: data_jumpType }"
            :meeting-id="meetingId"
            :meeting-title="meetingTitle"
            :form-id="formId"
            :form-name="formName"
            :marketing-event-id="marketingEventId"
            v-bind="$attrs"
            @update:jumpType="handleJumpTypeChange"
            @update:selected="handleJumpSelect"
          />
        </div>
        <div
          v-if="data_jumpType === 13 && data_renderType === 'yxt'"
          class="edit__inline"
        >
          <div
            class="label"
            style="align-self: flex-start;"
          >
            <!-- <em>*</em> -->
            <div class="label__title">
              {{ $t('marketing.commons.tjygsz_657817') }}
            </div>
          </div>
          <fx-radio-group
            v-model="work_qrcode_add_setting"
            class="input"
            style="margin-left: 13px;"
          >
            <fx-radio :label="1">
              {{ $t('marketing.commons.dqxzhmzpzd_1257bd') }}
            </fx-radio>
            <fx-radio
              style="margin-top: 8px;"
              :label="2"
            >
              {{ $t('marketing.commons.ygjstgrwxz_407505') }}
            </fx-radio>
          </fx-radio-group>
        </div>
        <div
          v-if="data_jumpType === 13 && data_renderType === 'yxt'"
          class="edit__inline"
        >
          <div
            class="label"
            style="align-self: flex-start;"
          >
            <!-- <em>*</em> -->
            <div class="label__title">
              {{ $t('marketing.commons.sbts_df669e') }}
              <QuestionTooltip
                :offset="40"
                style="margin-left: 4px;"
                effect="dark"
              >
                <div
                  slot="question-content"
                >
                  {{ $t('marketing.commons.tgygwhtjkd_4f65dd') }}
                </div>
              </QuestionTooltip>
            </div>
          </div>
          <fx-radio-group
            v-model="work_qrcode_error_tips"
            class="input"
            style="margin-left: 13px;"
          >
            <fx-radio :label="1">
              {{ $t('marketing.commons.wtsmrsydqh_f90edb') }}
            </fx-radio>
            <fx-radio
              style="margin-top: 8px;"
              :label="2"
            >
              {{ $t('marketing.commons.tssczshbsb_22e761') }}
            </fx-radio>
          </fx-radio-group>
        </div>
        <!-- <div class="edit__inline">
          <div class="label">
            <em>*</em>
            <div class="label__title">添加二维码</div>
          </div>
          <fx-button @click="handleAddQr">添加二维码</fx-button>
        </div>-->
        <div
          v-if="showQrSetting && data_renderType === 'yxt'"
          class="edit__inline"
        >
          <div class="label">
            <em>*</em>
            <div class="label__title">
              {{ $t('marketing.commons.ewmlx_6fdc01') }}
            </div>
          </div>
          <el-radio-group
            v-model="data_codeType"
            :disabled="/2|3/.test(data_jumpType)"
            class="input"
            @change="handleSetCodeType"
          >
            <!-- data_jumpType=2为渠道二维码，只能够用h5二维码 -->
            <el-radio :label="'qrcode'">
              {{ $t('marketing.commons.ymewm_84078c') }}
            </el-radio>
            <el-radio :label="'miniappQrcode'">
              {{ $t('marketing.commons.xcxewm_4e85a1') }}
            </el-radio>
          </el-radio-group>
        </div>
        <div
          v-if="showQrSetting"
          class="edit__inline"
        >
          <div class="label">
            <em />
            <div class="label__title">
              {{ $t('marketing.commons.tgqd_054ed9') }}
            </div>
          </div>
          <ChannelSelector
            @change="handleSpreadChannelChange"
            @wechatSelected="handleWechatSelected"
          />
        </div>
        <div
          v-if="backgroundImage && !flag_isCkt"
          class="edit__text"
        >
          <div class="label">
            <em />
            <div class="label__title">
              {{ $t('marketing.commons.tjwz_399ca7') }}
            </div>
          </div>
          <!--  @input="handleToolChange" :value="editorToolbarStyle"  -->
          <VAddFont @update:fonts="handleFontsChange" />
        </div>
      </div>
    </div>
</template>
<script>

import http from '@/services/http/index.js'
import { generateFeedKey } from '@/utils/feedKey.js'
import VMarketingEventSelector from '@/pages/promotion-activity/common/pick-selfobject.vue'
import VJumpSelector from './components/VJumpSelector.vue'
import VPosterEditor from './components/v-poster-editor.vue'
import VAddFont from './components/v-add-font.vue'
import VAddBackground from './components/VAddBackground.vue'
import ChannelSelector from '@/components/ChannelSelector/index.vue'
import kisvData from '@/modules/kisv-data.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'

import qrcode from './res/qrcode.jpg'
import miniappQrcode from './res/miniAppQrcode.png'

export default {
  components: {
    ElRadioGroup: FxUI.RadioGroup,
    ElRadio: FxUI.Radio,
    VMarketingEventSelector,
    VJumpSelector,
    VPosterEditor,
    VAddFont,
    VAddBackground,
    ChannelSelector,
    Input: FxUI.Input,
    Button: FxUI.Button,
    QuestionTooltip,
  },
  props: {
    defaultMarketingEventId: {
      type: String,
      default: '',
    }, // 市场活动ID
    defaultMarketingEventName: {
      type: String,
      default: '',
    }, // 市场活动名称
    defaultMarketingActivityId: {
      type: String,
      default: '',
    }, // 营销活动ID
    groupId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      vDatas: kisvData.datas,
      emptyMiniappQrcode: '',
      marketingEventId: '',
      meetingId: '', // 会议id，通过市场活动查出来，不需要传进来
      meetingTitle: '', // 会议标题，通过市场活动查出来，不需要传进来
      formId: '', // 会议表单id，通过市场活动查出来，不需要传进来
      formName: '', // 会议表单名称，通过市场活动查出来，不需要传进来

      model_marketingEvent: {},
      model_forwardType: '',
      model_title: '',
      model_bgTAPath: 'TA_9428ccd1b17f4ee78e735227c885a162.jpg',
      model_finalTAPath: 'TA_9428ccd1b17f4ee78e735227c885a162.jpg',
      model_qrStyle: '',
      model_channelValue: '',

      data_params: {}, // 从jump-select组件传过来的数据
      data_jumpType: null, // 扫码后跳转类型
      data_materielQrCodeId: '', // 调用生成qrcode接口时返回的Id
      data_urlQrCodeId: '', // 调用生成qrcode接口时返回的Id
      data_codeType: 'qrcode', // 码类型 qrcode：二维码（方的）  miniappQrcode：小程序码（圆的）
      data_renderType: 'yxt', // 二维码生成模式 yxt:营销通  external:外部
      showingImage: '',
      backgroundImage: '', // 背景图

      model_marketingactivity: $t('marketing.commons.wsschdmz_1ac420'),

      model_fonts: [], // 添加文字 - 文字数组，文字对象包含text以及style
      model_qrcode: '', // 二维码
      model_cktData: {}, // 创客贴数据，在选用创客贴海报背景时，才会有此数据
      flag_isCkt: false,

      loading_vjumpselector: false,
      wxAppId: '',
      external_params: [],
      work_qrcode_error_tips: 1,
      work_qrcode_add_setting: 1,
    }
  },
  computed: {
    isExternalQrcodeOpen() {
      const { uinfo = {} } = this.vDatas || {}
      if (uinfo.outQrCode && uinfo.outQrCodeActive) {
        return true
      }
      return false
    },
    showQrSetting() {
      return /^(1|4|5|6)$/.test(this.data_jumpType)
    },
    currentCompanyName() {
      return FS.getAppStore('contactData') && FS.getAppStore('contactData').companyName
    },
    realMiniAppQrcode() {
      if (this.isDev()) {
        // 开发环境下，若后端给了图，则加代理使用；否则用本地备用图
        return this.emptyMiniappQrcode ? `/marketing/file/redirectDownload?url=${this.emptyMiniappQrcode}` : miniappQrcode
      } if (!this.emptyMiniappQrcode) {
        // 线上环境下，后端给的码异常时，用同域下的备用图（直接用miniappQrcode是跨域的，它在cdn上）
        return `${window.location.origin}/ec/kemai/release/static/miniAppQrcode.jpg`
      }
      // 线上正常情况下，直接用后端码
      return this.emptyMiniappQrcode
    },
    realQrcode() {
      return this.isDev() ? qrcode : `${window.location.origin}/ec/kemai/release/static/qrcode.jpg`
    },
    openDingDing() {
      return this.$store.state.Global.openDingDing
    },
  },
  watch: {
    model_marketingEvent() {
      this.marketingEventId = this.model_marketingEvent.id
    },
    async marketingEventId() {
      this.loading_vjumpselector = true
      const { bindMeeting, meetingId, meetingTitle } = await this.checkConferenceStatus()
      if (bindMeeting) {
        const meetingDetail = await this.queryConferenceDetail(meetingId)
        console.log('meetingDetail', meetingDetail)
        this.formId = meetingDetail.formId
        this.formName = meetingDetail.formName
      }
      this.meetingId = meetingId
      this.meetingTitle = meetingTitle
      this.loading_vjumpselector = false
    },
  },
  beforeCreate() {
    // this.$options.components.JumpSelector = JumpSelector;
  },
  mounted() {
    this.marketingEventId = this.defaultMarketingEventId
    this.marketingActivityId = this.defaultMarketingActivityId
    this.createEmptyMiniappQrcode()
    const externalQrcodeParamsCache = sessionStorage.getItem('external_qrcode_params_cache')
    if (externalQrcodeParamsCache) {
      this.external_params = JSON.parse(externalQrcodeParamsCache) || []
    }
    // this.initPage();
  },
  methods: {
    handleParamInput(data) {
      this.$set(this.external_params, data.index, {
        ...this.external_params[data.index],
        [data.key]: data.value,
      })
    },
    handleParamAdd() {
      this.external_params.push({
        key: '',
        value: '',
      })
    },
    handleParamDel(index) {
      this.external_params.splice(index, 1)
    },
    isDev() {
      return window.APP_MARKETING_ENV === 'DEV' // 注入当前环境  DEV:开发环境 PRO:否则是生产环境
    },
    async queryConferenceDetail(meetingId) {
      const res = await YXT_ALIAS.http.queryConferenceDetail({ id: meetingId })
      if (res && res.errCode === 0) {
        return res.data
      }
      return {}
    },
    createEmptyMiniappQrcode() {
      http
        .createQRCode({
          type: 19,
          value: JSON.stringify({}),
        })
        .then(
          res => {
            if (res && res.errCode === 0) {
              // FxUI.Message.success('上传成功');
              this.emptyMiniappQrcode = res.data.qrCodeUrl
            }
          },
          () => { },
        )
    },
    handleAddQr() {
      if (!this.showQrSetting || this.data_renderType === 'external') {
        this.data_codeType = 'qrcode'
      }
      this.$nextTick(() => {
        if (this.data_jumpType === -1 && this.data_renderType === 'yxt') {
          this.model_qrcode = ''
        } else {
          this.model_qrcode = this.data_codeType === 'miniappQrcode' ? this.realMiniAppQrcode : this.realQrcode
        }
      })
    },
    handleSetCodeType() {
      this.handleAddQr()
    },
    handleSpreadChannelChange(channelValue) {
      this.model_channelValue = channelValue
    },
    handleWechatSelected(wxAppId) {
      this.wxAppId = wxAppId
    },
    validate() {
      const urlReg = /^(https?:\/\/)([0-9a-z.]+)(:[0-9]+)?([/0-9a-z.]+)?(\?[0-9a-z&=]+)?(#[0-9-a-z]+)?/i

      if (!this.model_title) {
        FxUI.Message.error($t('marketing.commons.qsrhbmc_c02d2c'))
        return false
      }
      if (!this.backgroundImage) {
        FxUI.Message.error($t('marketing.commons.qxzhbbjt_833829'))
        return false
      }

      // 外部二维码海报校验
      if (this.data_renderType === 'external') {
        if (this.external_params.length && this.external_params.some(item => (!item.key || !item.value))) {
          FxUI.Message.error($t('marketing.components.qrposter_create_dialog.qwscssz_3f25fe'))
          return false
        }
        return true
      }
      if (!this.data_jumpType) {
        FxUI.Message.error($t('marketing.commons.qxzsmhtzlx_94d8a7'))
        return false
      }
      // 跳转类型 1：内容  2：关注公众号 3：网页 4：会议 5：邀请函 6：报名表单(会议营销专用) 7：文章
      if (this.data_jumpType === 1 && !this.data_params.id) {
        FxUI.Message.error($t('marketing.commons.qxzsmhtzdn_6510c6'))
        return false
      }
      if (this.data_jumpType === 2 && !this.data_params.qrcodeUrl) {
        FxUI.Message.error($t('marketing.commons.qxzsmhtzdg_3b545e'))
        return false
      }
      if (this.data_jumpType === 3 && !this.data_params.url) {
        FxUI.Message.error($t('marketing.commons.qsrsmhtzdw_b3a887'))
        return false
      }
      if (this.data_jumpType === 3 && this.data_params.url && !urlReg.test(this.data_params.url)) {
        FxUI.Message.error($t('marketing.commons.qsrzqdwydz_4fb6f8'))
        return false
      }
      if (this.data_jumpType === 4 && !this.data_params.id) {
        FxUI.Message.error($t('marketing.commons.qxzsmhtzdh_12c492'))
        return false
      }
      if (this.data_jumpType === 5 && !this.data_params.id) {
        FxUI.Message.error($t('marketing.commons.qxzsmhtzdy_9acca0'))
        return false
      }
      if (this.data_jumpType === 6 && !this.data_params.id) {
        FxUI.Message.error($t('marketing.commons.qxzsmhtzdb_df8a56'))
        return false
      }
      if (this.data_jumpType === 7 && !this.data_params.id) {
        FxUI.Message.error($t('marketing.commons.qxzsmhtzdw_05cbcd'))
        return false
      }
      if (this.data_jumpType === 13 && !this.data_params.id) {
        FxUI.Message.error($t('marketing.commons.qxzqwyghm_0841e3'))
        return false
      }

      return true
    },
    handleSave() {
      if (!this.validate()) return
      const me = this
      this.$emit('setLoading', true)
      // const qrcodeObj = {}

      function afterAjax(qrcodeObj) {
        // 兼容一下本地环境的跨域绘制canvas
        let qrcodeImageSrc = qrcodeObj.qrCodeUrl
        if (qrcodeImageSrc.indexOf('wow.') !== -1) {
          qrcodeImageSrc = `${window.location.protocol}//${window.location.host}${qrcodeImageSrc.split('.com')[1]}`
        }
        // me.pwq.addQrcode(qrcodeImageSrc, me.data_codeType, () => {
        //   me.pwq.save();
        // });
        me.model_qrcode = qrcodeImageSrc
        me.$nextTick(() => {
          me.$refs.$pe.save(me.actuallySave)
        })
      }
      function errorAjax() {
        FxUI.Message.error($t('marketing.commons.ewmscsbqzs_6f7af8'))
        me.$emit('setLoading', false)
      }

      // 外部二维码
      if (this.data_renderType === 'external') {
        this.ajaxGetQrcodeId().then(
          res => {
            afterAjax(res)
          },
          error => {
            errorAjax()
          },
        )
      } else if (/^(1|3|4|5|6|7)$/.test(this.data_jumpType)) {
        // 跳转类型为内容/网页/会议/报名/文章
        // if (/1|4|5|6/.test(this.data_jumpType)) {
        if (this.data_codeType === 'miniappQrcode') {
          this.ajaxGetMiniAppQrcodeId().then(
            res => {
              afterAjax(res)
            },
            error => {
              errorAjax()
            },
          )
        } else {
          this.ajaxGetQrcodeId().then(
            res => {
              afterAjax(res)
            },
            error => {
              errorAjax()
            },
          )
        }
      } else if (/2/.test(this.data_jumpType)) {
        // 跳转类型为渠道
        const qrcodeImageSrc = `${window.location.origin}/marketing/file/redirectDownload?url=${this.data_params.qrcodeUrl}`

        this.model_qrcode = qrcodeImageSrc
        me.$nextTick(() => {
          me.$refs.$pe.save(me.actuallySave)
        })
      } else if (/13/.test(this.data_jumpType)) {
        // 跳转类型为员工活码
        const qrcodeImageSrc = `${window.location.origin}/marketing/file/redirectDownload?url=${this.data_params.url}`

        this.model_qrcode = qrcodeImageSrc
        me.$nextTick(() => {
          me.$refs.$pe.save(me.actuallySave)
        })
      } else if (this.data_jumpType === -1) {
        me.$refs.$pe.save(me.actuallySave)
      }
    },
    actuallySave(background, poster, qrStyle, posterStyle) {
      console.log('background, poster, qrStyle', background, poster, qrStyle)
      // this.model_tapath = `${res.TempFileName}.${res.FileExtension}`;
      this.model_qrStyle = qrStyle
      this.ajaxCreateQRPoster({
        bgTAPath: background,
        finalTAPath: poster,
        posterStyle: JSON.stringify(posterStyle),
      }).then(() => {
        this.$emit('update:list')
        this.$emit('created')
        this.handleCloseDialog()
      })
    },

    handleCloseDialog() {
      if (this.avatar) {
        this.avatar.destroy()
      }
      this.$emit('update:visible', false)
    },
    handleJumpTypeChange(jumpType) {
      this.data_jumpType = jumpType
      this.data_params = {}
      this.handleAddQr()
    },
    handleJumpSelect(params) {
      // this.data_jumpType = params.jumpType;
      this.model_forwardType = params.forwardType
      this.data_params = params
    },
    ajaxGetMiniAppQrcodeId() {
      const saveToWitch = 'data_materielQrCodeId' // 存入到哪个data中，可选项：data_materielQrCodeId | data_urlQrCodeId
      const params = {
        feedKey: generateFeedKey(this.data_params.id),
        objectId: this.data_params.id,
        // objectType： http://git.firstshare.cn/fs-marketing/fs-marketing/blob/develop/fs-marketing-common/src/main/java/com/facishare/marketing/common/enums/ObjectTypeEnum.java
        objectType: {
          1: 4, // 产品
          2: 6, // 文章
          3: 13, // 会议详情页
          4: 25, // 会议邀请函
          5: 16, // 自定义表单
          8: 13, // 会议的表单
          9: 26, // 微页面
          1900: 9999, // 外部内容
        }[this.data_params.forwardType],
        marketingActivityId: this.marketingActivityId,
        needSpreadUser: false,
        value: JSON.stringify({
          marketingEventId: this.marketingEventId,
          spreadChannel: this.model_channelValue,
          hostType: this.openDingDing ? 'ding' : 'fs',
          ea: FS.contacts.getCurrentEmployee().enterpriseAccount,
        }),
      }

      if (this.data_jumpType === 6) {
        params.value = JSON.stringify({
          ...JSON.parse(params.value),
          type: 12,
          objectId: this.data_params.id,
          formId: this.data_params.formId,
        })
      }

      return new Promise((resolve, reject) => {
        http.createWXQRCodeByFeed(params).then(
          res => {
            if (res && res.errCode === 0) {
              // FxUI.Message.success('上传成功');
              this[saveToWitch] = res.data.qrCodeId
              resolve(res.data)
            } else {
              reject()
            }
          },
          () => {
            reject()
          },
        )
      })
    },
    ajaxGetQrcodeId() {
      let saveToWitch = 'data_materielQrCodeId' // 存入到哪个data中，可选项：data_materielQrCodeId | data_urlQrCodeId
      const params = {
        // 详细map见： http://git.firstshare.cn/fs-marketing/fs-marketing/blob/f-2.3.1/fs-marketing-common/src/main/java/com/facishare/marketing/common/enums/qr/QRCodeTypeEnum.java
        type: {
          1: 10006, // 产品
          2: 10005, // 文章
          3: 10003, // 会议详情页
          4: 10004, // 会议邀请函
          5: 10001, // 自定义表单
          // 6: 0, // 公众号
          7: 10007, // 网页
          8: 10001, // 会议的表单
          9: 10010, // 微页面
          1900: 19999,
        }[this.data_params.forwardType],
        value: {
          marketingEventId: this.marketingEventId,
          marketingActivityId: this.marketingActivityId,
          spreadChannel: this.model_channelValue,
          wxAppId: this.wxAppId || '',
          byshare: 1,
        },
      }
      // 外部二维码参数
      if (this.data_renderType === 'external') {
        this.model_forwardType = 12
        delete params.value.byshare
        params.type = 1000001
        try {
          const employee = FS.contacts.getCurrentEmployee()
          if (employee.id) {
            params.value.outerQrcodeSpreadFsUid = employee.id
          }
        } catch (error) { /* empty */ }
        this.external_params.forEach(item => {
          if (item.value) { params.value[item.key] = item.value }
        })
        params.value = JSON.stringify(params.value)
        saveToWitch = 'data_urlQrCodeId'
      } else if (this.data_jumpType === 1) {
        // 为内容
        params.value = JSON.stringify(
          this.data_params.forwardType === 9
            ? { ...params.value, id: this.data_params.id, type: 1 }
            : { ...params.value, id: this.data_params.id },
        )
        saveToWitch = 'data_materielQrCodeId'
      } else if (this.data_jumpType === 3) {
        // 为url
        params.value = JSON.stringify({ ...params.value, url: this.data_params.url })
        saveToWitch = 'data_urlQrCodeId'
      } else if (this.data_jumpType === 4) {
        // 为会议
        params.value = JSON.stringify({ ...params.value, id: this.data_params.id, byshare: 1 })
      } else if (this.data_jumpType === 5) {
        // 为邀请函
        params.value = JSON.stringify({ ...params.value, id: this.data_params.id })
      } else if (this.data_jumpType === 6) {
        params.value = JSON.stringify({
          ...params.value,
          objectType: 13,
          objectId: this.data_params.id,
          formId: this.data_params.formId,
        })
      } else if (this.data_jumpType === 7) {
        // 文章
        params.value = JSON.stringify({ ...params.value, id: this.data_params.id, byshare: 1 })
        saveToWitch = 'data_materielQrCodeId'
      } else if (this.data_jumpType === 13) {
        // 员工活码
        params.value = JSON.stringify(params.value)
      } else {
        // 支持新类型
        params.value = JSON.stringify({ ...params.value, id: this.data_params.id })
      }
      return new Promise((resolve, reject) => {
        http.createQRCode(params).then(
          res => {
            console.log(res)
            if (res && res.errCode === 0) {
              this[saveToWitch] = res.data.qrCodeId
              sessionStorage.setItem('external_qrcode_params_cache', JSON.stringify(this.external_params))
              resolve(res.data)
            } else {
              reject()
            }
          },
          () => {
            reject()
          },
        )
      })
    },
    getParamsWhenJumpType6() {
      return JSON.stringify({})
    },
    getParamsByJumpType() {
      const params = {
        qrCodeId: null,
        qrCodeUrl: null,
        targetId: null,
        appId: null,
        wxAppId: null,
      }
      switch (this.data_jumpType) {
        case 1:
        case 4:
        case 5:
        case 7: {
          params.qrCodeId = this.data_materielQrCodeId
          params.targetId = this.data_params.id
          break
        }
        case 2: {
          params.targetId = this.data_params.qrcodeId
          params.qrCodeUrl = this.data_params.qrcodeUrl
          params.appId = this.data_params.appId
          params.wxAppId = this.data_params.wxAppId
          break
        }
        case 3: {
          params.qrCodeId = this.data_urlQrCodeId
          break
        }
        case 6: {
          params.qrCodeId = this.data_materielQrCodeId
          params.targetId = this.data_params.id
          break
        }
        case 13: {
          params.targetId = this.data_params.id
          params.qrCodeUrl = this.data_params.url
          params.failedOperation = this.work_qrcode_error_tips
          params.userAddSettings = this.work_qrcode_add_setting
          break
        }
        default: break
      }
      // 外部二维码参数
      if (this.data_renderType === 'external') {
        params.qrCodeId = this.data_urlQrCodeId
      }
      return params
    },
    ajaxCreateQRPoster(opts) {
      const params = $.extend(
        {
          marketingEventId: this.marketingEventId,
          marketingActivityId: this.marketingActivityId,
          forwardType: this.model_forwardType,
          title: this.model_title,
          bgTAPath: '',
          finalTAPath: '',
          qrStyle: JSON.stringify(this.model_qrStyle),
          type: 1,
          designId: (this.model_cktData && this.model_cktData.designId) || null,
          groupId: this.groupId,
        },
        this.getParamsByJumpType(),
        opts,
      )

      return new Promise((resolve, reject) => {
        http.createQRPoster(params).then(res => {
          this.$emit('setLoading', false)
          if (res && res.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.cjcg_04a691'))
            resolve()
          }
        })
      })
    },
    handleBackgroundChange(backgroundImage, cktData, isCkt) {
      console.log('handleBackgroundChange', backgroundImage, cktData, isCkt)
      this.backgroundImage = backgroundImage
      this.model_cktData = cktData
      this.flag_isCkt = isCkt
    },
    handleFontsChange(fonts) {
      this.model_fonts = fonts
    },
    // 查询当前市场活动是否会议营销市场活动
    async checkConferenceStatus() {
      if (!this.marketingEventId) {
        return {
          bindMeeting: false,
          meetingId: '',
        }
      }
      const { errCode, data } = await YXT_ALIAS.http.checkConferenceStatus({
        marketingEventId: this.marketingEventId,
      })
      if (errCode === 0) {
        return {
          bindMeeting: data.bindConference,
          meetingId: data.bindConferenceId,
          meetingTitle: data.bindConferenceTitle,
        }
      }
      return {
        bindMeeting: false,
        meetingId: '',
      }
    },
  },
}
</script>
<style lang="less" scoped>
@images: '../../assets/images/';

  .external_question {
    display: inline-block;
    vertical-align: -3px;
    margin-left: 5px;
    cursor: pointer;
  }

  .external_question_icon {
    vertical-align: -3px;
    margin-left: 5px;
    cursor: pointer;
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("../../assets/images/icon/icon-question.png") no-repeat center;
    background-size: 100%;
  }

  .el-dialog__header {
    box-shadow: 0 2px 5px 0 rgba(69, 79, 91, 0.08);
    color: #181c25;
    text-align: start;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    background: #f4f6f9;
    padding: 10px 20px 10px;
  }

  .dialog-body-wrapper {
    min-height: 552px;
    margin: 0;
    box-sizing: border-box;
    display: flex;

    .wrapper__left {
      width: 289px;
      background: #fafafa;
      padding: 16px 24px;
      box-sizing: border-box;

      .qrposter-preview__label {
        font-size: 13px;
        color: #181c25;
      }

      .qrposter-preview__poster {
        margin-top: 21px;
        margin-left: 0 !important;

        .poster__cover {
          width: 240px;
          height: 426.88px;
          background: #eee;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          background-size: cover;

          .cover__emptytips {
            color: #c1c5ce;
            font-size: 12px;

            .emptytips__bg {
              background-image: url('@{images}/defualt-pic.png');
              width: 70px;
              height: 57px;
              background-size: cover;
            }
          }
        }

        .poster__info {
          width: 240px;
          height: 64px;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #fff;

          .info__text {
            position: relative;
            width: 100%;
            height: 100%;

            .text--1 {
              font-size: 48px;
              transform: scale(0.2);
              position: absolute;
              left: 13px;
              top: 18px;
              white-space: nowrap;
              transform-origin: 0 0;
              color: #181c25;
            }

            .text--2 {
              font-size: 41.5px;
              transform: scale(0.2);
              white-space: nowrap;
              transform-origin: 0 0;
              position: absolute;
              top: 38px;
              left: 13px;
              color: #91959e;
            }
          }

          .info__qr {
            margin-right: 12px;
            flex-shrink: 0;

            .qr__bg {
              background-image: url('@{images}/qr-default.jpg');
              background-size: cover;
              width: 44px;
              height: 44px;
            }
          }
        }
      }
    }

    .wrapper__right {
      padding: 15px 30px;
      flex: 1;
      overflow: auto;

      .label {
        flex-shrink: 0;
        display: flex;
        align-items: center;

        em {
          color: #ff3f3f;
          display: block;
          width: 13px;
        }

        .label__title {
          width: 110px;
          font-size: 13px;
          color: #181c25;
          display: flex;
          align-items: center;
        }

        .label__tips {
          margin-left: 30px;
          font-size: 12px;
          color: #91959e;
        }
      }
    }

    .edit__upload {
      margin-top: 20px;
    }

    .edit__text {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .label {
        align-self: flex-start;
      }

      .font-style-editor {
        flex: 1;
      }
    }

    .edit__block {
      margin-top: 20px;

      /deep/.add__wrapper {
        margin: 10px 123px;
      }
    }

    .edit__inline {
      display: flex;
      align-items: center;
      margin-top: 20px;

      .input {
        // width: 402px;
        flex: 1;
      }
    }

    .param__setting {
      .param__item {
        display: flex;
        margin-bottom: 10px;
      }

      .param__input {
        width: 176px;
        margin-right: 10px;
      }

      .param__opts {
        display: flex;
      }

      .param__add {
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-weight: bold;
          margin-right: 5px;
        }
      }
    }

    .upload__wrapper {
      margin: 15px 0 0 110px;
    }
  }
</style>
