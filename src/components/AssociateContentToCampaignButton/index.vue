<template>
  <div :class="$style.associate_content_campaignButton">
    <Dropdown @command="handleCommand">
      <span :class="$style.title"
        ><i :class="['el-icon-plus', $style.icon]"></i>{{ $t('marketing.commons.tjnr_f75488') }}</span
      >
      <DropdownMenu slot="dropdown">
        <DropdownItem command="private_site">{{ $t('marketing.components.AssociateContentToCampaignButton.xjzswym_d781ee') }}</DropdownItem>
        <DropdownItem command="content_center">{{ $t('marketing.commons.cnrzxxz_1c0d28') }}</DropdownItem>
        <DropdownItem command="external_content">{{ $t('marketing.components.AssociateContentToCampaignButton.yywbnr_6d566f') }}</DropdownItem>
        <DropdownItem command="upload_pdf">
          <div :class="$style.uploadpdf">
            <span >{{ $t('marketing.commons.scwj_a6fc9e') }}</span>
            <QuestionTooltip
              effect="dark"
              :offset="192"
              style="margin:2px 0 0 4px;"
            >
              <span
                slot="question-content"
              >
                {{ $t('marketing.commons.jzcscyyndw_55a3e2') }}
              </span>
            </QuestionTooltip>
          </div>

          <!-- <FileToHexagon
            style="display:flex;"
            :marketing-event-id="marketingEventId"
            @completed="handleFileToHexagonCompleted"
            @onUpload="val => disabledFileUpload = val"
          >
            <span>{{ $t('marketing.commons.scwj_a6fc9e') }}</span>
            <QuestionTooltip
              effect="dark"
              :offset="192"
              style="margin:2px 0 0 4px;"
            >
              <span
                slot="question-content"
              >
                {{ $t('marketing.commons.jzcscyyndw_55a3e2') }}
              </span>
            </QuestionTooltip>
          </FileToHexagon> -->
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
    <!-- 添加推广内容 -->
    <SelectMaterialDialog
      v-if="selectMaterialDialogVisible"
      :tabbar="tabbar"
      :visible="selectMaterialDialogVisible"
      :marketingEventId="marketingEventId"
      @onSubmit="handleMaterialSelected"
      @material:add="handleMaterialSelected"
      @onClose="selectMaterialDialogVisible = false"
    />
    <ExternalContentCreate
      v-if="externalContentVisible"
      :visible.sync="externalContentVisible"
      @onCreated="handleExternalContentCreated"
    />
    <UploadPdfToSite ref="UploadPdfToSite" :marketingEventId="marketingEventId" :systemSite="1" @completed="handleFileToHexagonCompleted"/>
  </div>
</template>

<script>
import SelectMaterialDialog from "@/components/select-material-dialog";
import ExternalContentCreate from "@/pages/external-content/components/external-content-create";
import http from "@/services/http/index";
import { confirm } from '@/utils/globals';
import FileToHexagon from '@/components/file-to-hexagon/index.vue'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import UploadPdfToSite from '@/pages/site/components/UploadPdfToSite.vue'


export default {
  components: {
    FileToHexagon,
    QuestionTooltip,
    Dropdown: FxUI.Dropdown,
    DropdownMenu: FxUI.DropdownMenu,
    DropdownItem: FxUI.DropdownItem,
    SelectMaterialDialog,
    ExternalContentCreate,
    UploadPdfToSite
},
  props: {
    marketingEventId: String,
    tabbar: {
      type: Array,
      default: () => [10, 1, 4, 16]
    }
  },
  data() {
    return {
      selectMaterialDialogVisible: false,
      externalContentVisible: false,
      disabledFileUpload: false,
    };
  },
  methods: {
    uploadPdfFile() {
      this.$refs.UploadPdfToSite.showDialog();
    },
    handleFileToHexagonCompleted() {
      this.$emit('onMaterialAdded')
    },
    handleCommand(command) {
      if (command === "content_center") {
        this.selectMaterialDialogVisible = true;
      } else if (command === "private_site") {
        //根据链接识别活动类型获取对应活动ID
        const url = window.location.href;
        const extendParams = {};
        if(url.includes('live-marketing/dashboard')) {
          //直播活动详情页面
          extendParams.liveId = this.$route.params.id;
        } else if(url.includes('/meeting-marketing/')) {
          //会议活动详情页面
          extendParams.conferenceId = this.$route.params.id;
        }
        const route = this.$router.resolve({
          name: "site-create",
          query: {
            ...extendParams,
            createType: "private",
            marketingEventId: this.marketingEventId || "",
            from: 'dialog'
          }
        });
        window.open(route.href, "_blank");
        confirm($t('marketing.components.AssociateContentToCampaignButton.sfywczswym_832358'), $t('marketing.commons.ts_02d981'), {}).then(() => {
          this.$emit("onMaterialAdded");
        });
      } else if (command === 'external_content') {
        this.externalContentVisible = true;
      } else if(command === 'upload_pdf') {
        this.uploadPdfFile();
      }
    },
    async handleMaterialSelected(row) {
      // 添加物料到市场活动
      const { errCode } = await http.addMaterial({
        marketingEventId: this.marketingEventId,
        objectId: row.id,
        objectType: row.objectType || (row.type == 1 ? 6 : row.type)
      });
      if (errCode === 0) {
        this.selectMaterialDialogVisible = false;
        this.$emit("onMaterialAdded");
      }
    },
    handleExternalContentCreated(data) {
      this.handleMaterialSelected({
        objectType: 9999,
        id: data.id
      });
    }
  }
};
</script>

<style lang="less" module>
.associate_content_campaignButton {
  .title {
    color: @color-link;
    .icon {
      margin-right: 3px;
      font-weight: bold;
    }
  }
}
.uploadpdf {
  display: flex;
}
</style>
