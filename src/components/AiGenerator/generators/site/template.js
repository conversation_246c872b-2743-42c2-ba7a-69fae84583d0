export const template = {
  "placeholderStyle": {
      "color": "#cbcccf"
  },
  "inputStyle": {
      "color": "#181c25"
  },
  "style": {
      "width": 375,
      "backgroundColor": "#fff",
      "backgroundSize": "100%",
      "backgroundRepeat": "no-repeat",
      "backgroundImage": ""
  },
  "id": "4d9f14dff4774016a0ef6cc0792c463a",
  "type": "page",
  "name": "",
  "title": "",
  "version": "1.0.0",
  "cover": "",
  "pcAdaptation": false,
  "isPdf": false,
  "shareOpts": {
      "title": "",
      "desc": "",
      "link": "",
      "imgUrl": ""
  },
  "popupOpts": {
      "isPreview": false,
      "fillType": 1,
      "isShow": false,
      "backgroundStyle": {
          "objectFit": "cover",
          "width": "100%",
          "height": "auto"
      },
      "width": 280,
      "height": 490,
      "borderRadius": 16,
      "positionY": 50,
      "show": 0,
      "coverUrl": "",
      "mode": 1,
      "action": {

      }
  },
  "backgroundOpts": {
      "mode": 1,
      "bgColor": "#f9f9f9",
      "bgImage": "",
      "fillType": "horizontal-filling",
      "carouselImgs": [

      ],
      "carouselPointType": 5,
      "carouselPointBottom": 24,
      "carouselPointIsShow": false,
      "carouselPointAutoPlay": true,
      "playDuration": 200,
      "carouselPointColor": "rgba(255, 255, 255, 0.5)",
      "carouselPointActiveColor": "#fff",
      "carouselPointActiveIndex": 0,
      "carouselPlayInterval": 3000,
      "circlePointSize": 6,
      "circlePointMargin": 5,
      "dashPointWidth": 12,
      "dashPointHeight": 2,
      "dashPointMargin": 4,
      "slidePointWidth": 180,
      "slidePointHeight": 1,
      "indicatorLeft": 187,
      "indicatorTop": 150
  },
  "headerOpts": {
      "isCustomMiniapp": false,
      "fpHeaderBackgroundColor": "rgba(255, 255, 255, 0)",
      "fpFontColor": 1,
      "fpHideTitle": true,
      "isCustomSticky": false,
      "headerBackgroundColor": "#ffffff",
      "fontColor": 1
  },
  "backgroundFillType": "filling",
  "dataSourceAction": {
      "type": "",
      "id": "",
      "url": "",
      "query": "",
      "label": "",
      "openInTopWindow": false,
      "miniprogram": {
          "wechat": {
              "appId": "",
              "originalId": "",
              "path": ""
          },
          "baidu": {
              "appId": "",
              "path": ""
          }
      },
      "content": {

      },
      "customizeLinkParams": [

      ],
      "phone": "",
      "chatTargetUid": "",
      "address": "",
      "location": {

      },
      "email": {
          "title": "下载文件通知",
          "sender": "09d5ecee739a4a44839571ea0e63bf98",
          "applyUser": "6873af2524f04308b7d7355672637886",
          "html": "<div id=\"yxt_tinymce_body_class\"style=\"font-size: 16px; line-height: 1.4; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\"><p style=\"text-align: center;\">您的文件已下发，请点击下方文件名称下载文件</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.name}</a></p><p style=\"text-align: center;\">若无法点击文件名下载，您也可以复制以下链接到浏览器打开下载</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.url}</a></p></div>"
      },
      "emailAttach": {

      },
      "ctaConfig": {

      },
      "extendParams": {

      },
      "functionApiName": "",
      "functionName": ""
  },
  "components": [
      {
          "id": 1638784345222,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "id": 1638784412448,
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 23px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">脑重大疾病精准医学蛋白质组</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 141,
                      "width": 345,
                      "position": "absolute"
                  },
                  "sort": 0,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": 1638784412448,
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"line-height: 24px; font-size: 44px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif; color: rgb(255, 255, 255);\">高端研讨会</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 96,
                      "width": 345,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 1,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  },
                  "action": {
                      "type": "",
                      "id": "",
                      "url": "",
                      "query": "",
                      "label": "",
                      "openInTopWindow": false,
                      "miniprogram": {
                          "wechat": {
                              "appId": "",
                              "originalId": "",
                              "path": ""
                          },
                          "baidu": {
                              "appId": "",
                              "path": ""
                          }
                      },
                      "content": {

                      },
                      "customizeLinkParams": [

                      ],
                      "phone": "",
                      "chatTargetUid": "",
                      "address": "",
                      "location": {

                      },
                      "email": {
                          "title": "下载文件通知",
                          "sender": "09d5ecee739a4a44839571ea0e63bf98",
                          "applyUser": "6873af2524f04308b7d7355672637886",
                          "html": "<div id=\"yxt_tinymce_body_class\"style=\"font-size: 16px; line-height: 1.4; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\"><p style=\"text-align: center;\">您的文件已下发，请点击下方文件名称下载文件</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.name}</a></p><p style=\"text-align: center;\">若无法点击文件名下载，您也可以复制以下链接到浏览器打开下载</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.url}</a></p></div>"
                      },
                      "emailAttach": {

                      },
                      "ctaConfig": {

                      },
                      "extendParams": {

                      }
                  }
              },
              {
                  "id": "1638777449059",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 14px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">会议时间：2021年7月18日</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 176,
                      "width": 345,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_4d003939a6e94a42b669bc10616c6775.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 99,
                      "height": 20.064,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 25,
                      "top": 29,
                      "position": "absolute"
                  },
                  "sort": 3,
                  "id": 1638784517507,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "image",
          "fillMethod": "stretch",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 430,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "",
              "backgroundImage": "url(https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_f73e7a1207a543e5860261875628de77.png&ea=fs)",
              "backgroundSize": "100% 100%",
              "backgroundRepeat": "no-repeat"
          },
          "sort": "0",
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          },
          "action": {
              "type": "",
              "id": "",
              "url": "",
              "query": "",
              "label": "",
              "openInTopWindow": false,
              "miniprogram": {
                  "wechat": {
                      "appId": "",
                      "originalId": "",
                      "path": ""
                  },
                  "baidu": {
                      "appId": "",
                      "path": ""
                  }
              },
              "content": {

              },
              "customizeLinkParams": [

              ],
              "phone": "",
              "chatTargetUid": "",
              "address": "",
              "location": {

              },
              "email": {
                  "title": "下载文件通知",
                  "sender": "09d5ecee739a4a44839571ea0e63bf98",
                  "applyUser": "6873af2524f04308b7d7355672637886",
                  "html": "<div id=\"yxt_tinymce_body_class\"style=\"font-size: 16px; line-height: 1.4; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\"><p style=\"text-align: center;\">您的文件已下发，请点击下方文件名称下载文件</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.name}</a></p><p style=\"text-align: center;\">若无法点击文件名下载，您也可以复制以下链接到浏览器打开下载</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.url}</a></p></div>"
              },
              "emailAttach": {

              },
              "ctaConfig": {

              },
              "extendParams": {

              }
          }
      },
      {
          "id": 1638861556826,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 318,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 1,
                      "top": 95,
                      "position": "absolute"
                  },
                  "sort": 0,
                  "id": 1638784674592,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 40,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 1,
                      "top": 67,
                      "position": "absolute"
                  },
                  "sort": 1,
                  "id": "1638777449060",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": 1638784785907,
                  "name": "文本",
                  "type": "text",
                  "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">会议邀请脑重大疾病精准医学领域著名专家学者和企业研发领袖，围绕脑损伤、脑肿瘤、脑血管病、神经退行性疾病相关重大疾病的发病机制、精准诊断和个体化治疗等做专题演讲和讨论，旨在提速神经科学、脑重大疾病研究与蛋白质组学的交叉融合，增进蛋白组学在基础研究、临床应用方面的交流与合作，共同推动我国脑重大疾病精准医学的长远发展。</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 32,
                      "top": 95,
                      "width": 313,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 55,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 1,
                      "top": 390,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 3,
                  "id": "1638777449061",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_99b4144d7a6443bfa9fa2cb2c494caf3.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 311,
                      "height": 129,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 10,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 32,
                      "top": 281,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 4,
                  "id": 1638784853182,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": 1639018949577,
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"font-size: 54px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">01</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 19,
                      "top": 25,
                      "width": 69,
                      "position": "absolute"
                  },
                  "sort": 5,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876518",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"font-size: 28px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">会议主题</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 88,
                      "top": 29,
                      "width": 124,
                      "position": "absolute"
                  },
                  "sort": 6,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 452,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "rgba(128, 201, 245, 1)",
              "backgroundImage": ""
          },
          "sort": "1",
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1638844635548,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 55,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 415,
                      "position": "absolute"
                  },
                  "sort": 0,
                  "id": "1638947876520",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 343,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 104,
                      "position": "absolute"
                  },
                  "sort": 1,
                  "id": 1638784674592,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 40,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 68,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "id": "1638777449060",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 55,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 117,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 3,
                  "id": "1638777449061",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 83,
                      "height": 99,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 33,
                      "top": 105,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 4,
                  "id": 1638784853182,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449064",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><span style=\"font-size: 14px;\">北京大学附属医院副教授</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 190,
                      "top": 107,
                      "width": 156,
                      "position": "absolute"
                  },
                  "sort": 5,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449065",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">妇产科副主任医师,2002年毕业以来，一直从事妇产科的临床工作。擅长治疗产科合并症，并发症。</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 131,
                      "top": 134,
                      "width": 220,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 6,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449066",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"color: rgb(24, 28, 37); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 126,
                      "top": 217,
                      "width": 56,
                      "position": "absolute"
                  },
                  "sort": 7,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 83,
                      "height": 99,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 32,
                      "top": 220,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 8,
                  "id": "1638777449067",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449068",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><span style=\"font-size: 14px;\">北京大学附属医院副教授</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 190,
                      "top": 219,
                      "width": 156,
                      "position": "absolute"
                  },
                  "sort": 9,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449069",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">妇产科副主任医师,2002年毕业以来，一直从事妇产科的临床工作。擅长治疗产科合并症，并发症。</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 130,
                      "top": 249,
                      "width": 220,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 10,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449070",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"color: rgb(24, 28, 37); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 126,
                      "top": 332,
                      "width": 56,
                      "position": "absolute"
                  },
                  "sort": 11,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 83,
                      "height": 99,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 33,
                      "top": 335,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 12,
                  "id": "1638777449071",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449072",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><span style=\"font-size: 14px;\">北京大学附属医院副教授</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 190,
                      "top": 334,
                      "width": 156,
                      "position": "absolute"
                  },
                  "sort": 13,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449073",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">妇产科副主任医师,2002年毕业以来，一直从事妇产科的临床工作。擅长治疗产科合并症，并发症。</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 126,
                      "top": 359,
                      "width": 220,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 14,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876519",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 28px; letter-spacing: 0px;\">特邀嘉宾</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 97,
                      "top": 29,
                      "width": 259,
                      "position": "absolute"
                  },
                  "sort": 15,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": 1638784785907,
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"color: rgb(24, 28, 37); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 126,
                      "top": 104,
                      "width": 56,
                      "position": "absolute"
                  },
                  "sort": 16,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": 1639019138519,
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 54px; letter-spacing: 0px;\">02</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 22,
                      "top": 24,
                      "width": 65,
                      "position": "absolute"
                  },
                  "sort": 17,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 476,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "rgba(128, 201, 245, 1)",
              "backgroundImage": ""
          },
          "sort": "2",
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1638844633252,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 534,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 96,
                      "position": "absolute"
                  },
                  "sort": 0,
                  "id": 1638784674592,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 55,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 604,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 1,
                  "id": "1638777449061",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 40,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 63,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "id": 1638844744198,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "文本",
                  "type": "text",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 130,
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 96,
                      "top": 22,
                      "position": "absolute",
                      "fontSize": 14
                  },
                  "sort": 3,
                  "id": "1638777449074",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  },
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 28px; font-family: Helvetica, Arial, sans-serif;\">会议流程</strong></p>"
              },
              {
                  "id": 1638861613963,
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">14:00-14:10</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 147,
                      "top": 240,
                      "width": 78,
                      "position": "absolute"
                  },
                  "sort": 4,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449972",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; color: rgb(24, 28, 37); line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">会议致辞</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 147,
                      "top": 267,
                      "width": 78,
                      "position": "absolute"
                  },
                  "sort": 5,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449977",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">14:00-14:10</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 147,
                      "top": 307,
                      "width": 78,
                      "position": "absolute"
                  },
                  "sort": 6,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449978",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; color: rgb(24, 28, 37); line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">基于微流控液滴技术的单细胞分析</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 83,
                      "top": 334,
                      "width": 210,
                      "position": "absolute"
                  },
                  "sort": 7,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449980",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 83,
                      "top": 359,
                      "width": 210,
                      "position": "absolute"
                  },
                  "sort": 8,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449994",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">14:00-14:10</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 150,
                      "top": 566,
                      "width": 78,
                      "position": "absolute"
                  },
                  "sort": 9,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638777449995",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; color: rgb(24, 28, 37); line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">抽奖、自由交流环节</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 81,
                      "top": 594,
                      "width": 210,
                      "position": "absolute"
                  },
                  "sort": 10,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "文本",
                  "type": "text",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 68,
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 22,
                      "position": "absolute",
                      "fontSize": 14
                  },
                  "sort": 11,
                  "id": "1638947876521",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  },
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 58px; font-family: Helvetica, Arial, sans-serif;\">03</strong></p>"
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_76e043170b0d41649b4cdd244774d1ae.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 316,
                      "height": 131.456,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 28,
                      "top": 96,
                      "position": "absolute"
                  },
                  "sort": 12,
                  "id": 1639019759924,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876522",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">14:00-14:10</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 147,
                      "top": 397,
                      "width": 78,
                      "position": "absolute"
                  },
                  "sort": 13,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876523",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; color: rgb(24, 28, 37); line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">基于微流控液滴技术的单细胞分析</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 83,
                      "top": 424,
                      "width": 210,
                      "position": "absolute"
                  },
                  "sort": 14,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876524",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 81,
                      "top": 450,
                      "width": 210,
                      "position": "absolute"
                  },
                  "sort": 15,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876525",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">14:00-14:10</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 147,
                      "top": 482,
                      "width": 78,
                      "position": "absolute"
                  },
                  "sort": 16,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876526",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; color: rgb(24, 28, 37); line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">基于微流控液滴技术的单细胞分析</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 82,
                      "top": 506,
                      "width": 210,
                      "position": "absolute"
                  },
                  "sort": 17,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876527",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 81,
                      "top": 530,
                      "width": 210,
                      "position": "absolute"
                  },
                  "sort": 18,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 662,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "rgba(128, 201, 245, 1)",
              "backgroundImage": ""
          },
          "sort": "3",
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1638861562161,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 321,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 91,
                      "position": "absolute"
                  },
                  "sort": 0,
                  "id": 1638784674592,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 55,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 407,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 1,
                  "id": "1638777449061",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 375,
                      "height": 40,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 63,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "id": 1638844744198,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "文本",
                  "type": "text",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 113,
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 96,
                      "top": 26,
                      "position": "absolute",
                      "fontSize": 14
                  },
                  "sort": 3,
                  "id": "1638777449074",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  },
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 28px; font-family: Helvetica, Arial, sans-serif;\">会议概要</strong></p>"
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_fddde12686f04169ae742a9bc3587c05.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 317,
                      "height": 155,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 30,
                      "top": 274,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 4,
                  "id": 1638847082311,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "文本",
                  "type": "text",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 113,
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 18,
                      "top": 21,
                      "position": "absolute",
                      "fontSize": 14
                  },
                  "sort": 6,
                  "id": "1638947876528",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  },
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 58px; font-family: Helvetica, Arial, sans-serif;\">04</strong></p>"
              },
              {
                  "id": "1638947876529",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 0px; font-family: Helvetica, Arial, sans-serif; font-size: 13px;\">会议时间</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 63,
                      "top": 86,
                      "width": 249,
                      "position": "absolute"
                  },
                  "sort": 6,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876530",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 0px; font-family: Helvetica, Arial, sans-serif; font-size: 14px;\">2021年7月18日 14:00-17:30</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 34,
                      "top": 111,
                      "width": 307,
                      "position": "absolute"
                  },
                  "sort": 7,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876531",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 0px; font-family: Helvetica, Arial, sans-serif; font-size: 13px;\">会议地址</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 63,
                      "top": 145,
                      "width": 249,
                      "position": "absolute"
                  },
                  "sort": 8,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876532",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(51, 51, 51); font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">广东省深圳大冲国际中心22楼</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 34,
                      "top": 171,
                      "width": 307,
                      "position": "absolute"
                  },
                  "sort": 9,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876533",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 0px; font-family: Helvetica, Arial, sans-serif; font-size: 13px;\">会议地址</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 64,
                      "top": 206,
                      "width": 249,
                      "position": "absolute"
                  },
                  "sort": 10,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1638947876534",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(51, 51, 51); font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">0755-23456543</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 34,
                      "top": 228,
                      "width": 307,
                      "position": "absolute"
                  },
                  "sort": 11,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 466,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "rgba(128, 201, 245, 1)",
              "backgroundImage": ""
          },
          "sort": "4",
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1638847043047,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 409,
                      "height": 42.45045751633987,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": -17,
                      "top": 15,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "id": 1639020429664,
                  "sort": 0,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 409,
                      "height": 36.088235294117645,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": -17,
                      "top": 57,
                      "position": "absolute"
                  },
                  "sort": 1,
                  "id": "1638947876535",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": 1639020633335,
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 24px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(128, 201, 245); letter-spacing: 0px;\">提交以下信息马上报名</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 47,
                      "width": 345,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 90,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "rgba(128, 201, 245, 1)",
              "backgroundImage": ""
          },
          "sort": "5",
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1638847361071,
          "name": "表单",
          "key": "form-container",
          "type": "container",
          "typeValue": "form",
          "components": [
              {
                  "id": "6e5c70b3e93885ba",
                  "label": "姓名",
                  "name": "姓名",
                  "title": "姓名",
                  "type": "input",
                  "typeValue": "text",
                  "fieldName": "name",
                  "customFieldName": "name",
                  "defaultValueOpen": false,
                  "defaultValue": "",
                  "globalCacheField": "",
                  "defaultValueType": "manual",
                  "required": true,
                  "placeholder": "请输入姓名",
                  "isFormComp": true,
                  "style": {
                      "color": "#181C25",
                      "width": 345,
                      "fontSize": 14,
                      "paddingBottom": 0,
                      "paddingTop": 0,
                      "paddingLeft": 12,
                      "paddingRight": 12,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5",
                      "marginLeft": 15,
                      "marginRight": 15,
                      "marginTop": 15,
                      "position": "relative"
                  },
                  "titleStyle": {
                      "color": "#181C25",
                      "fontSize": 14,
                      "lineHeight": 16,
                      "paddingBottom": 6,
                      "paddingTop": 6,
                      "whiteSpace": "normal"
                  },
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "height": 45,
                      "color": "#181c25",
                      "background": "#fff"
                  },
                  "sort": "0"
              },
              {
                  "id": "3307488d1a92f241",
                  "label": "手机号",
                  "name": "手机号",
                  "title": "手机号",
                  "type": "input",
                  "typeValue": "number",
                  "fieldName": "phone",
                  "customFieldName": "phone",
                  "pattern": "^1[0-9]\\d{9}$",
                  "defaultValue": "",
                  "defaultValueOpen": false,
                  "globalCacheField": "",
                  "defaultValueType": "manual",
                  "required": true,
                  "verify": false,
                  "enableInternationalCode": false,
                  "weChatAuthorizationButton": false,
                  "placeholder": "请输入手机号",
                  "isFormComp": true,
                  "weChatAuthorizationButtonStyle": {
                      "color": "#fff",
                      "background": "#09BB07",
                      "fontSize": 14,
                      "borderStyle": "solid",
                      "borderWidth": 0,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5"
                  },
                  "verifyButtonStyle": {
                      "color": "#181C25",
                      "background": "#ffffff",
                      "fontSize": 14,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5"
                  },
                  "titleStyle": {
                      "color": "#181C25",
                      "fontSize": 14,
                      "lineHeight": 16,
                      "paddingBottom": 6,
                      "paddingTop": 6,
                      "whiteSpace": "normal"
                  },
                  "style": {
                      "color": "#181C25",
                      "width": 345,
                      "fontSize": 14,
                      "paddingBottom": 0,
                      "paddingTop": 0,
                      "paddingLeft": 12,
                      "paddingRight": 12,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5",
                      "marginLeft": 15,
                      "marginRight": 15,
                      "marginTop": 15,
                      "position": "relative"
                  },
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "height": 45,
                      "color": "#181c25",
                      "background": "#fff"
                  },
                  "sort": "1"
              },
              {
                  "id": "a34b6e43dcbf382d",
                  "label": "邮箱",
                  "name": "邮箱",
                  "title": "邮箱",
                  "type": "input",
                  "typeValue": "text",
                  "fieldName": "email",
                  "customFieldName": "email",
                  "pattern": "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$",
                  "defaultValue": "",
                  "defaultValueOpen": false,
                  "globalCacheField": "",
                  "defaultValueType": "manual",
                  "required": true,
                  "placeholder": "请输入邮箱",
                  "isFormComp": true,
                  "titleStyle": {
                      "color": "#181C25",
                      "fontSize": 14,
                      "lineHeight": 16,
                      "paddingBottom": 6,
                      "paddingTop": 6,
                      "whiteSpace": "normal"
                  },
                  "style": {
                      "color": "#181C25",
                      "width": 345,
                      "fontSize": 14,
                      "paddingBottom": 0,
                      "paddingTop": 0,
                      "paddingLeft": 12,
                      "paddingRight": 12,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5",
                      "marginLeft": 15,
                      "marginTop": 15,
                      "position": "relative"
                  },
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "height": 45,
                      "color": "#181c25",
                      "background": "#fff"
                  },
                  "sort": "2"
              },
              {
                  "id": "bfa671e56aa20b47",
                  "label": "提交",
                  "name": "提交",
                  "tip": "提交成功",
                  "type": "button",
                  "position": "none",
                  "required": false,
                  "isFormComp": true,
                  "noDeletion": true,
                  "wrapStyle": {
                      "position": "none"
                  },
                  "style": {
                      "height": 45,
                      "width": 345,
                      "fontSize": 16,
                      "background": "#409EFF",
                      "borderRadius": 0,
                      "color": "#fff",
                      "letterSpacing": 0,
                      "lineHeight": 45,
                      "textAlign": "center",
                      "margin": "0 auto",
                      "boxShadow": "0px 0px 0px rgba(0,0,0,.1)",
                      "boxShadowLeft": 0,
                      "boxShadowTop": 0,
                      "boxShadowRadius": 0,
                      "boxShadowColor": "rgba(0,0,0,.1)",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "marginLeft": 15,
                      "marginRight": 15,
                      "marginBottom": 15,
                      "marginTop": 15
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "flow",
          "style": {
              "width": 375,
              "height": "auto",
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "",
              "backgroundImage": ""
          },
          "sort": 6,
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      }
  ],
  "language": "zh-CN"
}