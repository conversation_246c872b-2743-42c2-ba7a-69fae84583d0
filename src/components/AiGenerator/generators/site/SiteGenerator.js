import BaseGenerator from '../../core/BaseGenerator';
import { generateUUID } from '../../utils/uuid';
import { genPageSystemPrompt, editPageSystemPrompt } from './systemPrompt';
/**
 * 微页面生成器类
 * 处理微页面相关的AI生成逻辑
 */
export default class SiteGenerator extends BaseGenerator {
  constructor() {
    super();
    this.templates = {
      form: {
        type: 'form',
        fields: []
      },
      landing: {
        type: 'landing',
        sections: []
      },
      display: {
        type: 'display',
        content: {}
      }
    };
  }

  /**
   * 生成系统提示词
   * @param {Object} context - 生成上下文
   * @returns {string} 系统提示词
   */
  generateSystemPrompt(context) {
    const { existingData, systemPrompt } = context;
    const prompt = existingData ? editPageSystemPrompt.replace('{{pageData}}', JSON.stringify(existingData)) : genPageSystemPrompt;
    return `${prompt}\n\n${systemPrompt}`;
  }

  /**
   * 生成用户提示词
   * @param {string} prompt - 用户输入的提示词
   * @param {Object} context - 生成上下文
   * @returns {string} 用户提示词
   */
  generateUserPrompt(prompt, context) {
    const { existingData } = context;
    return `请根据以下需求${existingData ? '修改当前页面并保持组件ID不变' : '生成一个微页面'}：${prompt}`;
  }

  /**
   * 验证生成的数据
   * @param {Object} data - 生成的数据
   * @returns {boolean} 验证结果
   */
  validateGeneratedData(data) {
    // 仅保留最基本的数据结构检查
    if (!data || typeof data !== 'object') {
      throw new Error('页面数据必须是一个对象');
    }

    if (!Array.isArray(data.components)) {
      throw new Error('components 必须是数组');
    }

    // 确保基本字段存在
    if (!data.id || !data.type || !data.name) {
      throw new Error('缺少基本字段：id、type、name');
    }

    return true;
  }

  /**
   * 修复生成的数据
   * @param {Object} data - 需要修复的数据
   * @param {boolean} deep - 是否深度修复
   * @returns {Object} 修复后的数据
   */
  repairGeneratedData(data, deep = false) {
    // 创建默认的页面数据结构
    const defaultPageData = {
      placeholderStyle: {
        color: "#cbcccf"
      },
      inputStyle: {
        color: "#181c25"
      },
      style: {
        width: 375,
        backgroundColor: "#f9f9f9",
        backgroundSize: "100%",
        backgroundRepeat: "no-repeat",
        backgroundImage: ""
      },
      id: data.id || generateUUID(),
      type: "page",
      name: data.name || "微页面",
      title: data.title || "",
      version: "1.0.0",
      cover: "",
      pcAdaptation: false,
      isPdf: false,
      shareOpts: {
        title: data.name || "微页面",
        desc: "",
        link: "",
        imgUrl: "",
        sharePosterUrl: "",
        sharePosterAPath: ""
      },
      popupOpts: {
        isPreview: false,
        fillType: 1,
        isShow: false,
        backgroundStyle: {
          objectFit: "cover",
          width: "100%",
          height: "auto"
        },
        width: 280,
        height: 490,
        borderRadius: 16,
        positionY: 50,
        show: 0,
        coverUrl: "",
        mode: 1,
        action: {}
      },
      backgroundOpts: {
        mode: 1,
        bgColor: "#f9f9f9",
        bgImage: "",
        fillType: "horizontal-filling",
        carouselImgs: [],
        carouselPointType: 5,
        carouselPointBottom: 24,
        carouselPointIsShow: false,
        carouselPointAutoPlay: true,
        playDuration: 200,
        carouselPointColor: "rgba(255, 255, 255, 0.5)",
        carouselPointActiveColor: "#fff",
        carouselPointActiveIndex: 0,
        carouselPlayInterval: 3000,
        circlePointSize: 6,
        circlePointMargin: 5,
        dashPointWidth: 12,
        dashPointHeight: 2,
        dashPointMargin: 4,
        slidePointWidth: 180,
        slidePointHeight: 1,
        indicatorLeft: 187,
        indicatorTop: 150
      },
      headerOpts: {
        isCustomMiniapp: false,
        fpHeaderBackgroundColor: "rgba(255, 255, 255, 0)",
        fpFontColor: 1,
        fpHideTitle: true,
        isCustomSticky: false,
        headerBackgroundColor: "#ffffff",
        fontColor: 1
      },
      backgroundFillType: "horizontal-filling",
      dataSourceAction: {},
      components: []
    };

    // 深度合并数据
    const mergedData = this.deepMerge(defaultPageData, data);

    // 如果需要进行深度修复
    if (deep) {
      // 修复组件数据
      if (Array.isArray(mergedData.components)) {
        mergedData.components = mergedData.components.map(component => this.repairComponent(component, deep));
      }
    }

    return mergedData;
  }

  /**
   * 修复组件数据
   * @param {Object} component - 组件数据
   * @param {boolean} deep - 是否深度修复
   * @returns {Object} 修复后的组件数据
   */
  repairComponent(component, deep = false) {
    // 创建默认的组件数据结构
    const defaultComponent = {
      id: component.id || generateUUID(),
      name: component.name || "组件",
      type: component.type || "text",
      style: {
        width: 375,
        padding: "12px",
        margin: "15px",
        position: "relative"
      }
    };

    // 根据组件类型添加特定属性
    switch (component.type) {
      case 'input':
        Object.assign(defaultComponent, {
          typeValue: "text",
          fieldName: component.fieldName || generateUUID(),
          customFieldName: component.customFieldName || generateUUID(),
          required: true,
          placeholder: component.placeholder || "请输入",
          defaultValue: "",
          defaultValueOpen: false,
          globalCacheField: "",
          defaultValueType: "manual",
          style: {
            color: "#181C25",
            width: 345,
            fontSize: 14,
            paddingBottom: 12,
            paddingTop: 12,
            paddingLeft: 12,
            paddingRight: 12,
            borderStyle: "solid",
            borderWidth: 1,
            borderRadius: 3,
            borderColor: "#e9edf5",
            marginLeft: 15,
            marginRight: 15,
            marginTop: 15,
            position: "relative"
          },
          titleStyle: {
            color: "#181C25",
            fontSize: 14,
            lineHeight: 16,
            paddingBottom: 6,
            paddingTop: 6,
            whiteSpace: "normal"
          },
          placeholderStyle: {
            color: "#cbcccf"
          },
          inputStyle: {
            height: 45,
            color: "#181c25",
            background: "#fff"
          }
        });
        break;

      case 'container':
        Object.assign(defaultComponent, {
          typeValue: "form",
          components: [],
          current: 0,
          slideIndex: 0,
          layout: "single",
          fillType: "color",
          fillMethod: "filling",
          typesetting: "flow",
          visualStyle: {
            overflowX: "hidden",
            overflowY: "auto",
            height: 30
          },
          style: {
            width: 375,
            overflow: "hidden",
            position: "relative",
            backgroundColor: "",
            backgroundImage: ""
          },
          formLayout: "default"
        });
        break;
    }

    // 深度合并组件数据
    const mergedComponent = this.deepMerge(defaultComponent, component);

    // 如果需要进行深度修复且是容器组件
    if (deep && mergedComponent.type === 'container' && Array.isArray(mergedComponent.components)) {
      mergedComponent.components = mergedComponent.components.map(child => this.repairComponent(child, deep));
    }

    return mergedComponent;
  }

  /**
   * 深度合并对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @returns {Object} 合并后的对象
   */
  deepMerge(target, source) {
    const output = Object.assign({}, target);
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach(key => {
        if (this.isObject(source[key])) {
          if (!(key in target)) {
            Object.assign(output, { [key]: source[key] });
          } else {
            output[key] = this.deepMerge(target[key], source[key]);
          }
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    return output;
  }

  /**
   * 判断是否为对象
   * @param {*} item - 待判断项
   * @returns {boolean} 是否为对象
   */
  isObject(item) {
    return (item && typeof item === 'object' && !Array.isArray(item));
  }

  /**
   * 验证生成的数据是否有效
   * @param {Object} data - 页面数据
   * @returns {boolean} 是否有效
   */
  validate(data) {
    if (!data || !data.type) {
      return false;
    }

    const template = this.templates[data.type];
    if (!template) {
      return false;
    }

    // TODO: 添加更多验证逻辑

    return true;
  }
} 