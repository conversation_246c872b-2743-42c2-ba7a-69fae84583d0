import { AI_CONFIG } from '../config';

/**
 * 基础AI生成器类
 * 提供通用的AI生成功能和数据处理方法
 */
export default class BaseGenerator {
  constructor(options = {}) {
    this.apiEndpoint = options.apiEndpoint || AI_CONFIG.api.endpoint;
    this.model = options.model || AI_CONFIG.api.defaultModel;
    this.temperature = options.temperature || AI_CONFIG.api.temperature;
    this.maxTokens = options.maxTokens || AI_CONFIG.api.maxTokens;
    this.onStreamOutput = options.onStreamOutput || null;
    this.chatComponent = options.chatComponent || null; // 添加对聊天组件的引用
  }

  /**
   * 生成系统提示词
   * @param {Object} context - 生成上下文
   * @returns {string} 系统提示词
   */
  generateSystemPrompt(context) {
    throw new Error('generateSystemPrompt must be implemented by subclass');
  }

  /**
   * 生成用户提示词
   * @param {string} prompt - 用户输入的提示词
   * @param {Object} context - 生成上下文
   * @returns {string} 用户提示词
   */
  generateUserPrompt(prompt, context) {
    throw new Error('generateUserPrompt must be implemented by subclass');
  }

  /**
   * 验证生成的数据
   * @param {Object} data - 生成的数据
   * @returns {boolean} 验证结果
   */
  validateGeneratedData(data) {
    throw new Error('validateGeneratedData must be implemented by subclass');
  }

  /**
   * 修复生成的数据
   * @param {Object} data - 需要修复的数据
   * @param {boolean} deep - 是否深度修复
   * @returns {Object} 修复后的数据
   */
  repairGeneratedData(data, deep = false) {
    throw new Error('repairGeneratedData must be implemented by subclass');
  }

  /**
   * 调用AI接口生成内容
   * @param {string} prompt - 用户输入的提示词
   * @param {Object} context - 生成上下文
   * @returns {Promise<Object>} 生成的内容
   */
  async generate(prompt, context = {}) {
    const systemPrompt = this.generateSystemPrompt(context);
    const userPrompt = this.generateUserPrompt(prompt, context);

    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.api.key}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: this.temperature,
          max_tokens: this.maxTokens,
          stream: true
        })
      });

      if (!response.ok) {
        // 更详细的错误处理
        const errorBody = await response.text();
        console.error('API Error Response:', errorBody);
        throw new Error(`API 调用失败: ${response.status} ${response.statusText}`);
      }

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let streamBuffer = ''; // 用于累积解码后的数据块
      let fullContent = ''; // 用于存储完整的响应内容

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        streamBuffer += decoder.decode(value, { stream: true }); // 解码并累积

        // 查找最后一个换行符，处理所有完整的行
        let lastNewlineIndex = streamBuffer.lastIndexOf('\n');
        if (lastNewlineIndex !== -1) {
          const linesToProcess = streamBuffer.substring(0, lastNewlineIndex);
          streamBuffer = streamBuffer.substring(lastNewlineIndex + 1); // 保留不完整的行部分

          const lines = linesToProcess.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonData = line.slice(6).trim(); // 去除可能的空白字符
              if (jsonData === '[DONE]') continue;
              if (!jsonData) continue; // 跳过空的 data 行

              try {
                // console.log('完整行 jsonData：', jsonData); // 现在这里应该是完整的 JSON
                const parsedData = JSON.parse(jsonData);
                const content = parsedData.choices[0]?.delta?.content || '';

                // 发送文本进行打字效果显示
                if (content && context.chatComponent) {
                  context.chatComponent.handleStreamContent(content);
                }

                // 累积完整内容
                fullContent += content;
              } catch (e) {
                console.warn('解析流数据中的 JSON 出错:', e, '原始数据:', jsonData);
                // 即使某行解析失败，也应继续处理流中的其他行
              }
            }
          }
        }
      }

      // 处理可能残留在缓冲区中的最后一部分（如果流结束时没有换行符）
      if (streamBuffer.startsWith('data: ')) {
          const jsonData = streamBuffer.slice(6).trim();
          if (jsonData && jsonData !== '[DONE]') {
              try {
                  const parsedData = JSON.parse(jsonData);
                  const content = parsedData.choices[0]?.delta?.content || '';
                  if (content && context.chatComponent) {
                    context.chatComponent.handleStreamContent(content);
                  }
                  fullContent += content;
              } catch (e) {
                 console.warn('解析流末尾数据出错:', e, '原始数据:', jsonData);
              }
          }
      }


      // 在接收完所有数据后，尝试解析完整的 JSON (这部分逻辑是之前您同意修改的，保持不变)
      try {
        const startIndex = fullContent.indexOf('{');
        const endIndex = fullContent.lastIndexOf('}') + 1;

        if (startIndex !== -1 && endIndex > startIndex) {
          const jsonContent = fullContent.substring(startIndex, endIndex);
          return JSON.parse(jsonContent);
        }
      } catch (e) {
        console.error('最终 JSON 解析失败:', e, '完整内容:', fullContent);
        throw new Error('生成的数据格式不正确');
      }

      // 如果 fullContent 中没有有效的 JSON 结构
      console.warn('未在流中找到有效的 JSON 结构, fullContent:', fullContent);
      throw new Error('未找到有效的JSON数据');

    } catch (error) {
      console.error('生成失败:', error);
      throw new Error(`生成失败: ${error.message}`);
    }
  }
} 