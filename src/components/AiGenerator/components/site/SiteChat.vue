<!-- 网站生成聊天组件 -->
<template>
  <div :class="$style.site_chat">
    <base-chat ref="baseChat" default-helper-name="Copilot_hexagon_ai__c" @onReady="handleShareGPTReady" @onAgentMessage="handleAgentMessage" />

  </div>
</template>

<script>
import BaseChat from '../base/BaseChat.vue';
import http from "@/services/http/";

export default {
  name: 'SiteChat',
  components: {
    BaseChat,
  },
  props: {
    sessionId: {
      type: String,
      default: ''
    },
    existingData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      marketingImages: [],
      activityInfo: {}
    };
  },
  watch: {
    marketingImages: {
      handler() {
        this.setVariables();
      },
      deep: true
    },
    activityInfo: {
      handler() {
        this.setVariables();
      },
      deep: true
    },
    existingData: {
      handler() {
        this.setVariables();
      },
      deep: true
    }
  },
  mounted() {
    this.getMarketingImages();
    this.getActivityInfo();
  },
  methods: {
    handleShareGPTReady(marktingAIHelper) {
      marktingAIHelper.sendConfigData({
        bizSessionId: this.sessionId,
        enableAgentMessageListener: true
      })
      this.marktingAIHelper = marktingAIHelper

      this.setVariables();
    },
    handleAgentMessage(data) {
      console.log('handleAgentMessage', data);
      try {
        //移除\n、\t
        const content = data.message.content.replace(/\n/g, '').replace(/\t/g, '');
        const pageData = JSON.parse(content);
        console.log('pageData', pageData);
        // 触发生成完成事件
        this.$emit('generated', {
          pageData: pageData,
          existingData: this.existingData
        });
      } catch (error) {
        console.error('handleAgentMessage error', error);
      }
    },
    setVariables() {
      if(this.marktingAIHelper)
      this.marktingAIHelper.sendConfigData({
        variables: [
          {
            name: 'activityInfo',
            value: JSON.stringify(this.activityInfo)
          },
          {
            name: 'photos',
            value: JSON.stringify(this.marketingImages)
          },
          {
            name: 'pageData',
            value: JSON.stringify(this.existingData)
          },
          {
            name: 'brandColor',
            value: this.getBrandColors()
          },
        ]
      })
    },
    getBrandColors() {
      //获取公司品牌色
      const style = $('html').attr('style');
      const brandColors = {
        primary01: style.match(/--color-primary01: (#[0-9a-fA-F]{6});/)?.[1] || '#fffaf0',
        primary02: style.match(/--color-primary02: (#[0-9a-fA-F]{6});/)?.[1] || '#ffeecc',
        primary03: style.match(/--color-primary03: (#[0-9a-fA-F]{6});/)?.[1] || '#ffdda3',
        primary04: style.match(/--color-primary04: (#[0-9a-fA-F]{6});/)?.[1] || '#ffca7a',
        primary05: style.match(/--color-primary05: (#[0-9a-fA-F]{6});/)?.[1] || '#ffb452',
        primary06: style.match(/--color-primary06: (#[0-9a-fA-F]{6});/)?.[1] || '#ff9b29',
        primary07: style.match(/--color-primary07: (#[0-9a-fA-F]{6});/)?.[1] || '#d97818',
        primary08: style.match(/--color-primary08: (#[0-9a-fA-F]{6});/)?.[1] || '#b3590b',
        primary09: style.match(/--color-primary09: (#[0-9a-fA-F]{6});/)?.[1] || '#8c3e01',
        primary10: style.match(/--color-primary10: (#[0-9a-fA-F]{6});/)?.[1] || '#662900'
      };
      return brandColors['primary06'];
    },
    /**
     * 获取营销图片库最新的50张图片 
     */
    async getMarketingImages() {
      const params = {
        groupId: -1,
        pageNum: 1,
        pageSize: 30,
        time: 0,
      }
      const res = await http.listPhotoByGroup(params);
      if (res.errCode === 0) {
        //将返回图片的地址和关键词存储，再制作页面时添加到系统提示词中
        this.marketingImages = res.data.result.reduce((acc, item) => {
          if (item.photoName) {
            acc.push({
              url: item.cdnUrl || item.thumbnailUrl || item.url || '',
              keyword: item.photoName || ''
            });
          }
          return acc;
        }, []);
      }
    },
    /**
     * 活动下新建微页面场景，将当前活动的有关信息获取
     */
    async getActivityInfo() {
      const { liveId, conferenceId } = this.$route.query;
      if (liveId) {
        http.queryLiveDetail({
          id: liveId
        }).then((res) => {
          if (res && res.errCode === 0 && res.data) {
            const data = res.data;
            this.activityInfo = {
              title: data.title,
              startTime: data.startTime,
              endTime: data.endTime,
              details: data.desc, // 直播描述
              coverImageUrl: data.coverTaPath,
              type: '直播营销',
              viewUrl: data.viewUrl, // 观看链接
              lecturerName: data.lectureUserName, // 讲师名称
              shareImageUrl: data.sharePicOrdinaryUrl, // 分享图片
              marketingEventId: data.marketingEventId, // 营销活动ID
              livePlatform: data.livePlatform, // 直播平台
              otherPlatformLiveUrl: data.otherPlatformLiveUrl // 其他平台直播链接
            };
            // console.log('提取的直播信息:', this.activityInfo);
          }
        });
      } else if (conferenceId) {
        http.queryConferenceDetail({
          id: conferenceId
        }).then((res) => {
          if (res && res.errCode === 0 && res.data) {
            const data = res.data;
            this.activityInfo = {
              title: data.title,
              startTime: data.startTime,
              endTime: data.endTime,
              location: data.location,
              details: data.conferenceDetails, // 使用 details 字段存储 HTML 内容
              coverImageUrl: data.coverImageUrl,
              enrollEndTime: data.enrollEndTime,
              formId: data.formId,
              formName: data.formName,
              type: data.marketingEventDetail?.marketingEventType, // 从嵌套对象获取类型
              qrUrl: data.qrUrl,
              shareImageUrl: data.sharePicOrdinaryUrl // 选择一个默认的分享图
            };
            // console.log('提取的活动信息:', this.activityInfo);
          }
        });
      }
    }
  }
};
</script>

<style lang="less" module>
.site_chat {
  height: 100%;
}

.preview_container {
  margin: 0 auto;
}
</style>