<!-- 网站生成聊天组件 -->
<template>
  <div :class="$style.site_chat">
    <base-chat
      ref="baseChat"
      placeholder="请告诉我您想要生成什么样的页面？"
      :initial-messages="welcomeMessages"
      @send="handleSend"
    />
    
  </div>
</template>

<script>
import BaseChat from '../base/BaseChat.vue';
import PageRender from "@/components/Hexagon/PageRender";
import SiteGenerator from "../../generators/site/SiteGenerator";
import http from "@/services/http/";

export default {
  name: 'SiteChat',
  components: {
    BaseChat,
    PageRender
  },
  props: {
    existingData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      generator: null,
      welcomeMessages: [
        {
          role: 'assistant',
          content: '你好！我是AI页面生成助手。我可以帮你生成各种类型的落地页，包括但不限于：\n\n' +
            '• 表单页面（如：线索收集、调查问卷等）\n' +
            '• 落地页（如：产品介绍、活动宣传等）\n' +
            '• 展示页面（如：团队介绍、公司简介等）\n\n' +
            '请告诉我你想要生成什么样的页面，我会根据你的描述生成相应的页面内容。'
        }
      ],
      marketingImages: [],
      activityInfo: {}
    };
  },
  mounted() {
    this.getMarketingImages();
    this.getActivityInfo();
    // 初始化生成器
    this.generator = new SiteGenerator({
      onStreamOutput: (content) => {
        this.$refs.baseChat.handleStreamContent(content);
      }
    });
  },
  methods: {
    /**
     * 处理消息发送
     */
    async handleSend(message) {
      const chat = this.$refs.baseChat;
      chat.setLoading(true);

      try {
        // 使用生成器生成页面数据
        const pageData = await this.generator.generate(message, {
          existingData: this.existingData,
          chatComponent: chat,
          //追加系统提示词
          systemPrompt: `以下是当前相关活动信息，请在生成页面时参考使用：\n${JSON.stringify(this.activityInfo)}\n\n以下是营销图片库，请在生成页面时参考使用：\n${this.marketingImages.map(item => `图片地址：${item.url}，图片关键词：${item.keyword}`).join('\n')}`
        });
        chat.setLoading(false);
        chat.addAiMessage(
          '我已经根据你的描述生成了页面，你可以在左侧页面区域预览效果。\n\n' +
          '如果需要修改，你可以：\n' +
          '1. 直接告诉我需要修改的部分\n' +
          '2. 提供更详细的描述，我会重新生成'
        );

        // 触发生成完成事件
        this.$emit('generated', {
          prompt: message,
          pageData: pageData,
          existingData: this.existingData
        });
      } catch (error) {
        chat.addAiMessage(
          '抱歉，生成过程中出现了错误：' + error.message + '\n\n' +
          '你可以：\n' +
          '1. 尝试重新描述你的需求\n' +
          '2. 提供更多细节信息'
        );
      } finally {
        chat.setLoading(false);
      }
    },
    /**
     * 获取营销图片库最新的50张图片 
     */
    async getMarketingImages() {
      const params = {
        groupId: -1,
        pageNum: 1,
        pageSize: 30,
        time: 0,
      }
      const res = await http.listPhotoByGroup(params);
      if (res.errCode === 0) {
        //将返回图片的地址和关键词存储，再制作页面时添加到系统提示词中
        this.marketingImages = res.data.result.reduce((acc, item) => {
          if( item.photoName) {
            acc.push({
              url: item.cdnUrl || item.thumbnailUrl || item.url || '',
              keyword: item.photoName || ''
            });
          }
          return acc;
        }, []);
      }
    },
    /**
     * 活动下新建微页面场景，将当前活动的有关信息获取
     */
    async getActivityInfo() {
      const {liveId, conferenceId} = this.$route.query;
      if(liveId) {
        http.queryLiveDetail({
          id: liveId
        }).then((res) => {
          if (res && res.errCode === 0 && res.data) {
            const data = res.data;
            this.activityInfo = {
              title: data.title,
              startTime: data.startTime,
              endTime: data.endTime,
              details: data.desc, // 直播描述
              coverImageUrl: data.coverTaPath,
              type: '直播营销',
              viewUrl: data.viewUrl, // 观看链接
              lecturerName: data.lectureUserName, // 讲师名称
              shareImageUrl: data.sharePicOrdinaryUrl, // 分享图片
              marketingEventId: data.marketingEventId, // 营销活动ID
              livePlatform: data.livePlatform, // 直播平台
              otherPlatformLiveUrl: data.otherPlatformLiveUrl // 其他平台直播链接
            };
            // console.log('提取的直播信息:', this.activityInfo);
          }
        });
      } else if(conferenceId) {
        http.queryConferenceDetail({
          id: conferenceId
        }).then((res) => {
          if (res && res.errCode === 0 && res.data) {
            const data = res.data;
            this.activityInfo = {
              title: data.title,
              startTime: data.startTime,
              endTime: data.endTime,
              location: data.location,
              details: data.conferenceDetails, // 使用 details 字段存储 HTML 内容
              coverImageUrl: data.coverImageUrl,
              enrollEndTime: data.enrollEndTime,
              formId: data.formId,
              formName: data.formName,
              type: data.marketingEventDetail?.marketingEventType, // 从嵌套对象获取类型
              qrUrl: data.qrUrl,
              shareImageUrl: data.sharePicOrdinaryUrl // 选择一个默认的分享图
            };
            // console.log('提取的活动信息:', this.activityInfo);
          }
        });
      }
    }
  }
};
</script>

<style lang="less" module>
.site_chat {
  height: 100%;
}

.preview_container {
  margin: 0 auto;
}
</style> 