<!-- 基础AI聊天组件 -->
<template>
  <div :class="$style.chat_container" ref="chatContainer">
    <ShareGPT ref="ShareGPT" v-bind="$attrs" v-on="$listeners" />
  </div>
</template>

<script>
import ShareGPT from '@/pages/vibe-marketing/components/baseShareGPT.vue';

export default {
  name: 'BaseChat',
  components: {
    ShareGPT
  },
  beforeDestroy() {
  },
};
</script>

<style lang="less" module>
.chat_container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top right;
  overflow: hidden;
  border-left: 1px solid #e8e8e8;
}
</style>