<!-- 基础AI聊天组件 -->
<template>
  <div :class="$style.chat_container" ref="chatContainer">
    <!-- 聊天消息列表 -->
    <div :class="$style.message_list" ref="messageList">
      <div :class="$style.message_list_inner">
        <div v-for="(message, index) in messages" :key="index" :class="[
          $style.message_item,
          message.role === 'user' ? $style.user_message : $style.ai_message
        ]">
          <!-- 头像 -->
          <div :class="$style.avatar">
            <img :src="message.role === 'user' ? userAvatar : aiAvatar" :alt="message.role">
          </div>
          <!-- 消息内容 -->
          <div :class="$style.message_content">
            <div :class="$style.message_text" v-html="formatMessage(message.content)"></div>
          </div>
        </div>
        <!-- 正在输入的消息 -->
        <div v-if="typingMessage" :class="[$style.message_item, $style.ai_message]">
          <div :class="$style.avatar">
            <img :src="aiAvatar" alt="AI">
          </div>
          <div :class="$style.message_content">
            <div :class="$style.message_text" v-html="formatMessage(typingMessage)"></div>
          </div>
        </div>
        <!-- 加载中提示 -->
        <div v-if="loading" :class="$style.loading_message">
          <div :class="$style.avatar">
            <img :src="aiAvatar" alt="AI">
          </div>
          <div :class="$style.loading_dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div :class="$style.input_area">
      <fx-input
        :class="$style.input"
        v-model="inputMessage"
        type="textarea"
        :rows="3"
        :placeholder="placeholder"
        :disabled="loading"
        @keydown.enter.native.prevent="handleEnterPress"
      />
      <fx-button
        size="small"
        type="primary"
        :class="[$style.send_button, inputMessage.trim() ? $style.send_button_active : '']"
        square
        :loading="loading"
        icon="fx-icon-obj-app430"
        @click="handleSend"
      ></fx-button>
    </div>
  </div>
</template>

<script>
const defaultAiAvatar = 'https://a2.fspage.com/FSR/weex/avatar/marketing_app/images/ai-helper-avatar5.svg';
const defaultUserAvatar = FS.contacts.getCurrentEmployee().profileImage;

export default {
  name: 'BaseChat',
  props: {
    // 用户头像
    userAvatar: {
      type: String,
      default: defaultUserAvatar
    },
    // AI头像
    aiAvatar: {
      type: String,
      default: defaultAiAvatar
    },
    // 输入框占位文本
    placeholder: {
      type: String,
      default: '请输入消息...'
    },
    // 初始消息列表
    initialMessages: {
      type: Array,
      default: () => []
    },
    // 打字速度（毫秒/字符）
    typingSpeed: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      messages: [],
      inputMessage: '',
      loading: false,
      typingMessage: '', // 正在输入的消息
      typingBuffer: '', // 打字效果的缓冲区
      typingTimeout: null // 打字效果的定时器
    };
  },
  created() {
    // 初始化消息列表
    this.messages = this.initialMessages.map(msg => ({
      ...msg,
      timestamp: msg.timestamp || Date.now()
    }));
  },
  beforeDestroy() {
    // 清理定时器
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  },
  methods: {
    /**
     * 格式化消息内容
     * 支持换行和链接
     */
    formatMessage(content) {
      if (!content) return '';
      return content
        .replace(/\n/g, '<br>')
        .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
    },
    /**
     * 处理回车按键
     */
    handleEnterPress(e) {
      // Shift + Enter 换行
      if (e.shiftKey) return;
      this.handleSend();
    },
    /**
     * 处理发送消息
     */
    async handleSend() {
      const message = this.inputMessage.trim();
      if (!message || this.loading) return;

      // 添加用户消息
      this.addMessage({
        role: 'user',
        content: message
      });

      // 清空输入框
      this.inputMessage = '';

      // 触发消息发送事件
      this.$emit('send', message);
    },
    /**
     * 添加消息到列表
     */
    addMessage(message) {
      this.messages.push({
        ...message,
        timestamp: Date.now()
      });
      // 使用 nextTick 确保 DOM 更新后再滚动
      this.$nextTick(() => {
        this.scrollToBottom();
        // 添加一个延迟滚动，处理可能的图片加载等异步内容
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      });
    },
    /**
     * 滚动到底部
     */
    scrollToBottom() {
      const messageList = this.$refs.messageList.querySelector('.' + this.$style.message_list_inner);
      if (messageList) {
        messageList.scrollTop = messageList.scrollHeight;
      }
    },
    /**
     * 设置加载状态
     */
    setLoading(status) {
      this.loading = status;
      if (!status) {
        if(this.typingMessage){
          this.addAiMessage(this.typingMessage)
          this.typingMessage = '';
        }
      }
    },
    /**
     * 添加AI回复消息（带打字效果）
     */
    addAiMessage(content) {
        this.addMessage({
          role: 'assistant',
          content: content
        });
    },
    /**
     * 处理流式响应的内容
     */
    handleStreamContent(content) {
      this.typingMessage += content;
      // 使用 nextTick 确保 DOM 更新后再滚动
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
  }
};
</script>

<style lang="less" module>
.chat_container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  background-image: url(https://a2.fspage.com/FSR/weex/avatar/marketing_app/images/chat-bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top right;
  overflow: hidden;
  border-left: 1px solid #e8e8e8;
}

.message_list {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.message_list_inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.message_item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.avatar {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: none;
  }
}

.message_content {
  flex: 1;
  max-width: calc(100% - 64px);
}

.message_text {
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  word-break: break-word;
  line-height: 1.5;
  user-select: text;
  :global {
    a {
      color: #1890ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.message_time {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.user_message {
  flex-direction: row-reverse;

  .avatar {
    margin-right: 0;
    margin-left: 12px;
  }

  .message_text {
    background: #e6f7ff;
  }
}

.loading_message {
  display: flex;
  align-items: flex-start;
  margin-top: 20px;
}

.loading_dots {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  span {
    width: 6px;
    height: 6px;
    margin: 0 2px;
    background: #1890ff;
    border-radius: 50%;
    animation: dot-flashing 1s infinite linear alternate;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

.input_area {
  position: relative; // 改为相对定位
  margin: 0 20px 20px;
  display: flex;
  align-items: flex-start;
  padding: 1px;
  border-top: 1px solid #e8e8e8;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
  border-radius: 8px;
  //毛玻璃背景
  backdrop-filter: blur(10px);
  background: linear-gradient(100deg,#09f 0,#a033ff 50%,#ff5280 75%,#ff7061 100%);

  :global {
    textarea{
      border-radius: 8px;
      min-height: 50px!important;
      &::placeholder {
        color: #666;
      }
    }
  }
}

.send_button {
  margin-left: 12px;
  padding: 0;
  flex-shrink: 0;
  font-size: 20px;
  align-self: end;
  position: absolute;
  right: 10px;
  bottom: 10px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
  &.send_button_active {
    opacity: 1;
    transform: translateY(0);
  }
}



@keyframes dot-flashing {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 1;
  }
}
</style> 