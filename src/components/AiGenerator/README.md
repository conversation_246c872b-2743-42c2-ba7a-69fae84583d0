# AI 生成器组件

AI 生成器是一个基于 Vue.js 的智能生成组件库，目前主要用于生成网站页面。

## 目录结构

```
src/components/AiGenerator/
├── components/              # 组件目录
│   ├── base/               # 基础组件
│   │   └── BaseChat.vue    # 基础聊天组件
│   ├── site/               # 网站生成相关组件
│   │   └── SiteChat.vue    # 网站生成聊天组件
│   └── form/               # 表单相关组件（预留）
├── generators/             # 生成器目录
│   ├── site/              # 网站生成器
│   │   └── SiteGenerator.js
│   └── form/              # 表单生成器（预留）
├── core/                  # 核心功能
│   └── BaseGenerator.js   # 基础生成器类
└── utils/                 # 工具函数
    └── uuid.js           # UUID 生成工具

```

## 组件说明

### BaseChat.vue

基础聊天组件，提供了通用的聊天界面功能：

- 消息列表展示
- 用户输入处理
- 加载状态管理
- 自动滚动
- 时间格式化
- 消息格式化（支持链接和换行）

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| userAvatar | String | defaultUserAvatar | 用户头像 |
| aiAvatar | String | defaultAiAvatar | AI头像 |
| placeholder | String | '请输入消息...' | 输入框占位文本 |
| initialMessages | Array | [] | 初始消息列表 |

#### 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| send | message: String | 发送消息时触发 |

### SiteChat.vue

网站生成专用的聊天组件，继承自 BaseChat：

- 集成预览功能
- 支持现有数据迭代
- 友好的错误处理
- 引导式交互

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| existingData | Object | null | 现有页面数据 |

#### 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| generated | { prompt, pageData, existingData } | 页面生成完成时触发 |

## 生成器说明

### SiteGenerator

网站页面生成器，支持以下功能：

- 表单页面生成
- 落地页生成
- 展示页面生成

#### 页面类型

1. 表单页面
   - 支持多种表单组件
   - 自动表单验证
   - 数据提交配置

2. 落地页
   - 支持多种布局
   - 响应式设计
   - 自定义样式

3. 展示页面
   - 灵活的内容展示
   - 多媒体支持
   - 交互效果

## 使用示例

```vue
<template>
  <div class="page-container">
    <site-chat
      :existing-data="pageData"
      @generated="handleGenerated"
    />
  </div>
</template>

<script>
import { SiteChat } from '@/components/AiGenerator';

export default {
  components: {
    SiteChat
  },
  data() {
    return {
      pageData: null
    };
  },
  methods: {
    handleGenerated({ prompt, pageData }) {
      console.log('生成的页面数据：', pageData);
      this.pageData = pageData;
    }
  }
};
</script>
```

## 开发指南

### 添加新的生成器

1. 在 `generators` 目录下创建新的生成器目录
2. 继承 `BaseGenerator` 类
3. 实现必要的方法：
   - `generate`: 生成数据的核心逻辑
   - `validate`: 数据验证逻辑
   - `repair`: 数据修复逻辑

### 添加新的组件

1. 在 `components` 目录下创建新的组件目录
2. 如果需要聊天界面，继承 `BaseChat`
3. 实现组件特定的功能和样式

## 注意事项

1. 生成的页面数据必须符合规范
2. 注意处理敏感信息
3. 合理使用错误处理
4. 保持代码风格一致
5. 及时更新文档

## 待办事项

- [ ] 完善表单生成器
- [ ] 添加更多页面模板
- [ ] 优化生成算法
- [ ] 添加单元测试
- [ ] 支持更多自定义配置 