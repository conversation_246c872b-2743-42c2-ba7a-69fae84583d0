@import url("./iconfont.less");
.container {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  margin: 0 auto;
  min-height: 100vh;
  min-width: 1200px;
  background-color: #f2f2f5;
  display: flex;
  flex-direction: column;
  color: #181c25;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  .contet {
    height: calc(100vh - 54px);
    flex: 1;
    display: flex;
    overflow: hidden;
  }
  &--agent {
    height: 100%;
    min-height: auto;
    .contet {
      height: calc(100% - 54px);
    }
  }
  :global {
    ::-webkit-scrollbar {
      width: 6px;
      background: none;
      height: 6px;
    }

    ::-webkit-scrollbar-button {
      height: 0;
      width: 0;
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(0, 0, 0, 0.2);
    }

    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: rgba(0, 0, 0, 0.4);
    }

    ::-webkit-scrollbar-thumb:horizontal {
      border-width: 6px 1px 1px;
      padding: 0 0 0 100px;
      box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1),
        inset -1px 0 0 rgba(0, 0, 0, 0.07);
    }

    ::-webkit-scrollbar-corner {
      background: transparent;
    }

    .hexagon-icon {
      display: inline-block;
      speak: none;
      font-feature-settings: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;
      vertical-align: baseline;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    .el-radio-group {
      .el-radio {
        margin-left: 0;
      }
      .el-radio-button {
        &:focus {
          outline: none;
          box-shadow: none !important;
        }
        &.is-active {
          .el-radio-button__inner {
            background-color: var(--color-primary06,#ff8000);
            border-color: var(--color-primary06,#ff8000);
            box-shadow: -1px 0 0 0 var(--color-primary06,#ff8000);
            &:hover {
              color: #fff;
            }
          }
        }
        .el-radio-button__inner {
          &:hover {
            color: var(--color-info06,#407FFF);
          }
        }
      }
    }
    .el-button--primary {
      color: #ffffff;
      background-color: var(--color-primary06,#ff8000);
      border-color: var(--color-primary06,#ff8000);
    }
  }
}
:global {
  .hexagon__setting-colorpicker {
    .el-color-predefine__color-selector {
      border: 1px solid #e9edf5;
      box-sizing: border-box;
    }
  }
}
