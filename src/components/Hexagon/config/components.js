import { deepClone, createId, getSystemThemeColor } from '../utils/index.js'
/**
 * 默认边框样式
 */
const defaultBorderStyle = {
  borderWidth: 0,
  borderRadius: 0,
  borderStyle: 'none',
  borderColor: '#e9edf5',
}

/**
 * 默认输入框样式
 */
const defaultInputStyle = {
  color: '#181C25',
  width: 345,
  // height: 45,
  // lineHeight: 45,
  fontSize: 14,
  paddingBottom: 0,
  paddingTop: 0,
  paddingLeft: 12,
  paddingRight: 12,
  borderStyle: 'solid',
  borderWidth: 1,
  borderRadius: 3,
  borderColor: '#e9edf5',
  marginLeft: 15,
  marginTop: 15,
}

/**
 * 表单组件默认title样式
 */
const defaultFormCompTitleStyle = {
  color: '#181C25',
  fontSize: 14,
  lineHeight: 16,
  paddingBottom: 6,
  paddingTop: 6,
  whiteSpace: 'normal',
}

/**
 * 按钮组件默认配置
 */
const button = {
  id: '',
  label: $t('marketing_pd.commons.tj_939d53'),
  name: $t('marketing_pd.commons.tj_939d53'),
  tip: $t('marketing_pd.commons.tjcg_23b62e'),
  type: 'button',
  position: 'none',
  required: false,
  isFormComp: true,
  layoutType: 'text-only',
  textAlign: 'center',
  wrapStyle: {
    position: 'none',
  },
  style: {
    height: 45,
    width: 345,
    fontSize: 16,
    background: '#409EFF',
    borderRadius: 3,
    color: '#fff',
    letterSpacing: 0,
    lineHeight: 45,
    textAlign: 'center',
    marginLeft: 'auto',
    marginRight: 'auto',
    marginTop: 15,
    marginBottom: 15,
    // margin: '15px auto',
    boxShadow: '0px 0px 0px rgba(0,0,0,.1)',
    boxShadowLeft: 0,
    boxShadowTop: 0,
    boxShadowRadius: 0,
    boxShadowColor: 'rgba(0,0,0,.1)',
    ...defaultBorderStyle,
  },
}

const signupButtonItemStyle = {
  color: '#fff',
  fontSize: 16,
  lineHeight: 45,
  textAlign: 'center',
  letterSpacing: 0,
}

/**
 * 报名按钮组件默认配置
 */
const signupButtonConfig = {
  ...button,
  style: {
    ...button.style,
    borderRadius: 6,
  },
  type: 'signupbutton',
  typeValue: 'meeting',
  label: $t('marketing_pd.config.hybman_a14bdc'),
  name: $t('marketing_pd.config.hybm_bb1d71'),
  required: false,
  isFormComp: false,
  memberAutoSignup: false,
  memberAutoSignupButtonStyle: {
    textAlign: 'center',
    color: '#181c25',
    fontSize: 16,
    lineHeight: 45,
  },
  memberAutoSignupButton: true,
  memberAutoSignupButtonText: $t('marketing_pd.commons.hydl_83e581'),
  member: {},
  objectId: '',
  objectTitle: '',
  objectName: '', // 物料名称
  status: 'before', // 进度状态；before：开始前 processing：进行中 after：结束后
  schedule: {}, // 进度配置内容
}

/**
 * 分步表单按钮组件默认配置
 */
const stepButtonConfig = {
  id: '',
  label: $t('marketing_pd.config.fban_2e8f25'),
  name: $t('marketing_pd.commons.xyb_38ce27'),
  layoutType: 'text-only',
  backlabel: $t('marketing_pd.config.syb_eeb690'),
  backLayoutType: 'text-only',
  tip: '',
  type: 'stepbutton',
  position: 'none',
  required: false,
  isFormComp: true,
  hasBackBtn: true,
  noDeletion: true,
  isFixedBottom: false,
  backRate: 30,
  wrapStyle: { position: 'relative' },
  btnStyle: {
    background: '#409EFF',
    color: '#fff',
    borderRadius: 0,
    letterSpacing: 0,
    textAlign: 'center',
    borderWidth: 0,
    borderStyle: 'none',
    borderColor: '#e9edf5',
    fontSize: 16,
    lineHeight: 45,
  },
  backStyle: {
    background: '#409EFF',
    color: '#fff',
    borderRadius: 0,
    letterSpacing: 0,
    textAlign: 'center',
    borderWidth: 0,
    borderStyle: 'none',
    borderColor: '#e9edf5',
    fontSize: 16,
    lineHeight: 45,
  },
  style: {
    height: 45,
    width: 345,
    position: 'none',
    marginLeft: 'auto',
    marginRight: 'auto',
    marginTop: 15,
    marginBottom: 15,
    // margin: '15px auto',
  },
  // style: {
  //   height: 45,
  //   width: 345,
  //   left: 16,
  //   top: 165,
  //   position: "absolute",
  //   margin: '0 auto',
  // },
  sort: '0',
}

/**
 * 悬浮按钮
 */
const suspensionButtonConfig = {
  label: $t('marketing_pd.commons.cbxf_5537aa'),
  name: $t('marketing_pd.config.an_fa9663'),
  type: 'suspension',
  required: false,
  isFormComp: false,
  backgroundFillType: 'filling',
  showType: 'text',
  layoutType: 'circle',
  iconLayoutType: 'line',
  iconInfo: {
    icon: '',
    iconType: 'iconfont',
    iconStyle: {
      color: '',
    },
  },
  wrapStyle: {
    position: 'fixed',
    zIndex: 10,
    left: 310,
    top: 375,
    width: 58,
    height: 58,
  },
  style: {
    width: 58,
    height: 58,
    borderRadius: 58,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e9edf5',
    color: '#333',
    fontSize: 12,
    backgroundColor: '#fff',
    boxShadow: '2px 2px 12px rgba(0,0,0,.1)',
    boxShadowLeft: 2,
    boxShadowTop: 2,
    boxShadowRadius: 12,
    boxShadowColor: 'rgba(0,0,0,.1)',
  },
}

/**
 * 页面数据结构
 */
const defaultPageData = {
  id: '',
  type: 'page',
  name: '', // 页面名称
  title: '', // 页面标题
  version: '1.0.0', // 版本信息
  cover: '',
  pcAdaptation: false, // 是否开启pc端兼容模式
  isPdf: false,
  shareOpts: {
    title: '',
    desc: '',
    link: '',
    imgUrl: '',
    sharePosterUrl: '',
    sharePosterAPath: '',
  },
  popupOpts: {
    isPreview: false,
    fillType: 1,
    isShow: false,
    backgroundStyle: {
      objectFit: 'cover',
      width: '100%',
      height: 'auto',
    },
    width: 280,
    height: 490,
    borderRadius: 16,
    positionY: 50,
    show: 0,
    coverUrl: '',
    mode: 1,
    action: {},
  },
  backgroundOpts: {
    mode: 1, // 1: 纯颜色，2: 单张图片，3: 轮播图
    bgColor: '#f9f9f9',
    bgImage: '',
    fillType: 'horizontal-filling',
    carouselImgs: [],
    carouselPointType: 5, // 1: 无，2：短线，3: 长线，4：空心原点，5：实心原点
    carouselPointBottom: 24, // 指示点高度
    carouselPointIsShow: false, // 是否显示指示点
    carouselPointAutoPlay: true, // 是否自动播放
    playDuration: 200, // 动画过渡时间
    carouselPointColor: 'rgba(255, 255, 255, 0.5)', // 轮播指示点颜色
    carouselPointActiveColor: '#fff', // 轮播指示点激活颜色
    carouselPointActiveIndex: 0, // 轮播指示点激活索引
    carouselPlayInterval: 3000, // 自动播放的间隔时间（毫秒）
    circlePointSize: 6, // 指示点圆点大小
    circlePointMargin: 5, // 指示点原点间隔
    dashPointWidth: 12, // 指示点虚线宽度
    dashPointHeight: 2, // 指示点虚线高度
    dashPointMargin: 4, // 指示点虚线间隔
    slidePointWidth: 180, // 指示点滑块宽度
    slidePointHeight: 1, // 指示点滑块高度
    indicatorLeft: 187, // 指示点X轴
    indicatorTop: 150, // 指示点Y轴
  },
  headerOpts: {
    isCustomMiniapp: false,
    fpHeaderBackgroundColor: 'rgba(255, 255, 255, 0)', // 背景色
    fpFontColor: 1, // 标题字/按钮/电池条颜色 // 1: 黑色， 2: 白色
    fpHideTitle: true, // 是否隐藏标题
    isCustomSticky: false,
    headerBackgroundColor: '#ffffff', // 悬浮栏背景色
    fontColor: 1, // 悬浮栏 标题字/按钮/电池条颜色 1: 黑色， 2: 白色
    // titleColor: '#000',
    // headerBackgroundColor: '#fff', // 悬浮栏背景色
    // fontColor: 1, // 悬浮栏 标题字/按钮/电池条颜色 1: 黑色， 2: 白色
    // isCustomFp: false,
    // fpFontColor: 1, // 首屏标题字/按钮/电池条颜色 // 1: 黑色， 2: 白色
    // fpHideTitle: true, // 首屏是否隐藏标题
    // fpHideHeaderBg: true, // 首屏是否隐藏顶部栏背景色
  },
  // 页面样式
  style: {
    width: 375,
    backgroundColor: '#f9f9f9',
    backgroundSize: '100%',
    backgroundRepeat: 'no-repeat',
    backgroundImage: '',
  },
  backgroundFillType: 'horizontal-filling', // 背景填充类型
  dataSourceAction: {},
  components: [],
  // language: 'zh-CN', // 语言  zh-CN:简体中文|en:英文
}

/**
 * 生成container容器默认配置
 * @param {Object} args
 */
const genContainerConfig = args => ({
  id: '',
  name: '',
  key: '', // 组件唯一key
  type: 'container',
  typeValue: '',
  components: [], // 存放其他组件
  current: 0, // 初始化选择布局索引，layout为multiple时有效
  slideIndex: 0, // 设计器选中布局索引，layout为multiple时有效
  layout: 'single', // single|multiple
  fillType: 'color', // color|image
  fillMethod: 'filling', // 填充方式
  typesetting: 'absolute', // 排版类型：absolute|flow，默认使用绝对定位布局
  visualStyle: {
    overflowX: 'hidden',
    overflowY: 'auto',
    height: 30,
  },
  style: {
    width: 375,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '',
    backgroundImage: '',
    ...(args.typesetting !== 'flow' ? { height: 235 } : {}),
    ...(args.style || {}),
  },
  ...args,
})

const genStepButtonConfig = args => ({
  ...deepClone(stepButtonConfig),
  id: createId(),
  ...args,
})
const genGridConfig = args => ({
  id: '',
  name: '',
  key: '', // 组件唯一key
  type: 'gridcontainer',
  typeValue: '',
  components: [], // 存放其他组件
  fillType: 'color', // color|image
  fillMethod: 'filling', // 填充方式
  style: {
    width: 375,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '',
    backgroundImage: '',
    ...(args.style || {}),
  },
  ...args,
})

// 邮件默认模板
const defaultFileSendMailTemplate = '<div id="yxt_tinymce_body_class"'
  + `style="font-size: 16px; line-height: 1.4; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;"`
  + '>'
    + `<p style="text-align: center;">${$t('marketing.components.Hexagon.ndwjyxfqdj_bdf2d1')}</p>`
    + '<p style="text-align: center;"><a href="$${file.url}">$${file.name}</a></p>'
    + `<p style="text-align: center;">${$t('marketing.components.Hexagon.rwfdjwjmxz_74d627')}</p>`
    + '<p style="text-align: center;"><a href="$${file.url}">$${file.url}</a></p>'
 + '</div>'

/**
 * 所有微页面组件默认配置
 */
const components = [
  {
    title: $t('marketing_pd.config.jczj_249d95'),
    value: 'baseComp',
    components: [
      {
        title: $t('marketing_pd.config.wb_97d076'),
        icon: 'el-icon-edit-outline',
        value: 'text',
        showIn: ['page', 'canvas', 'flow', 'grid'], // 显示场景
        config: {
          id: '',
          name: $t('marketing_pd.config.wb_97d076'),
          type: 'text',
          // value: '<p style="text-align: center;">请输入文本</p>',
          value: $t('marketing_pd.config.qsrwb_9fc954'),
          // noEdit: false,
          // noDeletion: true,
          style: {
            // width: 375,
            // height: 32,
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            background: 'rgba(255, 255, 255, 0)',
            fontSize: 14,
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.tp_20def7'),
        icon: 'el-icon-picture-outline',
        value: 'image',
        showIn: ['page', 'canvas', 'flow', 'grid'],
        config: {
          name: $t('marketing_pd.commons.tp_20def7'),
          type: 'image',
          previewEnable: false,
          images: [
            {
              url: '',
              action: {},
              uploadType: 'upload',
            },
          ],
          // itemStyle: {
          //   borderRadius: 0,
          // },
          imageGap: 4, // 图片间隙
          style: {
            display: 'flex',
            width: 375,
            height: 180,
            paddingBottom: 0,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            borderRadius: 0,
            background: 'rgba(255, 255, 255, 0)',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            ...defaultBorderStyle,
          },
          filterConfig: {
            brightness: 100,
            // blur: 0,
            grayscale: 0,
            opacity: 100,
          },
        },
      },
      {
        title: $t('marketing_pd.config.sp_7fcf42'),
        icon: 'el-icon-video-camera',
        value: 'video',
        showIn: ['page', 'flow', 'grid'],
        // disable: true,
        config: {
          id: '',
          name: $t('marketing_pd.config.sp_7fcf42'),
          type: 'video',
          layoutType: 'video-and-icon',
          iconMode: 'light',
          iconSize: 72,
          url: '', // 视频地址
          cover: '', // 视频封面
          autoplay: false, // 是否自动播放，目前仅在小程序上支持
          loop: false, // 循环播放
          style: {
            width: 375,
            height: 210,
            background: '#000',
            paddingBottom: 0,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            ...defaultBorderStyle,
          },
          wrapStyle: {
            position: 'relative',
            zIndex: 10,
            left: 0,
            top: 0,
          },
        },
      },
      {
        title: $t('marketing_pd.config.hdp_40bdf1'),
        icon: 'el-icon-film',
        value: 'slider',
        showIn: ['page', 'canvas', 'flow', 'grid'],
        config: {
          id: '',
          name: $t('marketing_pd.config.hdp_40bdf1'),
          type: 'slider',
          autoplay: true, // 自动播放
          interval: 3000, // 间隔时间
          duration: 300, // 过渡时间
          indicators: true, // 是否显示指示点
          indicatorType: 5, // 1: 无，2：短线，3: 长线，4：空心原点，5：实心原点
          indicatorColor: '#999999', // 指示点颜色
          indicatorActiveColor: '#FF8000', // 指示点选中颜色
          circlePointSize: 6, // 指示点圆点大小
          circlePointMargin: 5, // 指示点原点间隔
          dashPointWidth: 12, // 指示点虚线宽度
          dashPointHeight: 2, // 指示点虚线高度
          dashPointMargin: 4, // 指示点虚线间隔
          slidePointWidth: 180, // 指示点滑块宽度
          slidePointHeight: 1, // 指示点滑块高度
          indicatorLeft: 187,
          indicatorBottom: 24,
          indicatorTop: 150,
          active: 0,
          slider: [
            // {
            //   url: "", //图片地址
            //   action: {} //事件
            // },
            // {
            //   url: "", //图片地址
            //   action: {} //事件
            // }
          ],
          style: {
            width: 375,
            height: 180,
            paddingBottom: 0,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            overflow: 'hidden',
            ...defaultBorderStyle,
            // ...
          },
        },
      },
      {
        title: $t('marketing_pd.commons.jx_fbbebc'),
        icon: 'hexagon-icon hicon-huajuxing',
        value: 'rectangle',
        showIn: ['canvas', 'grid'], // 显示场景
        // hideBorder: true,
        // hidePadding: true,
        config: {
          id: '',
          name: $t('marketing_pd.commons.jx_fbbebc'),
          type: 'rectangle',
          radiusMode: 1, // 1: 全部，2: 单独
          borderRadiusStyles: {
            borderRadius: 0,
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0,
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
          },
          rectangle: {
            style: {
              width: 150,
              height: 150,
              backgroundColor: '#d9d9d9',
              display: 'inline-block',
            },
          },
          style: {
            width: 150,
            height: 150,
            backgroundColor: '#d9d9d9',
            lineHeight: 20,
            fontSize: 14,
            paddingTop: 0,
            paddingBottom: 0,
            paddingLeft: 0,
            paddingRight: 0,
            color: '#181C25',
            boxShadowLeft: 0,
            boxShadowTop: 0,
            boxShadowRadius: 0,
            boxShadowColor: 'rgba(0,0,0,.1)',
            borderWidth: 0,
            borderStyle: 'none',
            borderColor: '#e9edf5',
          },
        },
      },
      {
        title: $t('marketing_pd.config.fgx_8c47ec'),
        icon: 'el-icon-document-remove',
        value: 'line',
        showIn: ['page', 'canvas', 'flow', 'grid'], // 显示场景
        config: {
          id: '',
          name: $t('marketing_pd.config.fgx_8c47ec'),
          type: 'line',
          line: {
            style: {
              width: 345,
              borderBottomWidth: 1,
              borderBottomColor: '#333',
              borderBottomStyle: 'solid',
              display: 'inline-block',
            },
          },
          style: {
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            width: 375,
            background: 'rgba(255, 255, 255, 0)',
            textAlign: 'center',
            lineHeight: 0,
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.config.fzlb_e8cb18'),
        icon: 'el-icon-full-screen',
        value: 'blank',
        showIn: ['page', 'flow', 'grid'], // 显示场景
        config: {
          id: '',
          name: $t('marketing_pd.config.fzlb_e8cb18'),
          type: 'blank',
          style: {
            width: 375,
            height: 50,
            background: 'rgba(255, 255, 255, 0)',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.config.an_fa9663'),
        icon: 'el-icon-thumb',
        value: 'button',
        showIn: ['page', 'canvas', 'flow', 'grid'],
        // max: 10,
        config: {
          ...button,
          label: $t('marketing_pd.config.an_fa9663'),
          name: $t('marketing_pd.config.an_fa9663'),
          required: false,
          isFormComp: false,
          iconInfo: {
            icon: '',
            iconType: 'iconfont',
            iconStyle: {
              color: '',
            },
          },
          buttonList: [
            {
              name: $t('marketing_pd.config.an_fa9663'),
              iconInfo: {
                icon: '',
                iconType: 'iconfont',
                iconStyle: {
                  color: '',
                },
              },
              action: {},
              style: {
                background: '#409EFF',
                color: '#ffffff',
                borderRadius: 3,
                ...defaultBorderStyle,
              },
            },

          ],
          iconSize: 22,
          layoutType: 'text-only',
          iconLayoutType: 'line',
        },
      },
      {
        title: $t('marketing_pd.commons.cbxf_5537aa'),
        icon: 'hexagon-icon hicon-cebianxuanfu',
        value: 'suspension',
        showIn: ['page', 'flow'],
        // max: 10,
        config: {
          ...suspensionButtonConfig,
        },
      },
      // {
      //   title: "链接",
      //   icon: "el-icon-link",
      //   value: "button",
      //   showIn: ["page", "canvas"],
      //   config: {
      //     ...button,
      //     name: "链接",
      //     required: false,
      //     isFormComp: false,
      //     style: {
      //       ...button.style,
      //       width: 375,
      //     }
      //   }
      // }
    ],
  },
  {
    title: $t('marketing_pd.config.bjzj_eae370'),
    value: 'superComp',
    components: [
      {
        title: $t('marketing_pd.commons.bd_eee1e2'),
        icon: 'el-icon-postcard',
        value: 'container',
        showIn: ['page', 'grid'],
        max: 1,
        config: genContainerConfig({
          name: $t('marketing_pd.commons.bd_eee1e2'),
          key: 'form-container', // 组件唯一key
          typeValue: 'form',
          typesetting: 'flow',
          formLayout: 'default',
        }),
      },
      {
        title: $t('marketing_pd.commons.fbbd_e7d713'),
        icon: 'hexagon-icon hicon-fenbuzhoubiaodan',
        value: 'container',
        showIn: ['page', 'grid'],
        max: 1,
        config: genContainerConfig({
          name: $t('marketing_pd.commons.fbbd_e7d713'),
          key: 'form-container', // 组件唯一key
          typeValue: 'step-form',
          isStepSubmit: true,
          formLayout: 'default',
          typesetting: 'flow',
        }),
      },
      {
        title: $t('marketing_pd.config.zfbd_ff542e'),
        icon: 'el-icon-shopping-cart-full',
        value: 'container',
        showIn: ['page', 'grid'],
        max: 1,
        config: genContainerConfig({
          name: $t('marketing_pd.config.zfbd_ff542e'),
          key: 'form-container', // 组件唯一key
          typeValue: 'order',
          formLayout: 'default',
        }),
      },
      {
        title: $t('marketing_pd.config.dxlb_a7958d'),
        icon: 'hexagon-icon hicon-liebiao',
        value: 'container',
        showIn: ['page', 'grid'],
        max: 1,
        config: genContainerConfig({
          name: $t('marketing_pd.commons.lb_371297'),
          key: 'list-container', // 组件唯一key
          typeValue: 'list',
        }),
      },
      {
        title: $t('marketing_pd.config.dxxq_ae692b'),
        icon: 'hexagon-icon hicon-duixiangxiangqing',
        value: 'container',
        showIn: ['page', 'grid'],
        config: genContainerConfig({
          name: $t('marketing_pd.config.dxxq_ae692b'),
          key: 'auto-container', // 组件唯一key
          typeValue: 'auto',
        }),
      },
      {
        title: $t('marketing_pd.commons.grxx_eab129'),
        icon: 'hexagon-icon hicon-gerenzhongxin',
        value: 'container',
        showIn: ['page', 'grid'],
        max: 1,
        config: genContainerConfig({
          name: $t('marketing_pd.commons.grxx_eab129'),
          key: 'personal-container', // 组件唯一key
          typeValue: 'personal',
          style: {
            height: 100,
          },
        }),
      },
      // {
      //   title: "个人信息",
      //   icon: "hexagon-icon hicon-gerenzhongxin",
      //   value: "personal",
      //   showIn: ["page", "canvas"], //显示场景
      //   config: {
      //     id: "",
      //     name: "个人信息",
      //     type: "personal",
      //     value: "",
      //     style: {
      //       width: 375,
      //       height: 143,
      //       paddingBottom: 0,
      //       paddingLeft: 0,
      //       paddingRight: 0,
      //       paddingTop: 0,
      //       background: "linear-gradient(180deg, #142044, #5b535b)",
      //       fontSize: 14,
      //       color: "#fff",
      //       ...defaultBorderStyle
      //     }
      //   }
      // },
      {
        title: $t('marketing_pd.commons.cd_4ccbdc'),
        icon: 'hexagon-icon hicon-caidan',
        value: 'menu',
        showIn: ['page', 'grid'], // 显示场景
        config: {
          id: '',
          name: $t('marketing_pd.commons.cd_4ccbdc'),
          type: 'menu',
          value: '',
          layout: 'grid',
          iconLayoutType: 'line',
          columnCount: 4,
          iconSize: 22,
          fontSize: 14,
          menus: [
            {
              name: $t('marketing_pd.config.cdbt_79b292'),
              iconType: 'iconfont',
              icon: {
                flat: 'hicon-chanpin4liang',
                line: 'hicon-yingyonghui',
              },
              iconStyle: {
                color: 'rgba(254, 95, 69, 1)',
                // fontSize: 22,
              },
              style: {
                color: '#181c25'
              },
              action: {},
            },
            {
              name: $t('marketing_pd.config.cdbt_79b292'),
              iconType: 'iconfont',
              icon: {
                flat: 'hicon-chanpin4liang',
                line: 'hicon-yingyonghui',
              },
              iconStyle: {
                color: 'rgba(254, 184, 62, 1)',
                // fontSize: 22,
              },
              style: {
                color: '#181c25'
              },
              action: {},
            },
            {
              name: $t('marketing_pd.config.cdbt_79b292'),
              iconType: 'iconfont',
              icon: {
                flat: 'hicon-chanpin4liang',
                line: 'hicon-yingyonghui',
              },
              iconStyle: {
                color: 'rgba(149, 205, 96, 1)',
                // fontSize: 22,
              },
              style: {
                color: '#181c25'
              },
              action: {},
            },
            {
              name: $t('marketing_pd.config.cdbt_79b292'),
              iconType: 'iconfont',
              icon: {
                flat: 'hicon-chanpin4liang',
                line: 'hicon-yingyonghui',
              },
              iconStyle: {
                color: 'rgba(43, 156, 250, 1)',
                // fontSize: 22,
              },
              style: {
                color: '#181c25'
              },
              action: {},
            },
          ],
          style: {
            paddingBottom: 10,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 10,
            background: 'rgba(255, 255, 255, 0)',
            fontSize: 14,
            color: '#181c25',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.zdybj_fe6488'),
        icon: 'el-icon-edit',
        value: 'container',
        showIn: ['page', 'grid'],
        config: genContainerConfig({
          name: $t('marketing_pd.commons.zdybj_fe6488'),
          key: 'auto-container', // 组件唯一key
          typeValue: 'auto',
          isVisual: true,
          visual: false,
        }),
      },
      {
        title: $t('marketing_pd.config.dhbj_9959b2'),
        icon: 'hexagon-icon hicon-bujudaohang',
        value: 'container',
        showIn: ['page', 'grid'],
        config: genContainerConfig({
          name: $t('marketing_pd.config.dhbj_9959b2'),
          key: 'tab-container', // 组件唯一key
          typeValue: 'tab-container',
          tabLayout: 'underline',
          enableDividingLine: true,
          dividingLineColor: '#DEE1E8',
          underlineType: 'long-line',
          capsuleType: 'big-round',
          tabColor: '#181C25',
          tabCapsuleColor: '#EFEFEF',
          tabActiveColor: '#F86E30',
          tabActiveCapsuleColor: '#0C6CFF',
          tabBackgroundColor: '#FFFFFF',
          tabHeight: 40,
          tabFontSize: 16,
          typesetting: 'flow',
        }),
      },
      {
        title: $t('marketing_pd.config.sgbj_00e840'),
        icon: 'hexagon-icon hicon-icon-grid',
        value: 'gridcontainer',
        showIn: ['page'],
        config: genGridConfig({
          name: $t('marketing_pd.config.sgbj_00e840'),
          key: 'grid', // 组件唯一key
          typeValue: 'grid',
          typesetting: 'grid',
          style: {
          },
        }),
      },
      // {
      //   title: '地图',
      //   icon: 'el-icon-map-location',
      //   value: 'map',
      //   showIn: ['page', 'canvas'],
      // },
    ],
  },
  {
    title: $t('marketing_pd.config.bdzj_e63727'),
    value: 'formComp',
    components: [
      {
        title: $t('marketing_pd.config.xm_60d045'),
        icon: 'el-icon-user',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1, // 可生成数量
        config: {
          id: '',
          label: $t('marketing_pd.config.xm_60d045'),
          name: $t('marketing_pd.config.xm_60d045'),
          title: '',
          type: 'input',
          typeValue: 'text',
          fieldName: 'name',
          customFieldName: '',
          defaultValueOpen: false,
          defaultValue: '',
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: true,
          placeholder: $t('marketing_pd.config.qsrxm_8093e3'),
          isFormComp: true,
          style: defaultInputStyle,
          titleStyle: defaultFormCompTitleStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.config.sjh_8098e2'),
        icon: 'el-icon-mobile-phone',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.config.sjh_8098e2'),
          name: $t('marketing_pd.config.sjh_8098e2'),
          title: '',
          type: 'input',
          typeValue: 'number',
          fieldName: 'phone',
          customFieldName: '',
          pattern: '^1[0-9]\\d{9}$',
          defaultValue: '',
          defaultValueOpen: false,
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: true,
          verify: false,
          enableInternationalCode: false,
          weChatAuthorizationButton: false, // 开启从微信授权获取手机号码按钮
          placeholder: $t('marketing_pd.config.qsrsjh_6e4f4b'),
          isFormComp: true,
          // noDeletion: true,
          // memberExclusive: true,
          weChatAuthorizationButtonStyle: {
            color: '#fff',
            background: '#09BB07',
            fontSize: 14,
            borderStyle: 'solid',
            borderWidth: 0,
            borderRadius: 3,
            borderColor: '#e9edf5',
          },
          verifyButtonStyle: {
            color: '#181C25',
            background: '#ffffff',
            fontSize: 14,
            borderStyle: 'solid',
            borderWidth: 1,
            borderRadius: 3,
            borderColor: '#e9edf5',
          },
          titleStyle: defaultFormCompTitleStyle,
          style: defaultInputStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.config.yx_3bc5e6'),
        icon: 'el-icon-message',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.config.yx_3bc5e6'),
          name: $t('marketing_pd.config.yx_3bc5e6'),
          title: '',
          type: 'input',
          typeValue: 'text',
          fieldName: 'email',
          customFieldName: '',
          pattern: '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$',
          defaultValue: '',
          defaultValueOpen: false,
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: true,
          placeholder: $t('marketing_pd.config.qsryx_dbf6d0'),
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: defaultInputStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.config.gsmc_d4b097'),
        icon: 'el-icon-office-building',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.config.gsmc_d4b097'),
          name: $t('marketing_pd.config.gsmc_d4b097'),
          title: '',
          type: 'input',
          typeValue: 'text',
          fieldName: 'companyName',
          customFieldName: '',
          defaultValue: '',
          defaultValueOpen: false,
          enableBusinessQuery: true, // 开启工商查询
          enforceSelectBusiness: false, // 强制使用工商信息
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: true,
          placeholder: $t('marketing_pd.config.qsrgsmc_96e6a2'),
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: {
            ...defaultInputStyle,
            paddingRight: 32,
          },
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.config.zw_77ec2a'),
        icon: 'el-icon-coordinate',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.config.zw_77ec2a'),
          name: $t('marketing_pd.config.zw_77ec2a'),
          title: '',
          type: 'input',
          typeValue: 'text',
          fieldName: 'position',
          customFieldName: '',
          defaultValue: '',
          defaultValueOpen: false,
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: true,
          placeholder: $t('marketing_pd.config.qsrzw_c28957'),
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: defaultInputStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.config.sfzh_32ffa2'),
        icon: 'el-icon-mobile-phone',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.config.sfzh_32ffa2'),
          name: $t('marketing_pd.config.sfzh_32ffa2'),
          title: '',
          type: 'input',
          typeValue: 'text',
          fieldName: 'identityCard',
          customFieldName: '',
          pattern:
          // eslint-disable-next-line max-len
            '(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)',
          defaultValue: '',
          defaultValueOpen: false,
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: true,
          placeholder: $t('marketing_pd.config.qsrsfzh_408c78'),
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: defaultInputStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.dhwb_f9291f'),
        icon: 'hexagon-icon hicon-danhangwenben',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        config: {
          id: '',
          label: $t('marketing_pd.commons.dhwb_f9291f'),
          name: $t('marketing_pd.commons.dhwb_f9291f'),
          title: '',
          type: 'input',
          typeValue: 'text',
          fieldName: 'text1',
          customFieldName: '',
          defaultValue: '',
          defaultValueOpen: false,
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: false,
          placeholder: $t('marketing_pd.commons.qsr_02cc4f'),
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: defaultInputStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.dhwb_15d169'),
        icon: 'hexagon-icon hicon-duohangwenben',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        config: {
          id: '',
          label: $t('marketing_pd.commons.dhwb_15d169'),
          name: $t('marketing_pd.commons.dhwb_15d169'),
          title: '',
          type: 'input',
          typeValue: 'textarea',
          fieldName: 'text6',
          customFieldName: '',
          defaultValue: '',
          defaultValueOpen: false,
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: false,
          placeholder: $t('marketing_pd.commons.qsr_02cc4f'),
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: {
            ...defaultInputStyle,
            // height: 100,
            paddingBottom: 10,
            paddingTop: 10,
            lineHeight: 20,
          },
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 100,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.sz_55d479'),
        icon: 'hexagon-icon hicon-shuzi1',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        config: {
          id: '',
          label: $t('marketing_pd.commons.sz_55d479'),
          name: $t('marketing_pd.commons.sz_55d479'),
          title: '',
          type: 'input',
          typeValue: 'number',
          fieldName: 'num1',
          customFieldName: '',
          defaultValue: '',
          defaultValueOpen: false,
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: false,
          placeholder: $t('marketing_pd.commons.qsr_02cc4f'),
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: defaultInputStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.rqsj_0c3bf4'),
        icon: 'el-icon-date',
        value: 'date',
        showIn: ['canvas', 'flow', 'grid'],
        config: {
          id: '',
          label: $t('marketing_pd.commons.rqsj_0c3bf4'),
          name: $t('marketing_pd.commons.rqsj_0c3bf4'),
          title: '',
          type: 'date',
          typeValue: 'date_time',
          fieldName: 'num2',
          customFieldName: '',
          required: false,
          placeholder: $t('marketing_pd.config.qxzrq_aa2fb1'),
          showType: 'datetime',
          isFormComp: true,
          titleStyle: defaultFormCompTitleStyle,
          style: defaultInputStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.dx_9fd1b7'),
        icon: 'el-icon-check',
        value: 'radio',
        showIn: ['canvas', 'flow', 'grid'],
        config: {
          id: '',
          label: $t('marketing_pd.commons.dx_9fd1b7'),
          name: $t('marketing_pd.commons.dx_9fd1b7'),
          title: '',
          type: 'radio',
          typeValue: 'select_one',
          layout: 'select', // select|tile，为空时默认tile
          fieldName: 'text7',
          customFieldName: '',
          required: false,
          placeholder: $t('marketing_pd.commons.qxz_708c9d'),
          isFormComp: true,
          otherOption: false,
          otherOptionValueRequired: false,
          associatedOpen: false,
          associatedFieldName: '',
          associatedFields: [],
          options: [
            {
              label: $t('marketing_pd.config.xx_6edda8'),
              value: 1,
              isDefault: false,
            },
            {
              label: $t('marketing_pd.config.xx_396920'),
              value: 2,
              isDefault: false,
            },
          ],
          components: [], // 存放其他组件
          titleStyle: defaultFormCompTitleStyle,
          style: {
            width: 345,
            // height: 45,
            color: '#181C25',
            borderRadius: 3,
            fontSize: 14,
            paddingBottom: 0,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 0,
            marginLeft: 15,
            marginTop: 15,
            borderStyle: 'solid',
            borderWidth: 1,
            borderColor: '#e9edf5',
          },
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.dx_443f46'),
        icon: 'el-icon-finished',
        value: 'checkbox',
        showIn: ['canvas', 'flow', 'grid'],
        config: {
          id: '',
          label: $t('marketing_pd.commons.dx_443f46'),
          name: $t('marketing_pd.commons.dx_443f46'),
          title: $t('marketing_pd.commons.dx_443f46'),
          type: 'checkbox',
          typeValue: 'select_manny',
          fieldName: 'texts1',
          customFieldName: '',
          layout: 'tile', // select|tile
          required: false,
          placeholder: $t('marketing_pd.commons.qxz_708c9d'),
          isFormComp: true,
          otherOption: false,
          otherOptionValueRequired: false,
          associatedOpen: false,
          associatedFieldName: '',
          associatedFields: [],
          options: [
            {
              label: $t('marketing_pd.config.xx_6edda8'),
              value: 1,
              isDefault: false,
            },
            {
              label: $t('marketing_pd.config.xx_396920'),
              value: 2,
              isDefault: false,
            },
          ],
          components: [], // 存放其他组件
          titleStyle: defaultFormCompTitleStyle,
          style: {
            width: 345,
            borderRadius: 3,
            fontSize: 14,
            marginTop: 15,
            marginLeft: 15,
            color: '#181C25',
            lineHeight: 20,
            borderWidth: 0,
            borderStyle: 'none',
            borderColor: '#e9edf5',
          },
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.ssq_5ea217'),
        icon: 'el-icon-location-outline',
        value: 'region',
        showIn: ['canvas', 'flow', 'grid'],
        // max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.commons.ssq_5ea217'),
          name: $t('marketing_pd.commons.ssq_5ea217'),
          title: '',
          type: 'region',
          typeValue: 'text',
          fieldName: 'region',
          customFieldName: '',
          required: false,
          placeholder: $t('marketing_pd.config.qxzssq_075488'),
          isFormComp: true,
          associatedOpen: false,
          associatedFieldName: '',
          associatedFields: [],
          fields: {
            country: {
              label: $t('marketing_pd.commons.gj_3d15d1'),
              value: 'country',
              disable: false,
              show: false,
            },
            province: {
              label: $t('marketing_pd.commons.s_3d14d1'),
              value: 'province',
              disable: false,
              show: true,
            },
            city: {
              label: $t('marketing_pd.commons.s_371528'),
              value: 'city',
              disable: false,
              show: true,
            },
            district: {
              label: $t('marketing_pd.commons.q_47c762'),
              value: 'district',
              disable: false,
              show: true,
            },
            address: {
              label: $t('marketing_pd.config.xxdz_61a0ec'),
              value: 'address',
              disable: false,
              show: true,
            },
          },
          titleStyle: defaultFormCompTitleStyle,
          style: {
            color: '#666',
            width: 345,
            // height: 100,
            fontSize: 14,
            paddingBottom: 0,
            paddingTop: 0,
            paddingLeft: 12,
            paddingRight: 12,
            marginLeft: 15,
            marginTop: 15,
            borderStyle: 'solid',
            borderWidth: 1,
            borderRadius: 3,
            borderColor: '#e9edf5',
          },
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.config.ldxx_5e7535'),
        icon: 'hexagon-icon hicon-liandong',
        value: 'cascade',
        showIn: ['canvas', 'flow', 'grid'],
        max: 5,
        config: {
          id: '',
          label: $t('marketing_pd.config.ldxx_5e7535'),
          name: $t('marketing_pd.config.ldxx_5e7535'),
          title: '',
          type: 'cascade',
          level: 3, // 级联层级, 最多支持5级
          typeValue: 'select_one',
          layout: 'select', // select|tile，为空时默认tile
          fieldName: 'text7',
          customFieldName: '',
          required: false,
          placeholder: $t('marketing_pd.commons.qxz_708c9d'),
          isFormComp: true,
          optionInfo: [],
          associatedOpen: false,
          associatedFieldName: '',
          associatedFields: [],
          options: [
            {
              label: $t('marketing_pd.config.yjxx_035a15'),
              value: 'first1',
              children: [
                {
                  label: $t('marketing_pd.config.ejxx_dffee8'),
                  value: 'second1',
                  children: [
                    {
                      label: $t('marketing_pd.config.sjxx_b1a6d8'),
                      value: 'thrid1',
                    },
                  ],
                },
              ],
            },
            {
              label: $t('marketing_pd.config.yjxx_0131d6'),
              value: 'first2',
              children: [
                {
                  label: $t('marketing_pd.config.ejxx_a457d7'),
                  value: 'second2',
                  children: [
                    {
                      label: $t('marketing_pd.config.sjx_d80853'),
                      value: 'thrid2',
                    },
                  ],
                },
              ],
            },
          ],
          components: [], // 存放其他组件
          titleStyle: defaultFormCompTitleStyle,
          style: {
            width: 345,
            // height: 45,
            color: '#181C25',
            borderRadius: 3,
            fontSize: 14,
            paddingBottom: 0,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 0,
            marginLeft: 15,
            marginTop: 15,
            borderStyle: 'solid',
            borderWidth: 1,
            borderColor: '#e9edf5',
          },
          placeholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing_pd.config.yssm_de763e'),
        icon: 'hexagon-icon hicon-yinsishengming',
        value: 'agreement',
        showIn: ['order', 'form', 'step-form', 'flow'],
        max: 1,
        config: {
          isFormComp: true,
          id: '',
          name: $t('marketing_pd.config.yssm_de763e'),
          type: 'agreement',
          title: $t('marketing_pd.commons.ydbtyysbhx_08485a'),
          tplTitle: $t('marketing_pd.commons.ydbtyysbhx_08485a'), // 标题模版
          placeholder: $t('marketing_pd.config.qydbtyysbh_eaad7d'), // 为阅读提示文案
          value: '', // string
          agreementType: 'text', // 协议类型，text 原来的文本类型 outLink 外部链接
          outLink: '', // 外部链接
          agreements: [], // 多协议隐私保护内容
          checked: false,
          checkedColor: '#409eff',
          num: 1, // 协议个数
          titleStyle: {
            ...defaultFormCompTitleStyle,
            color: '#409EFF',
          },
          style: {
            width: 236,
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            marginLeft: 15,
            marginTop: 15,
            background: 'rgba(255, 255, 255, 0)',
            fontSize: 14,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.tpsc_6bfb9b'),
        icon: 'hicon-tianjiatupian hexagon-icon',
        value: 'file',
        showIn: ['canvas', 'flow', 'grid'],
        config: {
          isFormComp: true,
          label: $t('marketing_pd.commons.tpsc_6bfb9b'),
          name: $t('marketing_pd.commons.tp_20def7'),
          type: 'file',
          typeValue: 'image',
          fieldName: 'picMap',
          customFieldName: '',
          required: false,
          placeholder: $t('marketing_pd.config.zdscyztp_e6cc31'),
          placeholderImage:
            'https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202104_02_5ea3e9bb0bff4e119f0c169f56b1a3a9.jpg',
          titleStyle: defaultFormCompTitleStyle,
          style: {
            width: 95,
            height: 95,
            paddingBottom: 0,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            marginLeft: 15,
            marginTop: 15,
            borderRadius: 0,
            background: 'rgba(255, 255, 255, 0)',
            borderWidth: 1,
            borderStyle: 'dashed',
            borderColor: '#4f9efd',
            overflow: 'hidden',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.tj_939d53'),
        icon: 'el-icon-circle-check',
        value: 'button',
        showIn: ['grid'],
        config: {
          ...button,
          style: {
            ...button.style,
            marginTop: 30,
          },
        },
      },
      {
        title: $t('marketing.commons.hymm_7f139e'),
        icon: 'hexagon-icon hicon-mima',
        value: 'input',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1, // 可生成数量
        config: {
          id: '',
          label: $t('marketing.commons.hymm_7f139e'),
          name: $t('marketing.commons.hymm_7f139e'),
          passwordType: 'member-login',
          title: '',
          type: 'input',
          typeValue: 'password',
          fieldName: 'password',
          customFieldName: '',
          defaultValueOpen: false,
          defaultValue: '',
          globalCacheField: '', // 全局缓存字段名
          defaultValueType: 'manual', // manual：手动输入 globalCache：从全局缓存从取值
          required: true,
          placeholder: $t('marketing.commons.qsrmm_e39ffe'),
          confirmPlaceholder: $t('marketing.commons.qzcsrmm_a24ab0'),
          isFormComp: true,
          style: defaultInputStyle,
          titleStyle: defaultFormCompTitleStyle,
          placeholderStyle: {
            color: '#cbcccf',
          },
          confirmPlaceholderStyle: {
            color: '#cbcccf',
          },
          inputStyle: {
            height: 45,
            color: '#181c25',
            background: '#fff',
          },
        },
      },
      {
        title: $t('marketing.components.Hexagon.wjsc_481e03'),
        icon: 'hexagon-icon hicon-xiazai1',
        value: 'fileupload',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1,
        config: {
          isFormComp: true,
          required: false,
          id: '',
          name: $t('marketing.components.Hexagon.wjsc_481e03'),
          type: 'fileupload',
          title: '',
          placeholder: '',
          value: $t('marketing.commons.xzwj_fd7e0c'),
          fileType: [],
          fileCount: 1,
          maxSize: 50,
          fieldName: 'files',
          customFieldName: '',
          style: {
            width: 375,
            height: 40,
            fontSize: 14,
            color: '#FF8000',
            background: '#FFFFFF',
            borderColor: '#C1C5CE',
            borderRadius: 4,
            borderWidth: 1,
            borderStyle: 'dashed',
            paddingBottom: 10,
            paddingLeft: 15,
            paddingRight: 15,
            paddingTop: 10,
          },
          titleStyle: defaultFormCompTitleStyle,
          placeholderStyle: {
            color: '#91959E',
            fontSize: 12,
            paddingTop: 10,
          },
        },
      },
      {
        title: $t('marketing.components.Hexagon.rjyz_d61a89'),
        icon: 'hexagon-icon hicon-renjijiaohu',
        value: 'imagecaptcha',
        showIn: ['canvas', 'flow', 'grid'],
        max: 1,
        config: {
          isFormComp: true,
          id: '',
          name: $t('marketing.components.Hexagon.rjyz_d61a89'),
          type: 'imagecaptcha',
          title: $t('marketing.components.Hexagon.ljdjyz_382580'),
          value: '',
          style: {
            width: 375,
            height: 64,
            fontSize: 14,
            color: '#FF7C19',
            background: '#FFF5E6',
            borderColor: '#FF7C19',
            borderRadius: 4,
            borderWidth: 1,
            borderStyle: 'solid',
            paddingBottom: 10,
            paddingLeft: 15,
            paddingRight: 15,
            paddingTop: 10,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.cp_a01543'),
        icon: 'el-icon-goods',
        value: 'product',
        showIn: ['order'],
        max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.commons.cp_a01543'),
          name: $t('marketing_pd.commons.cp_a01543'),
          type: 'product',
          layout: 'row-image', // row-image|row
          isFormComp: true,
          product: {
            title: $t('marketing_pd.config.qsrspmc_d83187'),
            image: '',
            price: '1.00',
          },
          imageStyle: {
            width: 80,
            height: 80,
            background: '#e9edf5',
            marginRight: 10,
          },
          titleStyle: {
            fontSize: 18,
            color: '#181c25',
            marginBottom: 10,
          },
          priceStyle: {
            color: '#ff8000',
            fontSize: 16,
          },
          style: {
            width: 375,
            height: 93,
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            background: 'rgba(0,0,0,0)',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.config.zfan_134b29'),
        icon: 'el-icon-bank-card',
        value: 'paybutton',
        showIn: ['order'],
        max: 1,
        config: {
          id: '',
          label: $t('marketing_pd.config.zfan_134b29'),
          name: $t('marketing_pd.commons.tj_939d53'),
          tip: $t('marketing_pd.config.zfcg_eb5dc9'),
          type: 'paybutton',
          position: 'none',
          required: false,
          isFormComp: true,
          product: {
            title: $t('marketing_pd.config.qsrspmc_d83187'),
            price: '1.00',
          },
          wrapStyle: {
            position: 'none',
            background: 'rgba(255,255,255,.9)',
          },
          priceStyle: {
            color: '#ff8000',
            fontSize: 16,
            fontWeight: '600',
          },
          style: {
            height: 45,
            width: 345,
            fontSize: 16,
            background: '#409EFF',
            color: '#fff',
            letterSpacing: 0,
            lineHeight: 45,
            textAlign: 'center',
            margin: '0 auto',
            borderWidth: 1,
            borderRadius: 6,
            borderStyle: 'solid',
            borderColor: '#409EFF',
          },
        },
      },
    ],
  },
  {
    title: $t('marketing_pd.commons.grxx_eab129'),
    value: 'personalComp',
    components: [
      {
        title: $t('marketing_pd.commons.tx_4c50ee'),
        icon: 'hexagon-icon hicon-touxiang',
        value: 'image',
        showIn: ['canvas'],
        config: {
          name: $t('marketing_pd.commons.tx_4c50ee'),
          type: 'image',
          fieldName: 'memberAvatar',
          images: [
            {
              url:
                'https://www.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_201912_09_c2cf6114ba424594894e4485209b5366.jpg',
              action: {},
            },
          ],
          imageGap: 4,
          style: {
            display: 'flex',
            width: 60,
            height: 60,
            paddingBottom: 0,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            borderRadius: 60,
            background: 'rgba(255, 255, 255, 0)',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            borderWidth: 2,
            borderStyle: 'solid',
            borderColor: 'rgba(233, 237, 245, 1)',
            left: 20,
            top: 20,
            position: 'absolute',
          },
        },
      },
      {
        title: $t('marketing_pd.commons.nc_23eb0e'),
        icon: 'hexagon-icon hicon-nicheng',
        value: 'text',
        showIn: ['canvas'], // 显示场景
        config: {
          id: '',
          name: $t('marketing_pd.commons.nc_23eb0e'),
          type: 'text',
          value: $t('marketing_pd.commons.nc_23eb0e'),
          fieldName: 'memberName',
          style: {
            paddingBottom: 6,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 6,
            background: 'rgba(255, 255, 255, 0)',
            fontSize: 14,
            borderWidth: 0,
            borderRadius: 0,
            borderStyle: 'none',
            borderColor: '#e9edf5',
            color: '#fff',
            left: 90,
            top: 20,
            position: 'absolute',
            width: 275,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.hydj_6c1583'),
        icon: 'hexagon-icon hicon-dengji',
        value: 'text',
        showIn: ['canvas'], // 显示场景
        config: {
          id: '',
          name: $t('marketing_pd.commons.hydj_6c1583'),
          type: 'text',
          value: '0',
          fieldName: 'memberLevel',
          style: {
            // width: 375,
            // height: 32,
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            // background: "rgba(255, 255, 255, 0)",
            color: '#ff7800',
            fontSize: 14,
            // ...defaultBorderStyle
          },
        },
      },
      {
        title: $t('marketing_pd.config.hyjf_f75eb8'),
        icon: 'hexagon-icon hicon-jifen',
        value: 'text',
        showIn: ['canvas'], // 显示场景
        config: {
          id: '',
          name: $t('marketing_pd.config.hyjf_f75eb8'),
          type: 'text',
          value: '0',
          fieldName: 'memberPoints',
          style: {
            // width: 375,
            // height: 32,
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            background: 'rgba(255, 255, 255, 0)',
            color: '#ff7800',
            fontSize: 14,
            ...defaultBorderStyle,
          },
        },
      },
    ],
  },
  {
    title: $t('marketing_pd.config.yxzj_e42158'),
    value: 'marketingComp',
    components: [
      {
        title: $t('marketing_pd.commons.nr_2d711b'),
        icon: 'el-icon-notebook-2',
        value: 'content',
        showIn: ['page', 'flow'],
        max: 10,
        config: {
          name: $t('marketing_pd.commons.nr_2d711b'),
          type: 'content',
          url: '',
          range: 'site',
          layout: 'big-image-card-layout',
          contentObjectType: 26,
          contentObjectIds: [],
          siteGroupId: '-1', // 微页面分组ID
          titleColor: '#FFFFFF',
          descColor: '#91959E',
          componentBackgroundColor: '#FFFFFF',
          enableSearch: false,
          searchLayout: 'small-round',
          searchPlaceholder: $t('marketing_pd.components.comp.ss_e5f71f'),
          searchBackgroundColor: '#F2F3F5',
          searchBorderColor: '#FFFFFF',
          searchFontColor: '#181C25',
          searchPlaceholderFontColor: '#C1C5CE',
          style: {
            width: 375,
            height: 'auto',
            paddingBottom: 8,
            paddingLeft: 8,
            paddingRight: 8,
            paddingTop: 8,
            gap: 8,
            background: 'rgba(0,0,0,0)',
            boxShadow: '0px 0px 0px rgba(0,0,0,.1)',
            boxShadowLeft: 0,
            boxShadowTop: 0,
            boxShadowRadius: 0,
            boxShadowColor: 'rgba(0,0,0,.1)',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.hd_36c6f5'),
        icon: 'hexagon-icon hicon-huodong14hui',
        value: 'eventslist',
        showIn: ['page', 'flow'],
        max: 10,
        config: {
          name: $t('marketing_pd.commons.hd_36c6f5'),
          type: 'eventslist',
          url: '',
          items: [],
          range: 'all',
          layout: 'big-image-card-layout',
          marketingEventTypes: ['conference', 'live'],
          marketingEventIds: [],
          titleColor: '#181C25',
          descColor: '#91959E',
          componentBackgroundColor: '#FFFFFF',
          enableSearch: false,
          searchLayout: 'small-round',
          searchPlaceholder: $t('marketing_pd.components.comp.ss_e5f71f'),
          searchBackgroundColor: '#F2F3F5',
          searchBorderColor: '#FFFFFF',
          searchFontColor: '#181C25',
          searchPlaceholderFontColor: '#C1C5CE',
          orderType: 0,
          style: {
            width: 375,
            height: 'auto',
            paddingBottom: 8,
            paddingLeft: 8,
            paddingRight: 8,
            paddingTop: 8,
            gap: 8,
            background: 'rgba(0,0,0,0)',
            boxShadow: '0px 0px 0px rgba(0,0,0,.1)',
            boxShadowLeft: 0,
            boxShadowTop: 0,
            boxShadowRadius: 0,
            boxShadowColor: 'rgba(0,0,0,.1)',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.config.gzgzh_8f54bf'),
        icon: 'hexagon-icon hicon-gongzhonghao',
        value: 'followwechat',
        showIn: ['page'],
        max: 1,
        config: {
          name: $t('marketing_pd.config.gzgzh_8f54bf'),
          type: 'followwechat',
          url: '',
          style: {
            // width: 375,
            // height: 84,
            paddingBottom: 10,
            paddingLeft: 10,
            paddingRight: 10,
            paddingTop: 10,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.wz_c75625'),
        icon: 'el-icon-document',
        value: 'article',
        showIn: ['page'],
        config: {
          id: '',
          name: $t('marketing_pd.commons.wz_c75625'),
          type: 'article',
          article: {
            type: 'column', // 三种布局方式, 1.column（垂直布局）2.row（横向布局）3.tile（平铺）
            title: $t('marketing_pd.config.wzbt_7526a0'), // 文章标题
            desc: $t('marketing_pd.config.wzms_edb8b4'), // 文章描述
            author: $t('marketing_pd.config.gly_b1dae9'), // 作者
            time: '2019-11-20', // 时间
            image: '', // 文章图片, 图片地址,
            content: $t('marketing_pd.commons.qsrnr_a11cc7'), // 文章内容，富文本数据
            style: {
              borderRadius: 0,
              background: 'rgba(0,0,0,0)',
              borderColor: 'rgba(0,0,0,0)',
              borderWidth: 1,
              borderStyle: 'solid',
            },
          },
          style: {
            width: 375,
            height: 'auto',
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            background: 'rgba(0,0,0,0)',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing.commons.wj_2a0c47'),
        icon: 'el-icon-notebook-2',
        value: 'document',
        showIn: ['page', 'flow'],
        max: 10,
        config: {
          name: $t('marketing.commons.wj_2a0c47'),
          type: 'document',
          range: 'all',
          contentObjectType: 8,
          contentObjectIds: [],
          items: [],
          siteGroupId: '-1', // 分组ID
          hideWhenEmpty: false,
          enableSearch: false,
          searchLayout: 'small-round',
          searchPlaceholder: $t('marketing_pd.components.comp.ss_e5f71f'),
          searchBackgroundColor: '#F2F3F5',
          searchBorderColor: '#F2F3F5',
          searchFontColor: '#181C25',
          searchPlaceholderFontColor: '#C1C5CE',
          paginationThemeColor: getSystemThemeColor(),
          materialTagFilter: {},
          classification: [],
          optionLook: true,
          lookActionConfig: {
            type: 'file',
            ctaConfig: {},
          },
          optionDownload: true,
          downloadActionConfig: {
            type: 'fileDownloadV2',
            ctaConfig: {},
          },
          optionSendMail: true,
          sendMailActionConfig: {
            type: 'sendToEmail',
            ctaConfig: {},
          },
          email: {
            title: $t('marketing.commons.xzwjtz_3de6b6'),
            sender: '',
            applyUser: '',
            html: defaultFileSendMailTemplate,
          },
          style: {
            width: 375,
            height: 'auto',
            paddingTop: 0,
            paddingRight: 0,
            paddingBottom: 0,
            paddingLeft: 0,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.dh_5a93d3'),
        icon: 'el-icon-phone-outline',
        value: 'tel',
        showIn: ['page'],
        config: {
          id: '',
          label: $t('marketing_pd.commons.dh_5a93d3'),
          name: $t('marketing_pd.commons.bddh_b0ccf0'),
          type: 'tel',
          value: '',
          position: 'none',
          typeValue: 'mobile',
          required: false,
          isFormComp: false,
          wrapStyle: {
            position: 'none',
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            background: 'rgba(255,255,255,.9)',
            zIndex: 9,
          },
          style: {
            height: 45,
            borderRadius: 3,
            fontSize: 16,
            background: '#409EFF',
            color: '#fff',
            letterSpacing: 0,
            lineHeight: 45,
            borderColor: '#333',
            textAlign: 'center',
          },
        },
      },
      {
        title: $t('marketing_pd.config.wxjf_5c76ba'),
        icon: 'el-icon-chat-dot-round',
        value: 'wechat',
        showIn: ['page', 'canvas'],
        config: {
          id: '',
          name: $t('marketing_pd.config.wxjf_5c76ba'),
          type: 'wechat',
          wechat: {
            value: '', // 微信号
            style: {
              color: '#fff',
              background: 'rgb(80, 187, 70)',
            },
          },
          style: {
            // width: 375,
            // height: 68,
            paddingBottom: 6,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 6,
            textAlign: 'center',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.config.gzhxf_373e35'),
        icon: 'hexagon-icon hicon-gongzhonghao',
        value: 'qrcode',
        showIn: ['page', 'canvas'],
        config: {
          name: $t('marketing_pd.config.gzhxf_373e35'),
          type: 'qrcode',
          typeValue: 'wx',
          url: '',
          style: {
            display: 'flex',
            width: 355,
            height: 180,
            paddingBottom: 15,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 15,
            borderRadius: 0,
            background: 'rgba(255, 255, 255, 0)',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.config.qywxxf_23beda'),
        icon: 'hexagon-icon hicon-qiyeweixin',
        value: 'qrcode',
        showIn: ['page', 'canvas'],
        config: {
          name: $t('marketing_pd.config.qywxxf_23beda'),
          type: 'qrcode',
          typeValue: 'wxwork',
          url: '',
          style: {
            display: 'flex',
            width: 355,
            height: 180,
            paddingBottom: 15,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 15,
            borderRadius: 0,
            background: 'rgba(255, 255, 255, 0)',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.commons.ewm_22b03c'),
        icon: 'hexagon-icon hicon-erweima',
        value: 'qrcode',
        showIn: ['page', 'canvas'],
        config: {
          name: $t('marketing_pd.commons.ewm_22b03c'),
          type: 'qrcode',
          typeValue: '',
          qrcodeType: 0, // 0：上传图片 1：生成二维码
          qrcodeValue: '', // 二维码数据
          url: '',
          style: {
            display: 'flex',
            width: 355,
            height: 180,
            paddingBottom: 15,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 15,
            borderRadius: 0,
            background: 'rgba(255, 255, 255, 0)',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            ...defaultBorderStyle,
          },
        },
      },
      {
        title: $t('marketing_pd.config.sphsp_217ce2'),
        icon: 'hexagon-icon hicon-shipinhaoshipin2',
        value: 'wechatVideo',
        showIn: ['page'],
        config: {
          name: $t('marketing_pd.config.sphsp_217ce2'),
          type: 'wechatvideo',
          coverPageType: 'upload', // upload：上传图片 url：生成二维码
          url: '',
          videoNumId: '',
          videoId: '',
          videoName: $t('marketing_pd.config.sph_20f8a1'),
          style: {
            width: 375,
            height: 360,
            paddingTop: '10px',
            paddingBottom: '10px',
          },
        },
      },
      {
        title: $t('marketing_pd.config.sphzy_e14087'),
        icon: 'hexagon-icon hicon-shipinhaozhuye2',
        value: 'videoHomePage',
        showIn: ['page'],
        config: {
          name: $t('marketing_pd.config.sphzy_e14087'),
          type: 'videohomepage',
          associatedAccountId: '',
          style: {
            paddingTop: 15,
            paddingBottom: 15,
            paddingLeft: 10,
            paddingRight: 10,
          },
        },
      },
      {
        title: $t('marketing_pd.config.sphzb_4ecb85'),
        icon: 'hexagon-icon hicon-shipinhaozhibo2',
        value: 'videoLive',
        showIn: ['page'],
        config: {
          name: $t('marketing_pd.config.sphzb_4ecb85'),
          type: 'videolive',
          associatedAccountId: '',
          layout: 0, // 0卡片，1悬浮球，2全屏
          isHide: true,
          fillType: 'color', // color|image
          fillMethod: 'filling', // 填充方式
          wrapStyle: {
            position: 'static',
            zIndex: 10,
            left: 265,
            top: 375,
          },
          style: {
            paddingTop: 15,
            paddingBottom: 15,
            paddingLeft: 10,
            paddingRight: 10,
            backgroundColor: '',
            backgroundImage: '',
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
          },
        },
      },
      // {
      //   title: "直播预约按钮",
      //   icon: "hexagon-icon hicon-zhiboyuyue",
      //   value: "livebutton",
      //   showIn: ["page", "canvas"],
      //   // max: 10,
      //   config: {
      //     ...button,
      //     style: {
      //       ...button.style,
      //       borderRadius: 6
      //     },
      //     type: "livebutton",
      //     label: "直播预约按钮",
      //     name: "直播预约",
      //     required: false,
      //     isFormComp: false,
      //     memberAutoSignup: false,
      //     member: {},
      //     liveId: "",
      //     liveTitle: "",
      //     liveStatus: "before", //直播状态
      //     live: {
      //       before: {
      //         yes: {
      //           name: "已预约",
      //           style: {},
      //           action: {}
      //         },
      //         no: {
      //           name: "立即预约",
      //           style: {},
      //           action: {}
      //         }
      //       },
      //       processing: {
      //         yes: {
      //           name: "观看直播",
      //           style: {},
      //           action: {}
      //         },
      //         no: {
      //           name: "报名并观看",
      //           style: {},
      //           action: {}
      //         }
      //       },
      //       after: {
      //         yes: {
      //           name: "观看回放",
      //           style: {},
      //           action: {}
      //         },
      //         no: {
      //           name: "观看回放",
      //           style: {},
      //           action: {}
      //         }
      //       }
      //     }
      //   }
      // },
      {
        title: $t('marketing_pd.config.zbyyan_41e411'),
        icon: 'hexagon-icon hicon-zhiboyuyue',
        value: 'signupbutton',
        showIn: ['page', 'canvas'],
        // max: 10,
        config: {
          ...signupButtonConfig,
          typeValue: 'live',
          label: $t('marketing_pd.config.zbyyan_41e411'),
          name: $t('marketing_pd.config.zbyy_d24e3f'),
          placeholder: $t('marketing_pd.commons.xzzb_2d7a02'),
          objectName: $t('marketing_pd.commons.zb_7bbe8e'),
          schedule: {
            before: {
              yes: {
                name: $t('marketing_pd.config.yyy_44209e'),
                style: signupButtonItemStyle,
                action: {},
              },
              no: {
                name: $t('marketing_pd.config.ljyy_3ed720'),
                style: signupButtonItemStyle,
                action: {},
              },
            },
            processing: {
              yes: {
                name: $t('marketing_pd.config.gkzb_4dd8d5'),
                style: signupButtonItemStyle,
                action: {},
              },
              no: {
                name: $t('marketing_pd.config.bmbgk_759bbd'),
                style: signupButtonItemStyle,
                action: {},
              },
            },
            after: {
              yes: {
                name: $t('marketing_pd.config.gkhf_58dee7'),
                style: signupButtonItemStyle,
                action: {},
              },
              no: {
                name: $t('marketing_pd.config.gkhf_58dee7'),
                style: signupButtonItemStyle,
                action: {},
              },
            },
          },
        },
      },
      {
        title: $t('marketing_pd.config.hybman_a14bdc'),
        icon: 'hexagon-icon hicon-huiyizhongxinhui',
        value: 'signupbutton',
        showIn: ['page', 'canvas'],
        // max: 10,
        config: {
          ...signupButtonConfig,
          typeValue: 'meeting',
          label: $t('marketing_pd.config.hybman_a14bdc'),
          name: $t('marketing_pd.config.hybm_bb1d71'),
          placeholder: $t('marketing_pd.config.xzhy_a2a320'),
          objectName: $t('marketing_pd.commons.hy_ebcb81'),
          schedule: {
            before: {
              yes: {
                name: $t('marketing_pd.commons.ybm_4166d8'),
                style: signupButtonItemStyle,
                action: {},
              },
              no: {
                name: $t('marketing_pd.config.ljbm_1e6c87'),
                style: signupButtonItemStyle,
                action: {},
              },
            },
            processing: {
              yes: {
                name: $t('marketing_pd.commons.ybm_4166d8'),
                style: signupButtonItemStyle,
                action: {},
              },
              no: {
                name: $t('marketing_pd.config.ljbm_1e6c87'),
                style: signupButtonItemStyle,
                action: {},
              },
            },
            after: {
              yes: {
                name: $t('marketing_pd.config.jchg_f4724e'),
                style: signupButtonItemStyle,
                action: {},
              },
              no: {
                name: $t('marketing_pd.config.jchg_f4724e'),
                style: signupButtonItemStyle,
                action: {},
              },
            },
          },
        },
      },
      {
        title: $t('marketing_pd.config.hdbman_a61f17'),
        icon: 'hexagon-icon hicon-xinhui',
        value: 'signupbutton',
        showIn: ['page', 'canvas'],
        // max: 10,
        config: {
          ...signupButtonConfig,
          typeValue: 'activity',
          label: $t('marketing_pd.config.hdbman_a61f17'),
          name: $t('marketing_pd.config.hdbm_2bc045'),
          placeholder: $t('marketing_pd.config.xzhd_a05f05'),
          objectName: $t('marketing_pd.commons.hd_36c6f5'),
          schedule: {
            before: {
              yes: {
                name: $t('marketing_pd.commons.ybm_4166d8'),
                style: signupButtonItemStyle,
                action: {},
              },
              no: {
                name: $t('marketing_pd.config.ljbm_1e6c87'),
                style: signupButtonItemStyle,
                action: {},
              },
            },
          },
        },
      },
      {
        title: $t('marketing_pd.config.lxyg_d69864'),
        icon: 'hexagon-icon hicon-lianxiyuangong',
        value: 'contact',
        showIn: ['page'],
        // max: 10,
        config: {
          label: $t('marketing_pd.config.lxyg_d69864'),
          name: $t('marketing_pd.config.lxyg_d69864'),
          type: 'contact',
          defaultUid: '',
          defaultFsUid: '',
          defaultAvatar: '',
          required: false,
          isFormComp: false,
          buttonText: $t('marketing_pd.config.zx_a74ccc'),
          buttonStyle: {
            color: '#0C6CFF',
            fontSize: 12,
            fontWeight: 400,
            letterSpacing: 0,
            backgroundColor: '#fff',
            borderColor: '#e9edf5',
            borderStyle: 'solid',
            borderWidth: 1,
          },
          backgroundFillType: 'filling',
          showType: 'text',
          layoutType: 'circle',
          iconInfo: {
            icon: '',
            iconType: 'iconfont',
            iconStyle: {
              color: '',
            },
          },
          wrapStyle: {
            position: 'fixed',
            zIndex: 10,
            left: 310,
            top: 375,
            width: 58,
            height: 58,
          },
          style: {
            width: 58,
            height: 58,
            borderRadius: 58,
            borderStyle: 'solid',
            borderWidth: 1,
            borderColor: '#e9edf5',
            backgroundColor: '#fff',
            boxShadow: '2px 2px 12px rgba(0,0,0,.1)',
            boxShadowLeft: 2,
            boxShadowTop: 2,
            boxShadowRadius: 12,
            boxShadowColor: 'rgba(0,0,0,.1)',
          },
        },
      },
    ],
  },
  {
    title: $t('订货通组件'),
    value: 'dhtComp',
    components: [
      {
        title: $t('优惠券'),
        icon: 'hexagon-icon hicon-youhuiquan',
        value: 'dhtcoupon',
        showIn: ['page'],
        config: {
          id: '',
          name: $t('优惠券'),
          type: 'dhtcoupon',
          // 隐藏样式的边框设置组件
          hideBorder: true,
          // 隐藏样式的边距设置组件
          hidePadding: true,

          // 订货通所有组件都要埋入企业ei
          tenantId: window.CRM ? window.CRM.enterpriseId : '',
          // 订货通所有组件都要埋入企业ea
          ea: window.CRM ? window.CRM.ea : '',
          // 展示的标题
          displayName: $t('优惠券'),
          // 是否显示标题
          isShowDisplayName: true,
          // 优惠券批次列表
          couponList: [],
          styleData: {
            // 排列样式：'1'-横向滑动，'2'-一行两个，'3'-一行一个
            listType: '1',
            // 卡片样式
            cardType: '1',
            // 背景
            background: '#DE302B',
          },
          style: {},
        },
      },
      {
        title: $t('dht.component.product_list'), // 商品列表
        icon: 'hexagon-icon hicon-shangpin',
        value: 'dhtproduct',
        showIn: ['page'],
        config: {
          id: '',
          name: $t('dht.component.product_list'), // 商品列表
          type: 'dhtproduct',
          // 隐藏样式的边框设置组件
          hideBorder: true,
          // 隐藏样式的边距设置组件
          hidePadding: true,

          // 订货通所有组件都要埋入企业ei
          tenantId: window.CRM ? window.CRM.enterpriseId : '',
          // 订货通所有组件都要埋入企业ea
          ea: window.CRM ? window.CRM.ea : '',
          // 展示的标题
          displayName: $t('dht.component.product_list'), // 商品列表
          // 是否显示标题
          isShowDisplayName: true,
          // 商品来源：'list'-选择商品，'tag'-选择标签
          source: 'list',
          // 当来源是[选择商品]时，产品列表数据存储在里
          productList: [],
          // 当来源是[选择标签]时，产品列表数据存储在里
          productListByTag: [],
          // 产品标签
          commodity_label: 'option1',
          // 产品显示数量
          productNumber: 6,
          styleData: {
            // 列表样式：'1'-一行一个，'2'-一行两个，'3'-一行三个
            listType: '3',
            // 加车按钮
            cartBtnType: '1',
            // 页面边距
            pageMargin: 12,
            // 商品边距
            productMargin: 4,
            // 背景
            background: '#DE302B',
          },
          style: {},
        },
      },
      {
        title: $t('促销商品'),
        icon: 'hexagon-icon hicon-cuxiao',
        value: 'dhtpromotionproduct',
        showIn: ['page'],
        config: {
          id: '',
          name: $t('促销商品'),
          type: 'dhtpromotionproduct',
          // 隐藏样式的边框设置组件
          hideBorder: true,
          // 隐藏样式的边距设置组件
          hidePadding: true,

          // 订货通所有组件都要埋入企业ei
          tenantId: window.CRM ? window.CRM.enterpriseId : '',
          // 订货通所有组件都要埋入企业ea
          ea: window.CRM ? window.CRM.ea : '',
          // 展示的标题
          displayName: $t('促销商品'),
          // 是否显示标题
          isShowDisplayName: true,
          // 促销活动列表
          promotionList: [],
          // 促销产品列表
          productList: [],
          // 产品显示数量
          productNumber: 6,
          styleData: {
            // 列表样式：'1'-一行一个，'2'-一行两个，'3'-一行三个
            listType: '3',
            // 加车按钮
            cartBtnType: '1',
            // 页面边距
            pageMargin: 12,
            // 商品边距
            productMargin: 4,
            // 背景
            background: '#DE302B',
          },
          style: {},
        },
      },
    ],
  },
  {
    title: $t('SFA.pagedesigner.comp.name.member_comp'),
    value: 'memberComp',
    components: [
      {
        title: $t('SFA.pagedesigner.comp.name.member_info'),
        icon: 'sfa-marketing-icon-color fx-icon-obj-app139',
        value: 'minivipmemberinfo',
        showIn: ['page'],
        // 最多显示几个
        max: 1,
        config: genContainerConfig(
          {
            id: '',
            name: $t('SFA.pagedesigner.comp.name.member_info'),
            type: 'minivipmemberinfo',
            styleData: {},
            style: {},
            settingData: {
              showIcon: false,
              showLeague: false,
              typeOfLeague: 1,
            },
          },
        ),
      },
      {
        title: $t('SFA.pagedesigner.comp.name.member_level'),
        icon: 'sfa-marketing-icon-color fx-icon-obj-app286',
        value: 'miniviplevel',
        showIn: ['page'],
        // 最多显示几个
        max: 1,
        config: genContainerConfig(
          {
            id: '',
            name: $t('SFA.pagedesigner.comp.name.member_level'),
            type: 'miniviplevel',
            styleData: {},
            style: {},
            settingData: {
              defaultShow: 'join',
              join: {
                showOtherPlan: true,
                switchText: $t('SFA.pagedesigner.record.plan.myplans'),
                LevelText: $t('SFA.pagedesigner.form.level.levelnumber'),
                container: {
                  style: {},
                  visualStyle: {
                    visual: false,
                  },
                },
              },
              nojoin: {
                showOtherPlan: true,
                openText: $t('SFA.pagedesigner.minivip.level.opentips'),
                switchText: $t('SFA.pagedesigner.record.plan.myplans'),
                container: {
                  style: {},
                  visualStyle: {
                    visual: false,
                  },
                },
              },
            },
          },
        ),
      },
      {
        title: $t('SFA.pagedesigner.comp.name.point_detail'),
        icon: 'sfa-marketing-icon-color fx-icon-obj-app143',
        value: 'minivippointsdetails',
        showIn: ['page'],
        // 最多显示几个
        max: 1,
        config: genContainerConfig(
          {
            id: '',
            name: $t('SFA.pagedesigner.comp.name.point_detail'),
            type: 'minivippointsdetails',
            styleData: {},
            style: {},
            typeOfLeague: 1,
          },
        ),
      },
      {
        title: $t('SFA.pagedesigner.comp.name.switch_plan'),
        icon: 'sfa-marketing-icon-color fx-icon-obj-app199',
        value: 'minivipswitchmembershipplan',
        showIn: ['page'],
        // 最多显示几个
        max: 1,
        config: genContainerConfig(
          {
            id: '',
            name: $t('SFA.pagedesigner.comp.name.switch_plan'),
            type: 'minivipswitchmembershipplan',
            styleData: {},
            style: {},
          },
        ),
      },
      {
        title: $t('SFA.pagedesigner.comp.name.switch_store'),
        icon: 'sfa-marketing-icon-color fx-icon-obj-app161',
        value: 'minivipswitchstores',
        showIn: ['page'],
        // 最多显示几个
        max: 1,
        config: genContainerConfig(
          {
            id: '',
            name: $t('SFA.pagedesigner.comp.name.switch_store'),
            type: 'minivipswitchstores',
            styleData: {},
            style: {},
          },
        ),
      },
      {
        title: $t('SFA.pagedesigner.comp.name.coupon'),
        icon: 'sfa-marketing-icon-color fx-icon-obj-app161',
        value: 'minivipcoupon',
        showIn: ['page','flow'],
        // 最多显示几个
        // max: 1,
        config: genContainerConfig(
          {
            id: '',
            name: $t('SFA.pagedesigner.comp.name.coupon'),
            type: 'minivipcoupon',
            styleData: {},
            style: {},
            couponType: 'all',
            settingData: {
              showUseRules: true,
              showCouponBackground: false,
            },
          },
        ),
      },
    ],
  },
]

const getDefaultConfigByValue = (value, key) => {
  for (let i = 0; i < components.length; i += 1) {
    const children = components[i].components

    for (let j = 0; j < children.length; j += 1) {
      const target = children[j]
      if (
        target.value === value
        && (key === undefined || (key !== undefined && target.config.key === key))
      ) {
        return target
      }
    }
  }

  return undefined
}
//获取微页面所有组件的定义，作为AI提示词使用，返回一个字符串
const getAllPageComponentDefinition = () => {
  return components.map(item => item.components).flat().map(item => item.config).map(item =>  `
    ${item.name}组件的数据结构定义：
    \`\`\`json
    ${JSON.stringify(item)}
    \`\`\`
    `).join('\n');
}

//根据组件类型获取组件的默认配置
const getDefaultConfigByType = (matchKeys) => {
  return components.find(item => item.components.find(item => item.config.type === matchKeys.type))?.components.find(item => Object.keys(matchKeys).every(key => item.config[key] === matchKeys[key]))?.config
}

export {
  components,
  button,
  defaultPageData,
  genContainerConfig,
  getDefaultConfigByValue,
  genStepButtonConfig,
  defaultFileSendMailTemplate,
  getAllPageComponentDefinition,
  getDefaultConfigByType,
  suspensionButtonConfig,
}
