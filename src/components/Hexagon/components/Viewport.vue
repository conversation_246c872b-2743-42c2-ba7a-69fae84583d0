<template>
  <div ref="viewport" :class="[$style.Viewport, 'hexagon__viewport', { [$style['Viewport--agent']]: contextProps.isAgent }]">
    <Tabs
      v-model="layoutTabValue"
      :class="[
        $style.Viewport__tabs,
        designMode === 'form-page' ? $style.hideTab : ''
      ]"
      type="border-card"
      @tab-remove="removeTab"
      @tab-click="handleTabClick"
    >
      <TabPane
        v-for="item in layoutTabs"
        :closable="item.name !== 'page'"
        :key="item.name"
        :label="
          item.name === 'page' ? designer.pageData.name || $t('marketing_pd.commons.ym_59ceff') : item.title
        "
        :name="item.name"
      >
        <component v-if="item.name === layoutTabValue" :ref="'container'" v-bind:is="item.component"></component>
        <div style="height: 90px" v-if="item.component === 'page'"></div>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import { mapState, mapActions } from "vuex";
// import selectable from 'vue-selectable';
import Page from "./Page.vue";
import Canvas from "./Canvas.vue";
import Grid from './Grid.vue';

// Vue.directive('selectable', selectable)

export default {
  components: {
    Tabs: FxUI.Tabs,
    TabPane: FxUI.TabPane,
    Page,
    Canvas,
    Grid
  },
  computed: {
    ...mapState("hexagon", {
      layoutTabs: "layoutTabs",
      designer: "designer",
      designMode: "designMode",
      contextProps: "contextProps",
    }),
    layoutTabValue: {
      get() {
        return this.$store.state.hexagon.layoutTabValue;
      },
      set(val) {
        this.setTabValue(val);
      }
    }
  },
  watch: {
    layoutTabValue(name) {
      this.$nextTick(() => this.tabChange(name));
    }
  },
  methods: {
    ...mapActions("hexagon", ["setTabValue", "removeTab", "tabChange"]),
    handleTabClick(vnode) {
      //重新计算容器边距
      this.$nextTick(() => {
          if(this.$refs.container[vnode.index - 0]) {
            this.$refs.container[vnode.index - 0].calculateOffset()
          }
      });
    }
  }
};
</script>
<style lang="less" module>
.Viewport {
  height: calc(~"100vh - 54px");
  flex: 1;
  min-width: 400px;
  overflow: hidden;
  &--agent {
    height: 100%;
    :global {
    .el-tabs__content {
      height: 100%!important;
    }
  }
  }
  .Viewport__tabs {
    height: 100%;
    &.hideTab {
      :global {
        .el-tabs__nav-wrap {
          display: none;
        }
      }
    }
  }
  :global {
    .el-tabs--border-card {
      border: 0;
      background-color: #fafafa;
      .el-tabs__header {
        background: #f2f2f2 !important;
        border-color: #e9edf5;
        height: 49px;
        .el-tabs__item {
          height: 50px;
          line-height: 50px;
          font-size: 13px;
          max-width: 130px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          color: #91959e;
          &.is-active {
            background-color: #fafafa;
            color: #181c25;
          }
        }
      }
    }
    .el-tabs__content {
      position: unset;
      padding: 0 !important;
      height: calc(~"100vh - 104px");
      overflow-y: auto !important;
    }
  }
  .contextmenu {
    width: 110px;
    padding: 10px 0;
    position: fixed;
    top: 150px;
    left: 50%;
    z-index: 99999;
    background-color: #fff;
    border: 1px solid #e9edf5;
    color: #181c25;
    font-size: 14px;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.08);
    .optionmenu {
      line-height: 30px;
      padding: 0 15px;
      cursor: pointer;
      &.disable {
        color: #999;
        cursor: default;
        &:hover {
          background: none;
        }
      }
      &:hover {
        background-color: #f2f2f2;
      }
    }
  }
}
</style>
