<template>
  <div :class="[$style.Designer, 'hexagon__designer']">
    <Side v-bind="$attrs" v-on="$listeners"/>
    <Viewport v-loading="loading"/>
  </div>
</template>
<script>
import Side from './Side.vue';
import Viewport from './Viewport.vue';
import designer from '../mixin/designer';
import event from '../mixin/event';
import canvas from '../mixin/canvas';

export default {
  components: {
    Side,
    Viewport,
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [designer, event, canvas],
  data() {
    return {};
  },
  computed: {
    designer() {
      return this.$store.state.hexagon.designer;
    },
    components() {
      return this.$store.state.hexagon.components.lists;
    },
  },
};
</script>
<style lang="less" module>
.Designer {
  flex: 1;
  display: flex;
  overflow: hidden;
}
</style>

