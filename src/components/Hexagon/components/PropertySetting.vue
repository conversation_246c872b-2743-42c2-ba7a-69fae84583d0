<template>
  <div :class="[$style.PropertySetting, $style.show, 'hexagon__settting_wrap', { [$style['PropertySetting--agent']]: contextProps.isAgent }]">
    <Setting
      v-model="settingType"
      :title="settingTitle"
      :data="propertySetting"
    >
      <div
        v-if="propertySetting.type !== 'page'"
        :class="[$style.setting_con, 'hexagon__setting_body']"
      >
        <!-- <VText v-if="propertySetting.type === 'text'" :data="propertySetting" @change="handlePropertyChange"/> -->
        <component
          :is="components[propertySetting.type]"
          v-if="components[propertySetting.type]"
          :class="$style.component_wrap"
          :data="propertySetting"
          :edit-type="editType"
          :layout-tab-type="layoutTabType"
          :setting-type="settingType"
          v-bind="$attrs"
          v-on="$listeners"
          @change="handlePropertyChange"
        >
          <slot
            slot="beforecontent"
            name="beforecontent"
            :data="propertySetting"
          />
          <slot
            slot="content"
            slot-scope="scope"
            :data="scope.data"
          />
          <slot
            slot="aftercontent"
            name="aftercontent"
            :data="propertySetting"
          />
          <!-- 如果scope带有传入对应样式则触发change方法 -->
          <template
            slot="style:after"
            slot-scope="scope"
          >
            <template v-if="canShowInputStyle">
              <InputStyle
                :value="scope.inputStyle"
                :comp-data="propertySetting"
                @input="(data) => scope.change('inputStyle', data)"
              />
            </template>
            <template v-if="canShowPosition">
              <Position
                v-if="scope.positionStyle"
                :value="scope.positionStyle"
                @input="(data) => scope.change('position', data)"
              />
              <Position
                v-else
                v-model="style"
              />
            </template>
            <template v-if="canShowBorder">
              <Border
                v-if="scope.borderStyle"
                :value="scope.borderStyle"
                @input="(data) => scope.change('border', data)"
              />
              <Border
                v-else
                v-model="style"
              />
            </template>
            <template v-if="canShowPadding">
              <Padding
                v-if="scope.paddingStyle"
                :value="scope.paddingStyle"
                :show-gap-setting="showGapSetting"
                @input="(data) => scope.change('padding', data)"
              />
              <Padding
                v-else
                v-model="style"
                :show-gap-setting="showGapSetting"
              />
            </template>
          </template>
        </component>
        <!-- <VContainer v-if="propertySetting.type === 'container'" :data="propertySetting" @change="handlePropertyChange"/> -->
      </div>
      <div
        v-else
        :class="[$style.setting_con, 'hexagon__setting_body']"
      >
        <div style="padding: 16px 14px 0;">
          <Alert
            :title="$t('marketing_pd.components.PropertySetting.djzjszdyzj_cd5ba9')"
            type="warning"
          />
        </div>
        <!-- 页面属性设置 -->
        <VPage
          :data="propertySetting"
          :setting-type="settingType"
          v-bind="$attrs"
          v-on="$listeners"
          @change="handlePropertyChange"
        >
          <slot
            slot="beforecontent"
            name="beforecontent"
            :data="propertySetting"
          />
          <slot
            slot="content"
            slot-scope="scope"
            :data="scope.data"
          />
          <slot
            slot="aftercontent"
            name="aftercontent"
            :data="propertySetting"
          />
        </VPage>
      </div>
    </Setting>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import Setting from './common/Setting.vue'
import InputStyle from './common/InputStyle.vue'
import Position from './common/Position.vue'
import Padding from './common/Padding.vue'
import Border from './common/Border.vue'
import VPage from './setting/page/index.vue'
import VContainer from './setting/container/index.vue'
import VText from './setting/text/index.vue'
import VImage from './setting/image/index.vue'
import VSlider from './setting/slider/index.vue'
import VInput from './setting/input/index.vue'
import VRadio from './setting/radio/index.vue'
import VButton from './setting/button/index.vue'
import VWechat from './setting/wechat/index.vue'
import VLine from './setting/line/index.vue'
import VRectangle from './setting/rectangle/index.vue'
import VBlank from './setting/blank/index.vue'
import VGridcontainer from './setting/gridcontainer/index.vue'
import VArticle from './setting/article/index.vue'
import VTel from './setting/tel/index.vue'
import VVideo from './setting/video/index.vue'
import VPaybutton from './setting/paybutton/index.vue'
import VProduct from './setting/product/index.vue'
import VRegion from './setting/region/index.vue'
import VQrcode from './setting/qrcode/index.vue'
import VAgreement from './setting/agreement/index.vue'
import VFile from './setting/file/index.vue'
import VSuspension from './setting/suspension/index.vue'
import VMenu from './setting/menu/index.vue'
import VFollowWechat from './setting/followwechat/index.vue'
import VEventsList from './setting/eventslist/index.vue'
import VWechatVideo from './setting/wechatvideo/index.vue'
import VVideoHomePage from './setting/videohomepage/index.vue'
import VVideoLive from './setting/videolive/index.vue'
import VContact from './setting/contact/index.vue'
import VDhtcoupon from './setting/dht/dhtcoupon/index.vue'
import VDhtproduct from './setting/dht/dhtproduct/index.vue'
import VDhtpromotionproduct from './setting/dht/dhtpromotionproduct/index.vue'
import VStepButton from './setting/stepbutton/index.vue'
import VDocument from './setting/document/index.vue'
import VImageCaptcha from './setting/imagecaptcha/index.vue'
import VFileUpload from './setting/fileupload/index.vue'
import VMiniVipLevel from './setting/miniVip/miniVipLevel/index.vue'
import VMiniVipMemberInfo from './setting/miniVip/miniVipMemberInfo/index.vue'
import VMiniVipPointsDetails from './setting/miniVip/miniVipPointsDetails/index.vue'
import VMiniVipSwitchMemberShipPlan from './setting/miniVip/miniVipSwitchMemberShipPlan/index.vue'
import VMiniVipSwitchStores from './setting/miniVip/miniVipSwitchStores/index.vue'
import VMiniVipCoupon from './setting/miniVip/miniVipCoupon/index.vue'

// 组件maps
const components = {
  VContainer,
  VGridcontainer,
  VPage,
  VText,
  VImage,
  VVideo,
  VSlider,
  VInput,
  VDate: VInput,
  VTime: VInput,
  VLocation: VInput,
  VRadio,
  VCheckbox: VRadio,
  VButton,
  VWechat,
  VLine,
  VRectangle,
  VBlank,
  VArticle,
  VTel,
  VPaybutton,
  VProduct,
  VRegion,
  VQrcode,
  VAgreement,
  VFile,
  VSuspension,
  VMenu,
  VFollowWechat,
  VEventsList,
  VWechatVideo,
  VVideoHomePage,
  VVideoLive,
  VContact,
  VDhtcoupon,
  VDhtproduct,
  VDhtpromotionproduct,
  VStepButton,
  VDocument,
  VImageCaptcha,
  VFileUpload,
  VMiniVipMemberInfo,
  VMiniVipLevel,
  VMiniVipPointsDetails,
  VMiniVipSwitchMemberShipPlan,
  VMiniVipSwitchStores,
  VMiniVipCoupon,
}

let prevPropertySettingData = {}
export default {
  components: {
    ...components,
    Setting,
    InputStyle,
    Position,
    Padding,
    Border,
    Alert: FxUI.Alert,
  },
  data() {
    return {
      components: Object.keys(components).reduce(
        (map, key) => {
          map[key.toLocaleLowerCase().substr(1)] = key
          return map
        },
        {
          content: VEventsList,
          cascade: VRadio,
          livebutton: VButton,
          signupbutton: VButton,
        },
      ),
      settingType: 'content',
      position: {
        left: 0,
        top: 0,
      },
    }
  },
  computed: {
    ...mapState('hexagon', {
      layoutTabValue: 'layoutTabValue',
      designMode: 'designMode',
      contextProps: 'contextProps',
    }),
    ...mapState('hexagon/designer', {
      propertySetting: state => state.propertySetting || {},
    }),
    layoutTabType() {
      return this.$store.state.hexagon.layoutTabType
    },
    editType() {
      if (this.layoutTabValue === 'page') {
        return 'page'
      }

      return this.layoutTabType === 'grid' ? 'grid' : 'canvas'
    },
    settingTitle() {
      if (this.designMode === 'form-page') {
        return $t('marketing_pd.components.PropertySetting.bdsz_1f23e0')
      }
      const {
        type, label, name, conference,
      } = this.propertySetting || {}
      if (type === 'article' && conference) {
        return $t('marketing_pd.components.PropertySetting.hyxqsz_b3bda5')
      }
      return type === 'page' ? $t('marketing_pd.components.PropertySetting.ymsz_a018ef') : (label || name) + (FS.userLanguage === 'en' ? ' ' : '') +$t('marketing_pd.commons.sz_e366cc')
    },
    style: {
      get() {
        prevPropertySettingData = this.propertySetting
        return this.propertySetting.style
      },
      set(val) {
        // 防止在切换组件时，组件样式搞错组件的问题
        if (prevPropertySettingData.id !== this.propertySetting.id) {
          return
        }
        this.handlePropertyChange({
          ...this.propertySetting,
          style: {
            ...this.propertySetting.style,
            ...val,
          },
        })
      },
    },
    canShowInputStyle() {
      return (
        ['input', 'date', 'radio', 'checkbox', 'region', 'cascade'].indexOf(
          this.propertySetting.type,
        ) !== -1
      )
    },
    canShowPosition() {
      // 容器下，flow布局不显示坐标轴设置
      if (this.layoutTabType === 'flow' && this.layoutTabValue !== 'page') {
        return false
      }
      return this.layoutTabValue !== 'page'
    },
    // 是否显示边框设置
    canShowBorder() {
      const { type, layout } = this.propertySetting
      if (['content', 'eventslist'].indexOf(type) > -1 && layout === 'row') {
        return false
      }

      if (['video', 'rectangle', 'stepbutton', 'button', 'imagecaptcha', 'document'].includes(type)) {
        return false
      }

      return !this.propertySetting.hideBorder
    },
    // 是否显示边距设置
    canShowPadding() {
      const { type, layoutType } = this.propertySetting

      if (type === 'video' && layoutType === 'icon-only') {
        return false
      }

      // 容器下，flow布局不显示内边距设置
      // 放开简易模式下面边距设置
      // if(this.layoutTabType === 'flow' && this.layoutTabValue !== "page"){
      //   return false
      // }
      return !this.propertySetting.hidePadding && ['button', 'stepbutton', 'tel', 'rectangle'].indexOf(type) === -1
    },
    showGapSetting() {
      return ['content', 'eventslist'].indexOf(this.propertySetting.type) > -1
    },
  },
  watch: {
    // propertySetting(){
    //   console.log(this.propertySetting, 'watch setting')
    // },
    'propertySetting.id': {
      handler() {
        this.settingType = 'content'
      },
    },
    'propertySetting.type': {
      handler(val) {
        this.settingType = ['rectangle'].indexOf(val) === -1 ? 'content' : 'style'
      },
    },
    'propertySetting.style': {
      handler(style) {
        const { left = 0, top = 0 } = style || {}
        this.position = {
          left: left || 0,
          top: top || 0,
        }
      },
    },
  },
  methods: {
    ...mapActions('hexagon/designer', ['updatePageComp']),
    ...mapActions('hexagon/grid', ['updatePropertyById']),
    ...mapActions('hexagon/canvas', ['setCanvasStyle', 'updateCanvasComp']),
    handlePositionChange() {
      this.handlePropertyChange({
        ...this.propertySetting,
        style: {
          ...this.propertySetting.style,
          ...this.position,
        },
      })
    },
    handlePropertyChange(data) {
      /**
       * data.type等于page时，配置页面内容
       * tabsValue等于page时，页面布局下组件内容设置
       * data.type等于container时，修改自定义布局样式信息
       * 其他情况下时，编辑自定义布局内组件信息
       */
      console.log('handlePropertyChange:', data.type, this.layoutTabValue, data)
      if (data.type === 'page' || this.layoutTabValue === 'page') {
        this.updatePageComp(data)
      } else if (this.layoutTabType === 'grid') {
        this.updatePropertyById(data)
      } else if (data.type === 'container') {
        this.setCanvasStyle(data)
      } else {
        this.updateCanvasComp(data)
      }
    },
  },
}
</script>
<style lang="less" module>
.PropertySetting {
  background-color: #fff;
  border-left: 1px solid #e9edf5;
  overflow: hidden;
  transition: width 0.5s ease;
  overflow: hidden;
  width: 380px;
  height: calc(~"100vh - 54px");
  &--agent {
    height: 100%;
    .setting_con {
      height: calc(~"100vh - 154px");
    }
  }
  .setting_con {
    padding: 20px 14px 20px 14px;
    height: calc(~"100vh - 104px");
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 40px;

    :global {
      //颜色选择器修改
      .el-color-picker--mini {
        height: 20px;
        .el-color-picker__trigger {
          width: 70px;
          height: 20px;
          padding: 0;
          border-color: #e9edf5;
        }
        .el-color-picker__color {
          width: 50px;
          border: 0;
          height: 18px;
          .el-color-picker__empty {
            display: none;
          }
        }
        .el-color-picker__icon {
          display: inline-block !important;
          width: 20px;
          top: 0;
          right: 0;
          left: auto;
          transform: translate3d(0, 0, 0);
          color: #333;
          height: 18px;
          line-height: 18px;
          border-left: 1px solid #e9edf5;
          background: #fff;
        }
      }
      //radio 样式
      .el-radio {
        .el-radio__label {
          font-size: 12px;
          color: #545861;
        }
      }
      .el-checkbox {
        .el-checkbox__label {
          font-size: 12px;
          color: #545861;
        }
      }
    }
  }
  :global {
    .hexagon__style_setting {
      padding: 10px 15px;
      background-color: #fafafa;
      border-radius: 3px;
      border: 1px solid #eaeef5;
    }
    .hexagon__setting_item {
      -moz-user-select: text;
      -webkit-user-select: text;
      -ms-user-select: text;
      user-select: text;
      margin-bottom: 20px;

      &:last-of-type {
        margin-bottom: 0;
      }
      .hexagon__setting_title {
        color: #181c25;
        font-size: 12px;
        margin-bottom: 10px;
        &-right {
          float: right;
          color: var(--color-info06,#407FFF);
          cursor: pointer;
        }
      }
      .hexagon__setting_desc{
        color:#91959e;
        font-size:12px;
        margin-top: -2px;
        margin-bottom: 10px;
        span{
           color: var(--color-info06,#407FFF);
           cursor: pointer;
        }
      }
      .hexagon__setting_con {
        .el-slider {
          margin-top: -10px;
          margin-bottom: -10px;
        }
      }
      &.hexagon__setting_row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        &:last-of-type {
          margin-bottom: 0;
        }
        .hexagon__setting_title {
          width: 100px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 0;
          margin-top: -3px;
        }
        .hexagon__setting_con {
          flex: 1;
          min-width: 0;
          text-align: right;
          margin-top: 0;
          line-height: 0.9;
        }
      }
      :global {
        .el-input,
        .el-select {
          width: 100%;
          // margin-bottom: 10px;
          .el-select {
            margin: -10px -20px;
          }
          .el-input {
            margin-bottom: 0;
          }
        }
      }
    }

    .hexagon__setting_body.reset {
      padding: 0;

      .hexagon__card {
        padding-left: 14px;
        padding-right: 14px;
      }

      .hexagon__setting_item {
        align-items: stretch;

        .hexagon__setting_con {
          text-align: left;
          display: flex;
          justify-content: center;
          flex-direction: column;
        }

        &.hexagon__setting_row {
          .hexagon__setting_title {
            width: 140px;
            margin-top: 0;
            padding: 4px 0;
            line-height: 20px;
            overflow: inherit;
            word-wrap: normal;
            text-overflow: initial;
            white-space: inherit;
          }
        }
      }
    }
    .ql-toolbar.ql-snow {
      border-color: #eaeef5;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
    }
    .ql-container.ql-snow {
      border-color: #eaeef5;
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      display: flex;
      flex-direction: column;
    }
    .ql-editor {
      padding: 5px 10px;
      flex: 1;
    }
    .ql-editor ol,
    .ql-editor ul {
      padding-left: 0.7em;
    }
    .el-radio-button__inner {
      padding: 8px 20px;
    }
    .el-collapse {
      border: 0;
      .el-collapse-item__header,
      .el-collapse-item__wrap {
        border-bottom: 0;
      }
    }
    .el-input-group > .el-input__inner {
      vertical-align: top;
    }
    .el-slider__input {
      width: 40px;
      .el-input__inner {
        height: 20px;
        line-height: 20px;
        padding: 0 5px !important;
      }
    }
    .el-slider__runway.show-input {
      margin-right: 56px;
    }
    .el-slider__button-wrapper {
      top: -16px;
    }
    .el-slider__runway,
    .el-slider__bar {
      height: 4px;
    }
    .el-slider__bar {
      background-color: var(--color-primary06,#ff8000);
    }
    .el-slider__button {
      width: 10px;
      height: 10px;
      border: solid 2px var(--color-primary06,#ff8000);
      background-color: #ffffff;
    }

    //布局样式
    .hexagon__layout {
      .hexagon__layout_item {
        margin-bottom: 20px;
        cursor: pointer;
        .hexagon__layout_title {
          font-size: 12px;
          color: #545861;
          .hexagon__layout_radio {
            float: right;
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #fff;
            border: 1px solid #e9edf5;
            border-radius: 2px;
            color: #fff;
            text-align: center;
            i {
              font-weight: bold;
              opacity: 0;
              transition: all 0.3s ease;
            }
            &.hexagon__layout_checked {
              background: var(--color-primary06,#ff8000);
              border-color: var(--color-primary06,#ff8000);
              i {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
}
</style>
