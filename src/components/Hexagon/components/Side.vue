<template>
  <div :class="$style.Side">
    <div :class="$style.header">
      <!-- <div :class="$style.title">{{sidebarTabType === 'page' ? '页面':'组件'}}</div> -->
      <div :class="$style.tabs">
        <Tabs
          v-model="sidebarTabType"
          :item="navTabs"
          @input="(val) => $emit('page:tabchange', val)"
        />
        <!-- <el-radio-group size="mini" v-model="sidebarTabType" @change="(val) => $emit('page:tabchange', val)">
          <el-radio-button label="component">组件</el-radio-button>
          <el-radio-button label="page">页面</el-radio-button>
        </el-radio-group> -->
      </div>
    </div>
    <div
      :class="[
        $style.tabs_content,
        sidebarTabType === 'page' ? $style.has_bottom : '',
        { [$style['tabs_content--agent']]: contextProps.isAgent },
      ]"
    >
      <div :class="$style.scroll_content">
        <div
          v-if="sidebarTabType === 'page'"
          :class="$style.page_lists"
        >
          <div
            v-for="(item, i) in pages"
            :id="item.id"
            :key="i"
            :class="[
              $style.item,
              'hexagon__page-item',
              editPageId == item.id ? $style.active : '',
            ]"
            @click="_handleEditPage(i)"
          >
            <div :class="$style.page_name">
              <i
                v-if="item.isHomepage == 1"
                :class="'el-icon-house'"
                style="margin-right: 3px;"
              />{{ item.name }}
            </div>
            <div :class="$style.page_option">
              <i
                :class="[$style.icon, 'el-icon-edit']"
                :title="$t('marketing_pd.components.Side.xgmc_54a3dd')"
                @click.stop="_handlePageOption('rename', i)"
              />
              <i
                :class="[$style.icon, 'el-icon-copy-document']"
                :title="$t('marketing_pd.commons.fz_79d3ab')"
                @click.stop="_handlePageOption('copy', i)"
              />
              <!-- <i :class="[$style.icon, 'el-icon-view']" @click.stop="_handlePageOption('preview', i)" title="预览"></i> -->
              <i
                v-if="item.isHomepage !== 1"
                :class="[$style.icon, 'el-icon-delete']"
                :title="$t('marketing_pd.commons.sc_2f4aad')"
                @click.stop="_handlePageOption('delete', i)"
              />
              <i
                v-if="item.isHomepage !== 1"
                :class="[$style.icon, 'hexagon-icon hicon-shouye3hui1']"
                style="font-size:15px"
                :title="$t('marketing_pd.components.Side.swsy_35fe6c')"
                @click.stop="_handlePageOption('setHomePage', i)"
              />
            </div>
          </div>
          <div
            v-if="!pages.length"
            :class="$style.empty_page"
          >
            {{ $t('marketing_pd.components.Side.ymkk_114a55') }}
          </div>
        </div>
        <Collapse
          v-if="sidebarTabType === 'component'"
          v-model="activeNames"
          :class="[$style.components__wrap, 'components__wrap']"
        >
          <template v-for="(item, pindex) in components">
            <CollapseItem
              v-if="!item.hide"
              :key="item.value"
              :title="item.title"
              :name="item.value"
            >
              <div
                v-if="item.value === 'formComp' && exceedsMaxLimit"
                style="margin-right: 11px;margin-bottom: 10px;"
              >
                <Alert
                  :title="
                    $t('marketing_pd.components.Side.mqbdjzctjg_e34224', {data: ({'option0': formCompMaxLimit})})
                  "
                  type="warning"
                />
              </div>
              <div :class="$style.components">
                <template v-for="(comp, index) in item.components">
                  <div
                    v-if="!comp.hide"
                    :key="index"
                    :class="[
                      $style.component__item,
                      comp.disable ? $style.disable : 'hexagon__comp_candrag',
                      'hexagon__comp',
                      'hexagon__comp_drag',
                    ]"
                    :data-key="pindex + '-' + index"
                    @click="handleCompListClick(comp)"
                  >
                    <div
                      v-if="
                        comp.disable && typeof comp.updateMessage === 'function'
                      "
                      :class="$style.tipWrap"
                      @click="comp.updateMessage"
                    >
                      <img
                        :class="$style.tipIcon"
                        :src="crownIcon"
                      >
                    </div>
                    <div :class="$style.item_inner">
                      <img
                        v-if="compIcons[comp.icon]"
                        :class="['hexagon__component_icon']"
                        :src="compIcons[comp.icon]"
                      >
                      <i
                        v-else
                        :class="['hexagon__component_icon', comp.icon]"
                      />
                      <p
                        :class="[
                          $style.component__name,
                          'hexagon__component_name',
                        ]"
                      >
                        {{ comp.title }}
                      </p>
                    </div>
                  </div>
                </template>
              </div>
            </CollapseItem>
          </template>
        </Collapse>
      </div>
    </div>
    <div
      v-if="sidebarTabType === 'page'"
      :class="$style.fixed_bottom"
    >
      <Button
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click="_handlePageOption('create')"
      >
        {{ $t('marketing_pd.components.Side.xjym_880a0e') }}
      </Button>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import Tabs from './common/Tabs.vue'
import { createId, deepClone } from '../utils'
import crownIcon from '../assets/crown-icon.png'
// import stepFormIcon from "../assets/step-form-icon.png";
// import qrcodeIcon from "../assets/qrcode.png";
// import wechatQrcodeIcon from "../assets/wechat-qrcode.png";
// import wxworkQrcodeIcon from "../assets/wxwork-qrcode.png";
// import agreementIcon from "../assets/agreement-icon.png";
// import cascadeIcon from "../assets/cascade-icon.png";

const defaultLeft = {
  image: 0,
  slider: 0,
  line: 0,
  qrcode: 10,
  imagecaptcha: 0,
  fileupload: 0,
}

export default {
  components: {
    Collapse: FxUI.Collapse,
    CollapseItem: FxUI.CollapseItem,
    Button: FxUI.Button,
    Tabs,
    Alert: FxUI.Alert,
  },
  props: {
    hidePageList: Boolean, // 是否显示页面列表
  },
  data() {
    return {
      compIcons: {
        // "step-form-icon": stepFormIcon,
        // "qrcode-icon": qrcodeIcon,
        // "wechat-qrcode-icon": wechatQrcodeIcon,
        // "wxwork-qrcode-icon": wxworkQrcodeIcon,
        // "agreement-icon": agreementIcon,
        // "cascade-icon": cascadeIcon
      },
      crownIcon,
      activeNames: [
        'baseComp',
        'superComp',
        'marketingComp',
        'formComp',
        'personalComp',
        'dhtComp',
        'memberComp',
      ],
      tabs: [
        { label: $t('marketing_pd.commons.ym_59ceff'), value: 'page' },
        { label: $t('marketing_pd.components.Side.zj_bb7966'), value: 'component' },
      ],
    }
  },
  computed: {
    ...mapState('hexagon', {
      layoutTabValue: 'layoutTabValue',
      pages: 'pages',
      editPageId: 'editPageId',
      contextProps: 'contextProps',
      navTabs() {
        if (this.hidePageList) {
          return this.tabs.filter(item => item.value !== 'page')
        }
        return this.tabs
      },
      components: ({ components }) => components.lists,
      exceedsMaxLimit: ({ components }) => components.exceedsMaxLimit,
      formCompMaxLimit: ({ components }) => components.formCompMaxLimit,
    }),
    ...mapState('hexagon/designer', {
      pageCompList: 'pageCompList',
    }),
    ...mapState('hexagon/canvas', {
      compData: 'compData',
      canvasComps: 'comp',
    }),
    ...mapState('hexagon/grid', {
      gridCompData: 'compData',
    }),
    sidebarTabType: {
      get() {
        return this.$store.state.hexagon.sidebarTabType
      },
      set(val) {
        this.setTabType(val)
      },
    },
  },
  watch: {
    async editPageId(nextId, id) {
      const data = await this.getPageData()
      this.$emit('page:change', nextId, id, data)
      if (nextId !== id) {
        this.removeAllTab()
      }
    },
  },
  methods: {
    ...mapActions('hexagon/designer', {
      getPageData: 'getPageData',
      addPageComp: 'addPageComp',
    }),
    ...mapActions('hexagon', {
      setTabType: 'setTabType',
      removeAllTab: 'removeAllTab',
    }),
    ...mapActions('hexagon/canvas', ['addCanvasComp']),
    ...mapActions('hexagon/grid', ['addGridComp']),
    handleCompListClick(comp) {
      if (comp.disable) return
      const newComp = deepClone(comp)
      if (this.layoutTabValue === 'page') {
        // 生成页面布局组件
        this.addPageComp({
          ...newComp.config,
          sort: this.pageCompList.length,
          id: createId(),
        })
      } else if (this.layoutTabValue === this.gridCompData.id) {
        // 在flow布局下，如果是图片上传组件，就添加一个标志，用于区分是多图还是单图模式。
        if (newComp.config.type === 'file' && newComp.config.typeValue === 'image') {
          newComp.config.fileList = [{
            customFieldName: newComp.config.customFieldName || newComp.config.fieldName,
            fieldName: newComp.config.fieldName,
            name: newComp.config.name,
            placeholderImage: newComp.config.placeholderImage,
            required: newComp.config.required,
          }]
        }
        this.addGridComp({
          ...newComp.config,
          id: createId(),
        })
      } else {
        const { layout, slideIndex, typesetting } = this.compData
        let insertIndex
        const currComps = layout === 'multiple' ? this.canvasComps[slideIndex].components : this.canvasComps
        // flow布局插入表单控件默认在提交按钮之前
        let mytypesetting = typesetting
        if (layout === 'multiple') {
          mytypesetting = this.canvasComps[slideIndex].typesetting
        }

        if (newComp.config.isFormComp && mytypesetting === 'flow') {
          insertIndex = currComps.findIndex(comp => ['button', 'stepbutton'].includes(comp.type) && comp.isFormComp)
          if (insertIndex === -1) {
            insertIndex = undefined
          }
          // 在flow布局下，如果是图片上传组件，就添加一个标志，用于区分是多图还是单图模式。
          if (newComp.config.type === 'file' && newComp.config.typeValue === 'image') {
            newComp.config.fileList = [{
              customFieldName: newComp.config.customFieldName || newComp.config.fieldName,
              fieldName: newComp.config.fieldName,
              name: newComp.config.name,
              placeholderImage: newComp.config.placeholderImage,
              required: newComp.config.required,
            }]
          }
        }
        // 用来修复绝对定位时，图片上传组件marginLeft 直接变成paddingLeft问题
        if (mytypesetting !== 'flow' && newComp.config.type === 'file' && newComp.config.typeValue === 'image') {
          newComp.config.style.marginLeft = 0
        }

        // 生成canvas布局组件
        this.addCanvasComp({
          ...newComp.config,
          sort: insertIndex !== undefined ? insertIndex : currComps.length,
          id: createId(),
          style: {
            ...newComp.config.style,
            left: defaultLeft[newComp.config.type] !== undefined ? defaultLeft[newComp.config.type] : 15,
            ...(currComps.length ? {} : { top: 42 }),
          },
        })
      }
    },
    _handlePageOption(type, i) {
      this.$emit(
        'page:operation',
        type,
        i === undefined ? this.pages : this.pages[i],
      )
    },
    _handleEditPage(i) {
      this.$emit('page:click', i, this.pages[i])
    },
  },
}
</script>
<style lang="less" module>
.Side {
  width: 270px;
  background-color: #fff;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.08);
  border-right: 1px solid #e9edf5;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  :global {
    .el-radio-button__inner {
      padding: 8px 20px;
    }
    .el-collapse {
      border: 0;
      .el-collapse-item__header,
      .el-collapse-item__wrap {
        border-bottom: 0;
      }
    }
  }
  .header {
    .title {
      font-size: 12px;
      color: #181c25;
      height: 39px;
      line-height: 39px;
      padding-left: 20px;
      border-bottom: 1px solid #e9edf5;
    }
  }
  .tabs_content {
    height: calc(~"100vh - 104px");
    position: relative;
    overflow: hidden;
    &:hover {
      overflow-y: auto;
    }
    &.has_bottom {
      height: calc(~"100vh - 154px");
    }
    &--agent {
      height: 100%;
      &.has_bottom {
        height: 100%;
      }
    }
  }
  .scroll_content {
    width: 270px;
    padding-left: 9px;
    padding-bottom: 10px;
  }
  // .tabs {
  //   text-align: center;
  // }
  .page_lists {
    padding-top: 20px;
    padding-right: 9px;
    .empty_page {
      text-align: center;
      color: #91959e;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .item {
      display: flex;
      position: relative;
      cursor: pointer;
      border: 1px solid #e9edf5;
      margin-bottom: 10px;
      border-radius: 3px;
      line-height: 35px;
      &.active {
        // border-color:#407FFF;
        .page_name {
          color: var(--color-info06,#407FFF);
        }
      }
    }
    .page_name {
      flex: 1;
      padding-left: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .page_option {
      padding: 0 8px;
      .icon {
        color: #888;
        &:hover {
          color: var(--color-info06,#407FFF);
        }
      }
    }
  }
  .fixed_bottom {
    border-top: 1px solid #e9edf5;
    height: 50px;
    line-height: 50px;
    text-align: center;
  }
  .components {
    display: flex;
    flex-wrap: wrap;
    .component__item {
      position: relative;
      .tipWrap {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 100%;
      }
      .tipIcon {
        width: 16px;
        position: absolute;
        top: 5px;
        right: 5px;
        z-index: 1;
      }
      .item_inner {
        height: 100%;
        width: 100%;
      }
      &.disable {
        .item_inner {
          opacity: 0.3;
          cursor: no-drop;
        }
      }
      // width: 77px;
      // height: 77px;
      // display: flex;
      // flex-direction: column;
      // align-items: center;
      // justify-content: center;
      // background-color: #fafafa;
      // border: 1px solid #e9edf5;
      // margin-right: 5px;
      // margin-bottom: 5px;
      // cursor: pointer;
      // border-radius: 3px;
      // transition: background .2s ease;
      // &:nth-child(3n){
      //   margin-right: 0;
      // }
      // .component__icon{
      //   font-size: 20px;
      //   color: #407FFF;
      // }
      // &:hover{
      //   background-color: rgba(142,180,255,.2);
      // }
    }
  }
}
:global {
  .hexagon__comp_drag {
    width: 80px;
    height: 80px;
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    // justify-content: center;
    text-align: center;
    background-color: #fafafa;
    border: 1px solid #e9edf5;
    margin-right: 5px;
    margin-bottom: 5px;
    cursor: pointer;
    border-radius: 3px;
    transition: background 0.2s ease;
    &:nth-child(3n) {
      margin-right: 0;
    }
    .hexagon__component_icon {
      width: 30px;
      height: 19px;
      overflow: hidden;
      font-size: 20px;
      color: var(--color-info06,#407FFF);
      margin-top: 16px;
      -webkit-user-drag: none;
    }
    .sfa-marketing-icon-color {
      &::before {
        color: var(--color-info06,#407FFF);
      }
    }
    .hexagon__component_name {
      font-size: 12px;
      color: #181c25;
      line-height: 16px;
    }
    &:hover {
      background-color: rgba(142, 180, 255, 0.2);
    }
    &.drag {
      width: 50px;
      height: 50px;
      opacity: 0.8;
      -moz-user-select: none;
      -webkit-user-select: none;
      -ms-user-select: none;
      user-select: none;
      .hexagon__component_icon {
        margin-top: 6px;
      }
      .hexagon__component_name {
        flex: 1;
        display: block;
        font-size: 10px;
        color: #181c25;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
</style>
