<template>
  <div :class="[$style.container, { [$style['container--agent']]: contextProps.isAgent }]">
    <Header @save="handleSave" @finish="handleFinish" @close="$emit('close')">
      <slot name="header:title" slot="title"></slot>
      <slot name="header:content" slot="content"></slot>
      <slot name="header:right" slot="right"></slot>
      <!-- <div slot="extend-right" :class="$style.ai_create" @click="handleAiCreate">
        <img :src="'https://a2.fspage.com/FSR/weex/avatar/marketing_app/images/ai-slogan-avatar.svg'" />
        <span>AI生成</span>
      </div> -->
    </Header>
    <div :class="$style.contet">
      <Designer v-bind="$attrs" v-on="$listeners" :loading="loading" :hidePageList="hidePageList"
        @page:click="handlePageClick" />
      <PropertySetting v-bind="$attrs" v-on="$listeners">
        <!-- 设置内容前置 -->
        <slot name="setting:beforecontent" slot="beforecontent" slot-scope="scope" :data="scope.data" />
        <!-- 设置内容区域 -->
        <slot name="setting" slot-scope="scope" :data="scope.data" />
        <!-- 设置内容后置 -->
        <slot name="setting:aftercontent" slot="aftercontent" slot-scope="scope" :data="scope.data" />
      </PropertySetting>
      <!-- <div :class="$style.ai_site_generator" v-if="aiSiteGeneratorVisible">
        <SiteChat :sessionId="sessionId" :existing-data="existingData" @generated="handleGenerated" />
      </div> -->
    </div>
  </div>
</template>

<script>
// import { mapActions } from 'vuex';
import hexagon from "../mixin/hexagon";
// import SiteChat from "@/components/AiGenerator/components/site/SiteChat.vue";

export default {
  mixins: [hexagon],
  // components: {
  //   SiteChat
  // },
  // data() {
  //   return {
  //     existingData: null,
  //     aiSiteGeneratorVisible: false,
  //     sessionId: '',
  //   };
  // },
  // methods: {
  //   ...mapActions('hexagon/designer', {
  //     getPageData: 'getPageData',
  //     editPage: "editPage",
  //   }),
  //   handleAiCreate() {
  //     if(this.aiSiteGeneratorVisible) {
  //       this.aiSiteGeneratorVisible = false;
  //       return;
  //     }
  //     this.getPageData().then(data => {
  //       console.log('data', data, 999);
  //       this.existingData = data;
  //       const { id, enterpriseAccount } = FS.contacts.getCurrentEmployee() || {}
  //       this.sessionId = `${enterpriseAccount}-${id}-${data.id}`;
  //       this.aiSiteGeneratorVisible = true;
  //     });
  //   },
  //   handleGenerated({ prompt, pageData }) {
  //     // this.$emit("confirm", {
  //     //   aiPrompt: prompt,
  //     //   pageData: pageData,
  //     //   existingData: this.existingData
  //     // });
  //     this.editPage({
  //       ...this.existingData,
  //       ...pageData
  //     });
  //     this.existingData = null;
  //   }
  // }
};
</script>

<style lang="less" module>
@import url(../assets/hexagon.less);
.container{
  .ai_create {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;

    img {
      width: 16px;
      height: 16px;
    }

    span {
      margin-left: 4px;
      background: radial-gradient(110% 110% at 16.75% 100%, #0099FF 0%, #A033FF 60%, #FF5280 90%, #FF7061 100%);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
      font-size: 14px;
      line-height: 14px;
    }
  }
  .ai_site_generator {
    width: 375px;
    height: calc(100vh - 54px);
  }
}

</style>
