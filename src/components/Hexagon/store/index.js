import components from './components.js'
import designer from './designer.js'
import canvas from './canvas.js'
import grid from './grid'
import mutations from './mutations.js'
import { rootDispatch } from '../utils/index.js'

const getInitialState = () => ({
  /**
   * 设计模式
   * 1.multi-page:多页面设计
   * 2.single-page:单页面设计
   * 3.form-page: 表单页面设计
   */
  designMode: 'multi-page',
  /**
   * 表单支持flow布局的小程序版本号
   */
  supportFlowFormMpVersion: location.href.match(/localhost|.ceshi112.com/) !== null ? '9.8.0':'9.9.0',
  /**
   * 页面列表
   */
  /**
   * 所有布局tab列表，默认打开页面布局tab
   */
  layoutTabs: [
    {
      title: $t('marketing_pd.commons.ymbj_8b519c'),
      name: 'page',
      type: 'page',
      component: 'Page',
    },
  ],
  /**
   * 布局模式tab类型
   * page:页面布局模式
   * 其他值代表画布布局模式
   */
  layoutTabValue: 'page',
  /**
   * 画布布局模式下，画布类型值，form:表单|personal:个人信息|order:订单|flow:流式布局等等
   */
  layoutTabType: '',
  /**
   * 设计器左侧工具栏tab，component|page
   */
  sidebarTabType: 'component',
  /**
   * 多页面模式下，所有页面列表
   */
  pages: [],
  /**
   * 多页面模式下，当前选中编辑的页面
   */
  editPageId: '',
  globalData: {},
  //省市区数据
  regionData: {},
  //是否被CTA使用
  usedByCta: false,
  // 上下文参数
  contextProps: {},
})

export const state = () => getInitialState()

const actions = {
  setDesignerType({ commit }, designMode) {
    commit('setState', {
      designMode,
    })
  },
  setLayoutTabType({ commit }, layoutTabType) {
    commit('setState', {
      layoutTabType,
    })
    // 更新可选择组件列表
    rootDispatch('render').call(this, 'components')
  },
  setPages({ commit }, pages) {
    commit('setState', {
      pages,
    })
  },
  setPageConfig({ state: prevState, commit }, payload) {
    commit('setState', {
      ...prevState,
      ...payload,
    })
  },
  updatePage({ commit, state: { pages, editPageId }}, pageData) {
    //通过pageId更新page数据
    const newPages = pages.map(page => {
      if (page.id === editPageId) {
        page = {
          ...page,
          ...pageData,
          content: JSON.stringify(pageData || {}),
        }
      }
      return page
    })
    console.log('newPages updatePage:', editPageId, newPages);
    commit('setState', {
      pages: newPages,
    })
  },
  setEditPageId({ commit }, editPageId) {
    commit('setState', {
      editPageId,
    })
  },
  setTabValue({ commit }, layoutTabValue) {
    commit('setState', {
      layoutTabValue,
    })
  },
  setTabType({ commit, dispatch }, sidebarTabType) {
    commit('setState', {
      sidebarTabType,
    })
    if (sidebarTabType === 'component') {
      // 重新计算组件数量
      dispatch('components/calcCompCount')
    }
  },
  addTab({ state: prevState, commit, dispatch }, { name, type, index }) {
    const layoutTabs = [...prevState.layoutTabs]
    const isInTabs = prevState.layoutTabs.some(item => item.name === name)
    if (!isInTabs) {
      layoutTabs.push({
        title: `${$t('marketing_pd.commons.bj_5aefca')}-${(index - 0 + 1)}`,
        name,
        type,
        component: 'Canvas',
      })
      commit('setState', {
        layoutTabs,
      })
    }
    commit('setState', {
      layoutTabValue: name,
      layoutTabType: type,
    })

    setTimeout(() => {
      // 解决自动生成组件无法关联的问题
      dispatch('canvas/renderRelatedMaps')
    }, 100)
  },
  addTabInGrid({ state: prevState, commit, dispatch }, { name, type, index, parent }){
    const layoutTabs = [...prevState.layoutTabs]
    const isInTabs = prevState.layoutTabs.some(item => item.name === name);
    if (!isInTabs) {
      layoutTabs.push({
        title: `${$t('marketing_pd.commons.bj_5aefca')}-${(parent.sort - 0 + 1)}-${(index)}`,
        name,
        type,
        parent,
        component: 'Canvas',
      })
      commit('setState', {
        layoutTabs,
      })
    }
    commit('setState', {
      layoutTabValue: name,
      layoutTabType: type,
    })
    setTimeout(() => {
      // 解决自动生成组件无法关联的问题
      dispatch('canvas/renderRelatedMaps')
    }, 100)
  },
  addGridTab({ state: prevState, commit, dispatch }, { name, type, index }) {
    const layoutTabs = [...prevState.layoutTabs]
    const isInTabs = prevState.layoutTabs.some(item => item.name === name)
    if (!isInTabs) {
      layoutTabs.push({
        title: `${$t('marketing_pd.commons.bj_5aefca')}-${(index - 0 + 1)}`,
        name,
        type,
        component: 'Grid',
      })
      commit('setState', {
        layoutTabs,
      })
    }
    commit('setState', {
      layoutTabValue: name,
      layoutTabType: type,
    })
  },
  editTab({ state: prevState, commit }, { name, data }) {
    const tabs = prevState.layoutTabs
    commit('setState', {
      layoutTabs: tabs.map(item => {
        if (item.name === name) {
          item = {
            ...item,
            ...data,
          }
        }
        return item
      }),
    })
  },
  removeTab({ state: prevState, commit }, targetName) {
    const tabs = prevState.layoutTabs
    let activeName = prevState.layoutTabValue
    if (activeName === targetName) {
      tabs.forEach((tab, index) => {
        if (tab.name === targetName) {
          const nextTab = tabs[index + 1] || tabs[index - 1]
          if (nextTab) {
            activeName = nextTab.name
          }
        }
      })
    }
    commit('setState', {
      layoutTabValue: activeName,
      layoutTabs: tabs.filter(tab => tab.name !== targetName),
    })
  },
  removeAllTab({ state: prevState, commit }) {
    const [firstTab] = prevState.layoutTabs
    commit('setState', {
      layoutTabValue: firstTab.name,
      layoutTabs: [firstTab],
    })
  },
  tabChange({ state: prevState, commit, dispatch }, name) {
    if (name !== 'page') {
      let newType = 'grid';
      let parent = null;
      prevState.layoutTabs.forEach(item => {
        if (item.name === name) {
          newType = item.type;
          parent = item.parent;
        }
      });
      commit('setState', {
        layoutTabType: newType,
      });
      // 此数pageCanvas 可以为画布，也可以为grid布局
      let pageCanvas = this.state.hexagon.designer.pageMaps[name] || {}
      // 如果有父组件。在父组件下面找。
      if(parent) {
        let parentNode = this.state.hexagon.designer.pageMaps[parent.id];
        if(parentNode.components && parentNode.components.length) {
          parentNode.components.map((item)=>{
            if(item.id === name) {
              pageCanvas = item;
            }
          })
        }
      }
      if (newType === 'grid') {
        commit('grid/setState', {
          comp: pageCanvas.components || [],
          compData: pageCanvas,
          gridStyle: {
            ...pageCanvas.style
          }
        })
        commit('canvas/setState', {
          comp: [],
          compData: {},
        })
      }else {
        let currCanvas = pageCanvas
        if (pageCanvas.layout === 'multiple') {
          currCanvas = pageCanvas.components[pageCanvas.current]
        }
        if (currCanvas && !currCanvas.style.width) {
          currCanvas.style.width = 375
        }
        commit('canvas/setState', {
          comp: pageCanvas.components || [],
          canvasStyle: (currCanvas && currCanvas.style) || {},
          compData: pageCanvas,
        })
        commit('grid/setState', {
          comp: [],
          compData: {},
        })
      }
    } else {
      commit('canvas/setState', {
        comp: [],
        compData: {},
      })
      commit('grid/setState', {
        comp: [],
        compData: {},
      })
    }
    // 切换tab重新生成组件列表
    dispatch('renderCompLists')

    if (name !== 'page') {
      setTimeout(() => {
        // 初始化历史记录状态
        dispatch('canvas/initHistoryRecord')
      }, 100)
    }
  },
  renderCompLists({ dispatch }) {
    dispatch('components/render')
  },
  //设置省市区数据
  setRegionData({ commit }, regionData) {
    console.log('setRegionData:', regionData);
    commit('setState', {
      regionData,
    })
  },
  resetStore({ commit }) {
    commit('RESET')
    commit('components/RESET')
    commit('canvas/RESET')
    commit('designer/RESET')
  },
}

export default {
  name: 'hexagon',
  namespaced: true,
  state,
  mutations: mutations(getInitialState()),
  actions,
  modules: {
    components,
    designer,
    canvas,
    grid,
  },
}
