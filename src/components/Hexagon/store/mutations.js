export default function (initialState) {
  return {
    setState: Object.assign,
    RESET(state) {
      state = Object.assign(state, initialState)
    },
    setGlobalData(state, payload) {
      state.globalData = Object.assign(state.globalData, payload.globalData)
    },
    setContextProps(state, payload) {
      state.contextProps = Object.assign(state.contextProps, payload.contextProps)
    },
  }
}
