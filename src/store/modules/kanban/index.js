import http from "@/services/http";
import {
  getMenuById,
  setMenuById
} from "@/modules/menu/menu";
// import Model from './model';
// import Tag from './tag';
// import { mock_kanbanId, mock_kanbanList, mock_kanbanDetail } from './mock.js';

export default {
  namespaced: true,
  modules: {
    // Model,
    // Tag,
  },
  state: {
    kanbanTemplates: [], // 看板的系统模板列表
    enterpriseTemplates: [], // 看板的企业模板列表
    kanbanList: [], // 看板列表
    kanbanListVersion: 0, // 看板列表的排序版本
    kanbanId: '', // 当前显示的看板id，会做localstorage缓存
    kanbanDetail: {}, // 当前显示的看板详情
    kanbanDetailLoading: true, // 看板详情是否加载中
    kanbanBoardUsers: [], // 看板的可见用户Id列表
    cardDragging: false, // 卡片是否处于拖拽状态
  },
  mutations: {
    setState: Object.assign
  },
  actions: {
    async listBoardTemplate({
      commit
    }, payload) {
      const res = await http.listBoardTemplate();
      if (res && res.errCode === 0) {
        commit('setState', {
          kanbanTemplates: res.data
        })
      }
      return res;
    },
    async listEnterpriseBoardTemplate({
      commit
    }, payload) {
      const res = await http.listEnterpriseBoardTemplate();
      if (res && res.errCode === 0) {
        commit('setState', {
          enterpriseTemplates: res.data.map(item => ({
            ...item,
            templateType: 1, // 企业模板
          })),
        })
      }
      return res;
    },
    async listBoardByFsUserId({
      commit
    }, payload) {
      const res = await http.listBoardByFsUserId();
      if (res && res.errCode === 0) {
        commit('setState', {
          kanbanList: res.data,
        })
      }
      return res;
    },
    async listEmployeeIdByBoardId({
      commit
    }, payload) {
      const res = await http.listEmployeeIdByBoardId({
        boardId: payload,
      });
      if (res && res.errCode === 0) {
        commit('setState', {
          kanbanBoardUsers: res.data,
        })
      }
      return res;
    },
    async getBoardDetail({
      dispatch,
      commit
    }, payload) {
      !payload.withoutLoading && commit('setState', {
        kanbanDetailLoading: true,
      })
      const res = await http.getBoardDetail({
        boardId: payload.id,
      });
      if (res && res.errCode === 0) {
        if (res.data.templateType == 0) {
          res.data.haveAuthority = true //看板默认都能编辑
        }
        commit('setState', {
          kanbanDetail: res.data,
        })
        dispatch('setKanbanDetail', res.data);
      }
      commit('setState', {
        kanbanDetailLoading: false,
      })
      return res;
    },
    async getBoardCardDetail({
      dispatch,
      commit
    }, payload) {
      const res = await http.getBoardCardDetail({
        boardCardId: payload,
      });
      if (res && res.errCode === 0) {}
      return res;
    },

    // 列表拖拽顺序
    async updateDisplayOrder({
      dispatch,
      state
    }, payload) {
      const res = await http.updateDisplayOrder({
        displayKey: `CARD_LIST_IN_BOARD_${state.kanbanId}`,
        version: state.kanbanListVersion,
        displayItems: payload,
      });
      if (res.errCode === 0) dispatch('getBoardDetail', {
        id: state.kanbanId,
        withoutLoading: true
      });
      return res;
    },
    // 卡片拖拽排序
    async moveBoardCard({
      dispatch,
      commit
    }, payload) {
      const res = await http.moveBoardCard(payload);
      if (res && res.errCode === 0) {}
      return res;
    },
    setKanbanId({
      dispatch,
      commit
    }, payload) {
      commit('setState', {
        kanbanId: payload,
      })
      // localStorage.setItem('app_marketing_kanban_id', payload);
      dispatch('getBoardDetail', {
        id: payload
      });
      // vibe marketing 冲突，暂时不调转
      // this.router.push({
      //   name: "kanban-detail",
      //   params: {
      //     id: payload
      //   }
      // })
    },
    setKanbanIdNotRedirect({
      dispatch,
      commit
    }, payload) {
      commit('setState', {
        kanbanId: payload,
      })
      // localStorage.setItem('app_marketing_kanban_id', payload);
      dispatch('getBoardDetail', {
        id: payload
      })
    },
    setKanbanDetail({
      commit
    }, payload) {
      commit('setState', {
        kanbanDetail: JSON.parse(JSON.stringify(payload)),
        kanbanBoardUsers: payload.boardUsers || [],
        kanbanListVersion: payload.boardCardListVersion,
      })
    },
    setKanbanDetailLoading({
      commit
    }, payload) {
      commit('setState', {
        kanbanDetailLoading: payload,
      })
    },
    setCardDragging({
      commit
    }, payload) {
      commit('setState', {
        cardDragging: payload,
      })
    }
  }
};
