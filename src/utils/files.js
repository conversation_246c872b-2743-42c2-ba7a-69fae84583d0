import axios from "axios";
import _ from "lodash";

/**
 * 选择文件
 * @param {*} params 
 * @returns 
 */
export function pickFile(params = {}) {
  let { multiple, accept } = params;
  return new Promise((resolve, reject) => {
    let input = document.createElement("input");
    input.setAttribute("type", "file");
    multiple && input.setAttribute("multiple", true);
    accept && input.setAttribute("accept", accept);
    input.click();
    input.addEventListener("change", listener);

    function listener(e) {
      console.log(e);
      let files = e.target.files;
      files = Array.from(files);
      resolve(files);

      input.removeEventListener("change", listener);
      input = null;
    }
  });
}

/**
 * 上传后台文件
 * @param {*} files 
 * @returns 
 */
export function getTNPath(files, options = {}) {
  if (!_.isArray(files) || files.length === 0) return;
  const uploadUrl = options.cdn ? FS.BASE_PATH + "/FSC/EM/File/UploadByForm?needCdn=true" : FS.BASE_PATH + "/FSC/EM/AFile/UploadByForm";
  let requests = [];
  files.forEach(file => {
    requests.push(
      new Promise((resolve, reject) => {
        let formData = new FormData();
        formData.append("file", file);
        formData.append(
          "extension",
          file.name.substring(file.name.lastIndexOf(".") + 1)
        );
        axios
          .post(uploadUrl, formData)
          .then(res => {
            if (!res || !res.data) {
              reject(res);
            }
            resolve({
              path: res.data.TempFileName,
              name: file.name.substring(0, file.name.lastIndexOf(".")),
              ext: res.data.FileExtension,
              size: file.size
            });
          })
          .catch(err => {
            reject(err);
          });
      })
    );
  });
  return Promise.all([...requests]);
}

/**
 * 限制最大文件大小
 */
export function passMaxSize(size, files) {
  let tSize = files.reduce((pre, cur) => pre + cur.size, 0);
  /**
   * 默认限制传入单位为KB
   */
  return size * 1000 < tSize;
}
