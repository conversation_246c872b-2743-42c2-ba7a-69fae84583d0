<template>
  <div class="kanban" v-loading="model_kanbanDetailLoading">
    <div class="kanban__header">
      <!-- 看板标题展示及编辑器 -->
      <KanbanTitle @onClickTitleIcon="handleSwitchKanbanDialog"></KanbanTitle>
      <div class="header-margin"></div>
      <PublicSelector v-if="model_kanbanDetail.haveAuthority"></PublicSelector>
      <MemberSelector v-if="model_kanbanDetail.haveAuthority"></MemberSelector>
      <KanbanOverview v-if="model_templateType === 0" :boardId="id"></KanbanOverview>
      <KanbanFullscreen></KanbanFullscreen>
      <KanbanMoreOperation
        v-if="model_kanbanDetail.haveAuthority"
        @deleteBoard="deleteBoard"
      ></KanbanMoreOperation>
    </div>
    <div class="kanban__body">
      <!-- 看板集合 -->
      <KanbanCollection
        ref="kanbanCollection"
        :editable="model_kanbanDetail.haveAuthority"
        :boardCardLists="model_boardCardLists"
        @click_kanbanCard="handleClickKanbanCard"
      ></KanbanCollection>
    </div>
    <SwitchKanbanDialog
      :visible.sync="isShowSwitchKanbanDialog"
      @update:submit="handleSwitchKanban"
      @update:create="handleChooseTemplate"
    ></SwitchKanbanDialog>
    <ChooseTemplateDialog
      :visible="isShowChooseTemplateDialog"
      @update:visible="isShowChooseTemplateDialog = false"
      @update:submit="handleChooseTemplateSubmit"
    ></ChooseTemplateDialog>
    <CreateKanbanDialog
      :defaultData="createKanbanDefaultData"
      :visible="isShowCreateKanbanDialog"
      @update:visible="isShowCreateKanbanDialog = false"
      @update:submit="handleCreateKanban"
    ></CreateKanbanDialog>
    <Detail
      :visible.sync="isShowDetailDialog"
      :id="curCardId"
      @refresh="handleDetailRefresh"
    ></Detail>
  </div>
</template>

<script>
import http from "@/services/http/index";

import KanbanTitle from "../components/kanban-title";
import PublicSelector from "../components/public-selector";
import MemberSelector from "../components/member-selector";
import KanbanFullscreen from "../components/kanban-fullscreen";
import KanbanOverview from "../components/kanban-overview";
import KanbanMoreOperation from "../components/kanban-operation";
import SwitchKanbanDialog from "../components/switch-kanban-dialog";
import KanbanCollection from "../components/kanban-collection";
import ChooseTemplateDialog from "../components/choose-template-dialog";
import CreateKanbanDialog from "../components/create-kanban-dialog";
import Detail from "../components/detail";
import { mapState, mapActions } from "vuex";
export default {
  components: {
ElButton: FxUI.Button,
KanbanTitle,
PublicSelector,
MemberSelector,
KanbanFullscreen,
KanbanOverview,
KanbanCollection,
SwitchKanbanDialog,
ChooseTemplateDialog,
CreateKanbanDialog,
Detail,
KanbanMoreOperation
},
  props: {
    isAgent: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      title: {
        text: $t('marketing.commons.yyjh_942f80'),
        needIcon: true
      },
      currentMenuIndex: 0,
      createKanbanDefaultData: {},

      isShowSwitchKanbanDialog: false,
      isShowChooseTemplateDialog: false,
      isShowCreateKanbanDialog: false,
      isShowDetailDialog: false,

      curCardId: null // 当前选中的cardId
    };
  },
  computed: {
    model_name() {
      return this.$store.state.Kanban.kanbanDetail.name;
    },
    model_templateType() {
      return this.$store.state.Kanban.kanbanDetail.templateType;
    },
    urlKanbanId() {
      return this.$route.params.id || this.id
    },
    model_kanbanList() {
      return this.$store.state.Kanban.kanbanList || [];
    },
    model_kanbanId() {
      return this.$store.state.Kanban.kanbanId;
    },
    model_kanbanDetail() {
      console.log(
        "kanban-index model_kanbanDetail",
        this.$store.state.Kanban.kanbanDetail
      );
      return this.$store.state.Kanban.kanbanDetail || {};
    },
    model_boardCardLists() {
      return this.model_kanbanDetail.boardCardLists || [];
    },
    model_kanbanDetailLoading() {
      return this.$store.state.Kanban.kanbanDetailLoading;
    }
  },
  watch: {},
  async mounted() {
    this.beforeLoad();
    this.setKanbanDetailLoading(true);
    this.listBoardTemplate();
    this.listEnterpriseBoardTemplate();
    await this.listBoardByFsUserId();
    this.loadKanban();
  },
  methods: {
    ...mapActions("Kanban", [
      "listBoardTemplate",
      "listEnterpriseBoardTemplate",
      "listBoardByFsUserId",
      "getBoardDetail",
      "getBoardCardDetail",
      "setKanbanDetailLoading",
      "setKanbanId",
      "setKanbanDetail"
    ]),
    deleteBoard() {
      this.$router.push({
        name: "kanban",
        query: { type: this.model_templateType == 1 ? "Template" : "Board" }
      });
    },
    // 加载看板
    loadKanban() {
      this.initKanbanId();
    },
    // 设置看板Id (规则优先级：1.读取url上的 2.若不匹配1，读取上一次的 3.若不匹配1、2，则显示第一条)
    async initKanbanId() {
      const urlKanbanId = this.urlKanbanId;
      // const lastKanbanId = localStorage.getItem("app_marketing_kanban_id");
      // let kanbanId;
      // if (
      //   urlKanbanId &&
      //   this.model_kanbanList.filter(item => item.id === urlKanbanId).length > 0
      // ) {
      //   kanbanId = urlKanbanId;
      // } else if (
      //   lastKanbanId &&
      //   this.model_kanbanList.filter(item => item.id === lastKanbanId).length >
      //     0
      // ) {
      //   kanbanId = lastKanbanId;
      // } else {
      //   kanbanId = this.model_kanbanList[0].id;
      // }
      this.setKanbanId(urlKanbanId);
    },
    // 切换看板
    handleSwitchKanbanDialog() {
      this.isShowSwitchKanbanDialog = true;
    },
    // 选择看板模板
    handleChooseTemplate() {
      this.isShowChooseTemplateDialog = true;
      this.$nextTick(() => {
        this.isShowSwitchKanbanDialog = false;
      });
    },
    // 新建看板
    handleChooseTemplateSubmit(data) {
      this.createKanbanDefaultData = { ...data, isTemplate: true };
      this.isShowCreateKanbanDialog = true;
      this.$nextTick(() => {
        this.isShowChooseTemplateDialog = false;
      });
    },
    // 新建看板完毕
    handleCreateKanban(newKanbanId) {
      // this.listBoardByFsUserId();
      // this.setKanbanId(newKanbanId);
      // this.$nextTick(() => {
      //   this.isShowCreateKanbanDialog = false;
      // });
    },
    handleSwitchKanban() {},
    // 点击看板卡片
    handleClickKanbanCard(cardId) {
      console.log("handleClickKanbanCard", cardId);
      this.curCardId = cardId;
      this.isShowDetailDialog = true;
    },
    // 看板卡片详情页做了变更后 { type, cardId, cardDetail }
    async handleDetailRefresh({ type, cardId, cardDetail }) {
      console.log(
        "handleDetailRefresh",
        type,
        cardId,
        cardDetail,
        this.model_kanbanDetail
      );
      this.updateCardDetailOnKanbanDetail(type, cardId, cardDetail);
      // 删除时，更新看板列表的任务数
      this.listBoardByFsUserId();
    },
    async updateCardDetailOnKanbanDetail(type, cardId, cardDetail) {
      let index_boardCardLists; // 目标卡片在看板的哪一列
      let index_boardCards; // 目标卡片在该列的哪一个
      (this.model_kanbanDetail.boardCardLists || []).forEach(
        (list, listIndex) => {
          (list.boardCards || []).forEach((card, cardIndex) => {
            if (card.id === cardId) {
              index_boardCardLists = listIndex;
              index_boardCards = cardIndex;
            }
          });
        }
      );
      if (index_boardCardLists === null || index_boardCards === null) return;
      if (type === "update") {
        const newBoardCardLists = this.model_kanbanDetail.boardCardLists;
        // 先用详情页传出来的数据渲染一次
        newBoardCardLists[index_boardCardLists].boardCards[
          index_boardCards
        ] = cardDetail;
        this.setKanbanDetail({
          ...this.model_kanbanDetail,
          boardCardLists: newBoardCardLists
        });
        // 再用接口请求的数据渲染一次
        const res = await this.getBoardCardDetail(cardId);
        newBoardCardLists[index_boardCardLists].boardCards[index_boardCards] =
          res.data;
        this.setKanbanDetail({
          ...this.model_kanbanDetail,
          boardCardLists: newBoardCardLists
        });
      } else if (type === "delete") {
        const newBoardCardLists = this.model_kanbanDetail.boardCardLists;
        newBoardCardLists[index_boardCardLists].boardCards.splice(
          index_boardCards,
          1
        );
        this.setKanbanDetail({
          ...this.model_kanbanDetail,
          boardCardLists: newBoardCardLists
        });
      } else {
        this.getBoardDetail({ id: this.model_kanbanId, withoutLoading: true });
      }
    },
    beforeLoad() {
      if (this.$route.params.id === "create") {
        this.isShowChooseTemplateDialog = true;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.kanban {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 1s ease 0s;
  .title__type {
    font-size: 12px;
    color: #879eb8;
    border: 1px solid #ced5de;
    border-radius: 2px;
    padding: 0 8px;
  }
  .header-margin {
    flex: 1;
  }
  .kanban__header {
    display: flex;
    height: 56px;
    align-items: center;
    border-bottom: 1px solid #e9edf5;
    background: #fff;
    padding: 0 20px;
    .header__btn {
      margin-left: auto;
    }
  }
  .kanban__body {
    flex: 1;
    overflow: auto;
  }
}
</style>
