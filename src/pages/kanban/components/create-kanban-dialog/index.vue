<template>
  <div>
    <v-dialog
      :title="$t('marketing.commons.kb_c668b1', {data: ( {'option0': kanbanForm.id ? $t('marketing.commons.bj_95b351') : $t('marketing.commons.cj_d9ac92')})})"
      append-to-body
      class="create-kanban-dialog"
      width="705px"
      @onClose="handleCloseDialog"
      @onSubmit="handleSubmitDialog"
      :loading="loading"
      :visible="visible"
      @opened="handleSelectName"
    >
      <el-form
        :model="kanbanForm"
        :rules="rules"
        ref="kanbanForm"
        class="template-form"
        label-width="110px"
        label-position="left"
      >
        <el-form-item :label="$t('marketing.pages.kanban.kbmc_cd2a09')" prop="name">
          <fx-input
            :placeholder="$t('marketing.commons.qsrkbmc_921185')"
            v-model="kanbanForm.name"
            maxlength="50"
            show-word-limit
            ref="nameInput"
          ></fx-input>
        </el-form-item>
        <el-form-item :label="$t('marketing.commons.kjfw_f96495')" prop="visibleRage">
          <el-radio-group v-model="kanbanForm.visibleRage">
            <el-radio
              v-for="item in publicTypeList"
              :key="item.id"
              :label="item.id"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="$t('marketing.commons.cy_ab5dea')"
          prop="boardUserIds"
          v-show="kanbanForm.visibleRage === 'private'"
        >
          <!-- <div class="kanban__principal"></div> -->
          <StaffSelectorInput
            @change="handleStaffSelectorChange"
            :defaultSelectedItems="{ member: kanbanForm.boardUserIds }"
            :options="{ group: false }"
            lock="fs"
          ></StaffSelectorInput>
        </el-form-item>
      </el-form>
    </v-dialog>
  </div>
</template>

<script>
import VDialog from "@/components/dialog";

import { mapState, mapActions } from "vuex";
import SelectorInput from "vue-selector-input";
import http from "@/services/http/index";
import StaffSelectorInput from "@/modules/staff-selector-input";
const publicTypeList = [
  {
    id: "private",
    label: $t('marketing.commons.fgk_94789e')
  },
  {
    id: "public",
    label: $t('marketing.commons.gk_b6a098')
  }
];

export default {
  components: {
VDialog,
ElForm: FxUI.Form,
ElFormItem: FxUI.FormItem,
ElRadioGroup: FxUI.RadioGroup,
ElRadio: FxUI.Radio,
StaffSelectorInput
},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    defaultData: {
      type: Object,
      default() {
        return {};
      }
    },
    updatedCallback: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      rules: {
        name: [
          { required: true, message: $t('marketing.commons.qsrkbmc_921185'), trigger: "change" }
        ],
        visibleRage: [
          { required: true, message: $t('marketing.pages.kanban.qxzgklx_c247c2'), trigger: "change" }
        ],
        boardUserIds: [
          { required: false, message: $t('marketing.pages.kanban.qxzcyfw_42f108'), trigger: "change" }
        ]
      },
      kanbanForm: {
        name: null,
        visibleRage: "private",
        boardUserIds: [FS.contacts.getCurrentEmployee().id]
      }
    };
  },
  mounted() {
    this.kanbanForm = Object.assign({}, this.kanbanForm, this.defaultData);
    console.log("this.kanbanForm", this.kanbanForm);
  },
  watch: {
    defaultData() {
      this.kanbanForm = Object.assign({}, this.kanbanForm, this.defaultData);
    }
  },
  computed: {
    publicTypeList() {
      return publicTypeList;
    }
  },
  methods: {
    handleSelectName() {
      setTimeout(() => {
        //默认选中名称
        if(this.$refs.nameInput){
          this.$refs.nameInput.focus();
        }
      }, 100)
    },
    // ...mapActions('Kanban', ['listBoardTemplate', 'listBoardByFsUserId']),
    // 选人组件选人变更
    handleStaffSelectorChange(selectorResult) {
      console.log("selectorResult", selectorResult);
      this.kanbanForm.boardUserIds = selectorResult.member;
    },
    // 点击模型
    handleSelectModelTemplate(item) {
      this.selectedModelId = this.selectedModelId === item.id ? null : item.id;
    },
    // 提交
    async handleSubmitDialog() {
      this.$refs.kanbanForm.validate(async valid => {
        if (valid) {
          console.log("kanbanForm", this.kanbanForm);
          if (!this.kanbanForm.id) {
            this.loading = true
            const res_addModel = await http.addBoard(this.kanbanForm);
            this.loading = false
            if (res_addModel.errCode === 0) {
              if (this.updatedCallback && typeof this.updatedCallback === 'function') {
                this.updatedCallback(res_addModel.data);
              } else {
                this.$emit("update:submit", res_addModel.data);
                this.resetForm();
                this.$router.push({
                  name: "kanban-detail",
                  params: { id: res_addModel.data }
                });
              }
            }
          } else {
            const res_updateModel = await http.updateModel(this.kanbanForm);
            if (res_updateModel.errCode === 0) {
              if (this.updatedCallback && typeof this.updatedCallback === 'function') {
                this.updatedCallback(res_updateModel.data);
              } else {
                this.$emit("update:submit", res_updateModel.data);
                this.resetForm();
                this.$router.push({
                  name: "kanban-detail",
                  params: { id: res_addModel.data }
                });
              }
            }
          }
        }
      });
    },
    resetForm() {
      this.kanbanForm = {
        name: null,
        visibleRage: "private",
        boardUserIds: [FS.contacts.getCurrentEmployee().id]
      };
      this.$refs.kanbanForm.resetFields();
    },
    // 关闭弹框
    handleCloseDialog() {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="less">
.create-kanban-dialog {
  .el-dialog__body {
    padding: 30px 40px;
    height: 270px;
    .el-form-item {
      margin-bottom: 16px;
    }
    .el-radio-group {
      .el-radio {
        .el-radio__label {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
