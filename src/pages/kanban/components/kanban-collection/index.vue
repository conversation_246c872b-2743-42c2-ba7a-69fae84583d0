<template>
  <div :class="['kanban-collection']" >
    <Draggable
      class="kanbanlist--draggable"
      v-model="model_boardCardLists"
      v-bind="dragOptions"
      @change="handleListsMove"
      v-show="model_boardCardLists.length > 0"
      :forceFallback="true"
      :disabled="!editable"
    >
      <KanbanList
        ref="kanbanListItems"
        v-for="list in model_boardCardLists"
        :key="list.id"
        :list="list"
        v-on="$listeners"
        :editable="editable"
      ></KanbanList>
    </Draggable>
    <div class="list--new" v-if="editable">
      <!-- <ElCollapseTransition> -->
      <div class="list__add" v-show="!model_addList" @click="handleAddList">
        <i class="iconfont icon_add">&#xe624;</i>{{ $t('marketing.commons.tjxlb_0292f1') }}
      </div>
      <AddKanbanList
        v-show="model_addList" 
        ref="$AddKanbanList"
        @update:submit="handleAddListSubmit"
        @update:cancel="handleAddListCancel"
        tabindex="0"
      ></AddKanbanList>
      <!-- </ElCollapseTransition> -->
    </div>
  </div>
</template>

<script>
import Draggable from "vuedraggable";
import KanbanList from "./kanban-list";
import AddKanbanList from "./add-kanban-list";
import http from "@/services/http";
import { mapState, mapActions } from "vuex";
import CollapseTransition from "marketing-ui/lib/transitions/collapse-transition";
export default {
  components: {
    Draggable,
    KanbanList,
    AddKanbanList,
    ElCollapseTransition: CollapseTransition
  },
  props: {
    boardCardLists: Array,
    editable:true
  },
  watch: {
    boardCardLists(newVal) {
      this.model_boardCardLists = JSON.parse(JSON.stringify(newVal));
    },
    model_boardCardLists() {
      console.log("model_boardCardLists", this.model_boardCardLists);
    }
  },
  data() {
    return {
      model_addList: false,
      model_boardCardLists: []
    };
  },
  computed: {
    model_templateType() {
      return this.$store.state.Kanban.kanbanDetail.templateType;
    },
    model_kanbanDetail() {
      return this.$store.state.Kanban.kanbanDetail;
    },
    dragOptions() {
      return {
        handle: ".list__header",
        animation: 200,
        group: "kanbanList",
        disabled: false
      };
    },
    model_kanbanId() {
      return this.$store.state.Kanban.kanbanId;
    },
    test_boardCardLists() {
      console.log("test_boardCardLists", this.boardCardLists);
      return this.boardCardLists;
    }
  },
  mounted() {
    this.model_boardCardLists = [...this.boardCardLists];
  },
  methods: {
    ...mapActions("Kanban", ["getBoardDetail", "updateDisplayOrder"]),
    handleAddList() {
      this.model_addList = true;
      this.$nextTick(() => {
        this.$refs.$AddKanbanList.inputfocus();
      });
      this.focusAddListWrapper();
    },
    // 新增看板列表
    async handleAddListSubmit(listName) {
      http
        .addBoardCardList({
          boardId: this.model_kanbanId,
          name: listName
        })
        .then(async res => {
          if (res.errCode === 0) {
            this.$refs.$AddKanbanList.model_name = "";
            await this.getBoardDetail({
              id: this.model_kanbanId,
              withoutLoading: true
            });
            this.model_addList = false;
          }
        });
    },
    handleAddListCancel() {
      this.model_addList = false;
    },
    // 列表被拖拽
    handleListsMove(evt) {
      this.updateDisplayOrder(this.model_boardCardLists.map(item => item.id));
    },
    focusAddListWrapper() {
      console.log("this.$el", this.$el);
      setTimeout(() => {
        this.$el.scrollLeft = this.$el.scrollWidth;
      }, 100);
    }
  }
};
</script>

<style lang="less" scoped>
.kanban-collection {
  height: 100%;
  width:100%;
  background: #f2f2f5;
  box-sizing: border-box;
  overflow: auto;
  padding: 18px 20px 0px;
  white-space: nowrap;
  overflow-y: hidden;
  display: inline-flex;
  .kanbanlist--draggable {
    display: inline-block;
    height: 100%;
  }
  .list--new {
    flex-shrink: 0;
    width: 295px;
    display: inline-block;
  }
  .list__add {
    height: fit-content;
    cursor: pointer;
    color: var(--color-info06,#407FFF);
    font-size: 14px;
    &:hover {
      color: var(--color-info06,#407FFF);
    }
    .icon_add {
      margin-right: 5px;
    }
  }
}
</style>
