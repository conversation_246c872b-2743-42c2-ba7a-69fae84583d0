<template>
  <div :class="[$style.kanban_input, 'kanban__input']">
    <Tooltip
      :disabled="focus"
      effect="dark"
      :content="$t('marketing.pages.kanban.djjkbj_9b35e0')"
      placement="top"
      :open-delay="1000"
    >
      <div v-if="md">
        <div
          v-if="!editorShow"
          :class="[$style.input, 'kanban__input']"
          @click="handleOpenEditor"
        >
          <div v-if="content" ref="markdown">{{ content }}</div>
          <div v-else>{{ placeholder }}</div>
        </div>
      </div>
      <Input
        v-else
        :class="$style.textarea"
        :type="type"
        autosize
        :placeholder="focus ? '' : placeholder"
        :maxlength="maxlength"
        v-model="content"
        @input="handleInput"
        @focus="focus = true"
        @blur="handleEnter"
        @keydown.enter.exact.prevent.native
        @keyup.enter.exact.native="handleEnter"
      >
      </Input>
    </Tooltip>
    <div :class="$style.editor" v-if="editorShow">
      <div ref="vditor"></div>
      <div :class="$style.button">
        <fx-button size="mini" plain @click="handleHideEditor">{{ $t('marketing.commons.qx_625fb2') }}</fx-button>
        <fx-button type="primary" size="mini" @click="handleSave"
          >{{ $t('marketing.commons.bc_be5fbb') }}</fx-button
        >
      </div>
    </div>
  </div>
</template>

<script>

import Vditor from "vditor";

export default {
  components: {
Tooltip: FxUI.Tooltip,
Input: FxUI.Input
},
  props: {
    value: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: $t('marketing.commons.qsr_02cc4f')
    },
    maxlength: {
      type: Number,
      default: 500
    },
    type: {
      type: String,
      default: "textarea"
    },
    md: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      content: this.value,
      editorShow: false,
      focus: false
    };
  },
  watch: {
    value() {
      this.content = this.value;
      this.handlePreview();
    }
  },
  methods: {
    handleInput() {
      this.focus = false;
      if (this.content !== this.value) this.$emit("input", this.content);
    },
    handleOpenEditor() {
      this.editorShow = true;
      this.focus = true;
      this.$nextTick(() => {
        this.createVditor();
      });
    },
    createVditor() {
      this.contentEditor = new Vditor(this.$refs.vditor, {
        height: "auto",
        minHeight: 186,
        // placeholder: this.placeholder,
        toolbarConfig: {
          pin: false
        },
        cache: {
          enable: false
        },
        toolbar: [
          "headings",
          "bold",
          "link",
          "list",
          "ordered-list",
          "outdent",
          "indent"
        ],
        counter: {
          type: "md",
          enable: true,
          max: 2000
        },
        // cdn: FS.APP_MARKETING_MODULE.ROOT_PATH + "/static/vditor",
        after: () => {
          this.contentEditor.setValue(this.value || "");
          this.contentEditor.focus();
        }
      });
    },
    handlePreview() {
      if (this.md && this.content) {
        Vditor.preview(this.$refs.markdown, this.content, {
          markdown: {
            toc: true,
            listStyle: true
          },
          speech: {
            enable: true
          },
          anchor: 0
        });
      }
    },
    handleHideEditor() {
      this.editorShow = false;
      this.$nextTick(() => this.handlePreview());
    },
    handleSave() {
      let content = this.contentEditor.getValue();
      if (content === "\n") {
        content = "";
      }
      this.content = content;
      this.editorShow = false;
      this.$nextTick(() => this.handlePreview());
      this.handleEnter();
    },
    handleEnter() {
      this.handleInput();
      this.$emit("save", this.content);
    }
  },
  mounted() {
    this.handlePreview();
  }
};
</script>

<style lang="less" module>
@import '~vditor/dist/index.css';
.kanban_input {
  display: block;
  padding: 0 5px;
  margin-left: -5px;
  &:hover {
    background-color: #f0f4fc;
    border-radius: 4px;
  }
  .input {
    min-width: 100px;
  }
  .editor {
    padding-top: 5px;
    :global{
      .vditor-toolbar{
        padding-left: 10px!important;
      }
      .vditor-reset{
        padding: 10px !important;
      }
    }
  }
  .button {
    text-align: right;
    padding: 10px 1px;
  }
  .textarea {
    font-size: inherit !important;
    :global {
      .el-input__inner {
        height: auto !important;
      }
      .el-textarea__inner,
      .el-input__inner {
        padding: 0 !important;
        border: 0 !important;
        resize: none;
        line-height: 1.5 !important;
        background: none;
        color: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        font-style: inherit !important;
        font-size: inherit !important;
      }
      textarea,
      input {
        &::-webkit-input-placeholder {
          color: @color-title;
        }
        &:-moz-placeholder {
          /* Firefox 18- */
          color: @color-title;
        }

        &::-moz-placeholder {
          /* Firefox 19+ */
          color: @color-title;
        }
        &:-ms-input-placeholder {
          color: @color-title;
        }
      }
    }
  }
  :global {
    .vditor-reset,
    .vditor {
      ul {
        list-style: disc;
      }
      ol {
        list-style: decimal;
      }
    }
    .vditor,
    .vditor-toolbar {
      border-color: @border-color-base;
    }
    .vditor-toolbar {
      background-color: #f8f8fa;
    }
  }
}
</style>
