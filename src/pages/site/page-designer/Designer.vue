<template>
  <div :class="['marketing-site__designer', { 'marketing-site__designer--agent': isAgent }]">
    <Hexagon
      ref="hexagon"
      :pages="pages"
      :page-load="pageLoad"
      :upload="upload"
      :edit-page-id.sync="editPageId"
      :before-comp-data-create="beforeCompDataCreate"
      :context-props="{
        isAgent: isAgent,
      }"
      @save="hanldePageSave"
      @finish="hanldePageFinish"
      @close="handlePageClose"
      @page:operation="handlePageOperation"
      @page:change="handlePageChange"
      @setting:change="handlePageSettingChange"
    >
      <template slot="header:title">
        <SiteNameInput
          :title="pageTitle"
          :value="siteName"
          @change="handleSiteNameEditFinish"
        />
      </template>
      <div slot="header:content">
        <div :class="['hexagon-saving-tip', saveStatus !== 0 ? 'showtip' : '']">
          {{ saveTip }}
        </div>
      </div>
      <!-- <template
        slot="setting"
        slot-scope="scope"
        v-if="scope.data.type === 'article'"
      >
        <hexagon-ex-article-component
          :comid="scope.data.id"
          :articleData="scope.data.article"
          @choose="handleArticleChoose"
        ></hexagon-ex-article-component>
      </template> -->
      <!-- 选择内容或活动组件内容 -->
      <template
        v-if="scope.data.type === 'activity' || scope.data.type === 'eventslist' || scope.data.type === 'content'"
        slot="setting:aftercontent"
        slot-scope="scope"
      >
        <div
          v-if="scope.data.range === 'site'"
          class="hexagon__activity"
        >
          <!-- 选择微页面分组 -->
          <GroupDropdown
            :active-group-id="scope.data.siteGroupId"
            :object-type="26"
            :hide-group-ids="['-2', '-3']"
            style="width: auto;"
            @change="handleSiteGroupChange"
          />
        </div>
        <!-- 指定内容展示 -->
        <div
          v-if="scope.data.range.indexOf('custom') !== -1 && scope.data.range !== 'custom-product-more' && scope.data.range !== 'custom-article-more'"
          class="hexagon__activity"
        >
          <draggable
            v-model="scope.data.items"
            v-bind="dragOptions"
            :force-fallback="true"
            handle=".activity-item"
            @start="drag = true"
            @end="drag = false"
            @sort="(e)=>handleSortChange(e,scope.data.items)"
          >
            <transition-group>
              <div
                v-for="(item, i) in scope.data.items"
                :key="item "
                class="activity-item"
              >
                <div class="left-content">
                  <img
                    class="image"
                    :src="item.image"
                    alt=""
                  >
                  <div
                    class="title"
                    :title="item.title"
                  >
                    {{ item.title }}
                  </div>
                </div>
                <div class="right-content">
                  <span
                    class="iconfont icontuodongpaixu drag_handle"
                  />
                  <span
                    class="iconfont iconshanchu1 remove"
                    @click="handleRemoveActivity(i)"
                  />
                </div>
              </div>
            </transition-group>
          </draggable>
        </div>

        <!--    产品分组多组    -->
        <div
          v-if="scope.data.range == 'custom-product-more' || scope.data.range == 'custom-article-more'"
          class="hexagon__activity"
        >
          <div
            v-for="(item, i) in scope.data.classification"
            :key="i"
            class="activity-item"
            style="padding: 0 10px;"
          >
            <div
              class="title"
              style="cursor: pointer"
              @click="handleProduct(item)"
            >
              {{ item.label }}
            </div>
            <div
              class="remove"
              @click="handleRemoveProduct(item)"
            >
              {{ $t('marketing.commons.yc_86048b') }}
            </div>
          </div>
        </div>
        <!--    文章分组单组    -->
        <div
          v-if="Object.keys(objectTypeByRange).includes(scope.data.range)"
          class="hexagon__activity"
        >
          <GroupDropdown
            :active-group-id="scope.data.siteGroupId"
            :object-type="objectTypeByRange[scope.data.range]"
            :hide-group-ids="['-2', '-3']"
            style="width: auto;"
            @change="handleSiteGroupChange"
          />
        </div>

        <div
          v-if="
            scope.data.range === 'site-tags'
              || scope.data.range === 'article-tags'
              || scope.data.range === 'product-tags'
              || scope.data.range === 'activity-tags'
          "
          class="hexagon__activity"
        >
          <div
            class="hexagon__activity_tags"
            @click="handleShowTagsQueryDialog"
          >
            <template v-if="scope.data.materialTagFilter && scope.data.materialTagFilter.tags">
              <span
                v-for="item in scope.data.materialTagFilter.tags"
                :key="item.nameid"
                class="tag-item"
              >{{ item.nameid }}</span>
            </template>
            +{{ $t('marketing.commons.xzbq_f9b05d') }}
          </div>
          <ContentTagsQueryDailog
            v-if="queryDialogVisible"
            :visible="queryDialogVisible"
            :init-form="scope.data.materialTagFilter"
            @onSubmit="handleSubmitTagsQueryDialog"
            @onClose="handleCloseTagsQueryDialog"
          />
        </div>
        <div
          v-if="scope.data.range === 'filter'"
          class="hexagon__activity"
        >
          <ObjectFilterLine
            :value="scope.data.activityFilter"
            :filter-fields="filterMarketingEventObjField"
            :show-tag="false"
            @change="handleMarketingEventObjFilterChange"
          />
        </div>
        <!-- 内容组件排序提示，非置顶的 -->
        <p
          v-if="
            scope.data.range != 'custom-site'
              && scope.data.range != 'custom-article'
              && scope.data.range != 'custom-product'
              && scope.data.range != 'custom'
              && scope.data.type !== 'eventslist'
          "
          class="sort-tips"
        >
          {{ $t('marketing.pages.site.xznrmracjs_cf37b0') }}
        </p>

        <!-- 活动组件筛选,指定活动的时候还是按照本来顺序来 -->
        <div
          v-if="scope.data.type === 'eventslist' && scope.data.range !=='custom'"
          class="marketing-hexagon__setting_item"
        >
          <div class="marketing-hexagon__setting_title">
            {{ $t('marketing.pages.site.pxgz_726a6f') }}
          </div>
          <div class="marketing-hexagon__setting_con">
            <fx-select
              v-model="scope.data.orderType"
              size="mini"
              style="width: 100%;"
              :placeholder="$t('marketing.commons.qxz_708c9d')"
              :options="eventsOrderTypeOption"
              @change="handleEventOrderTypeChange"
            />
          </div>
        </div>
      </template>
      <template
        v-if="
          ['file', 'fileDownload', 'fileDownloadV2', 'function', 'content', 'memberPage', 'chat', 'couponDetail'].indexOf(
            scope.action.type,
          ) !== -1
        "
        slot="action:content"
        slot-scope="scope"
      >
        <ActionSlots
          :scope="scope"
          :form-data="formData"
          :member-pages="memberPages"
          :miniapp-type="miniappType"
        />
      </template>
    </Hexagon>
    <SiteInfoDialog
      v-if="siteNameDialogVisible && !isAgent"
      :dialog-title="$t('marketing.commons.wymmc_245675')"
      :default-site-info="{
        name: siteName,
      }"
      :visible.sync="siteNameDialogVisible"
      @update:submit="handleSiteNameSubmit"
    />
    <Dialog
      v-if="payformPupop"
      class="site__payform-pupop"
      :title="$t('marketing.commons.ts_02d981')"
      width="500px"
      :visible="payformPupop"
      :show-cancel="false"
      :no-footer="true"
      @onClose="payformPupop = false"
      @onSubmit="payformPupop = false"
    >
      <div class="site__payform-pupop-item">
        {{ $t('marketing.commons.zfbdsyztsb_c46917') }}
      </div>
      <div class="site__payform-pupop-item">
        {{ $t('marketing.pages.site.qqwxtsz_0f1f25') }}>{{ $t('marketing.pages.site.yxcjktqyqb_640f8b')
        }}<router-link
          :to="{ name: 'setting-setitems', query: { type: 'v-marketing-plugin' } }"
          target="_blank"
        >
          {{ $t('marketing.commons.qwkt_5371ff') }}
        </router-link>
      </div>
    </Dialog>
    <SiteInfoDialog
      v-if="pageNameDialogVisible"
      :dialog-title="$t('marketing.commons.ymmc_b78454')"
      :default-site-info="{
        name: pageRenamePropsName,
      }"
      :visible.sync="pageNameDialogVisible"
      @update:submit="handlePageNameSubmit"
    />
    <PageRender
      v-if="cutPageData"
      ref="cutPage"
      style="position: fixed; top: 0; left: -375px; z-index: 0;width: 375px"
      :data="cutPageData"
      :scroll-height="625"
    />
  </div>
</template>

<!-- eslint-disable import/no-import-module-exports -->
<script>
import Hexagon, {
  PageRender,
  componentStore,
  namespace,
} from '@/components/Hexagon'
import Draggable from 'vuedraggable'
import Dialog from '@/components/dialog/index.vue'
import ObjectFilterLine from '@/components/ObjectFilterLine/index.vue'
import SiteInfoDialog from '../site-info-dialog/index.vue'
import SiteNameInput from './SiteNameInput.vue'
import ActionSlots from './ActionSlots.vue'
import http from '@/services/http/index.js'

import {
  bindBrowserRefreshOrCloseTips, unbindBrowserRefreshOrCloseTips, jsonparse, b64toFile,
} from '@/utils/index.js'
import { confirm } from '@/utils/globals.js'
import { cachePages, clearCache } from '../pages-cache.js'
import FormModule from '../form-module.js'
import appmarketingRPC from '@/utils/appmarketing.rpc.js'
import html2canvas from '@/../libs/html2canvas.js'
import kisvData from '@/modules/kisv-data.js'
import GroupDropdown from '@/components/group-manage-new/group-dropdown.vue'
import ContentTagsQueryDailog from '@/components/content-tags-selector/tags-query-dialog.vue'

export default {
  components: {
    Hexagon,
    SiteInfoDialog,
    Dialog,
    ObjectFilterLine,
    PageRender,
    SiteNameInput,
    ActionSlots,
    GroupDropdown,
    Draggable,
    ContentTagsQueryDailog,
  },
  props: {
    upload: Function,
    memberConfig: Object,
    isAgent: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      saveStatus: 0, // 0:隐藏 1:保存中 2:保存成功 3:保存失败
      // 请求站点接口是否准备好了
      _isReady: false,
      pageLoad: this._pageLoad, // 给设计器传入当前页面的数据
      pages: [], // 给设计器传入页面列表的数据

      _siteId: '', // 站点Id：编辑时在路由中获取，新建时，调用接口获取
      siteName: '', // 站点名称：显示在左上角
      siteStatus: 5, // 站点状态： 1-正常 5-隐藏
      _templateSiteId: '', // 模板站点Id：新建站点时，有时候会带入模板站点
      editPageId: '', // 设计器中正在编辑的页面Id
      curOperationPageId: '', // 设计器中点击了编辑操作的页面ID(如点击某个页面的重命名按钮，则这个变量的值为该页面的Id)
      data_cacheForms: {}, // 表单数据缓存，用于最终提交，同样是用pageid作为key存储，value是form
      mainFormId: '', // 主表单id，editSite接口返回，用于crm映射弹窗入参

      isEdit: false, // 是否编辑态
      isWebsite: false, // 是否作为官网进行创建、编辑
      isTemplate: false, // 是否作为微页面模版编辑修
      siteNameStatus: 'display', // 站点名称的状态： display-展示态 edit-编辑态

      pageRenamePropsName: '', // 页面重命名的传入参数
      siteNameDialogVisible: false, // 站点重命名弹窗
      pageNameDialogVisible: false, // 页面重命名弹窗
      settingDialogVisible: false, // 站点设置弹窗（主要用于站点的主表单的crm映射）
      settingTipsDialogVisible: false, // 站点设置弹窗（主要用于站点的主表单的crm映射）
      payformPupop: false,
      showMaterialChooseDialog: false,
      currPageData: {}, // 当前微页面编辑页面数据
      funcSdkLoading: false, // 加载APL代码选择框
      tplPageIdToSitePageIdMap: {},
      cutPageData: null,
      miniappType: 0,
      queryDialogVisible: false,
      eventsOrderTypeOption: [
        {
          label: $t('marketing.commons.cjsj_eca37c'),
          value: 0,
        },
        {
          label: $t('marketing.commons.hdkssj_ae59ed'),
          value: 1,
        },
        {
          label: $t('marketing.commons.hdjssj_b85c71'),
          value: 2,
        },
      ],
      objectTypeByRange: {
        'custom-article-single': 6,
        'custom-product-single': 4,
      },
    }
  },
  computed: {
    saveTip() {
      let tip = ''
      if (this.saveStatus === 1) {
        tip = $t('marketing.pages.site.bcz_2a3302')
      } else if (this.saveStatus === 2) {
        tip = $t('marketing.pages.site.ybc_f8dfed')
      } else if (this.saveStatus === 3) {
        tip = $t('marketing.commons.bcsb_6de920')
      }
      return tip
    },
    vData() {
      return kisvData.datas
    },
    pageTitle() {
      if (this.isTemplate) {
        return $t('marketing.commons.wymmb_bc420a', {
          data: { option0: this.isEdit ? $t('marketing.commons.bj_95b351') : $t('marketing.commons.xj_26bb84') },
        })
      }
      return $t('marketing.commons.wym_ddd1f7', {
        data: { option0: this.isEdit ? $t('marketing.commons.bj_95b351') : $t('marketing.commons.xj_26bb84') },
      })
    },
    formData() {
      if (!this.editPageId) return null
      const content = this.currPageData
      if (!content) return null
      const form = FormModule.getFormBodyFromPage(content)
      return form
    },
    memberPages() {
      const {
        loginSiteId, registrationSiteId, contentCenterSiteId, updateInfoSiteId, forgotPasswordSiteId,
      } = this.memberConfig || {}
      if (loginSiteId) {
        return [
          {
            name: $t('marketing.commons.hydl_83e581'),
            id: loginSiteId,
          },
          {
            name: $t('marketing.commons.hyzc_8b0cf5'),
            id: registrationSiteId,
          },
          {
            name: $t('marketing.commons.grzx_409120'),
            id: contentCenterSiteId,
          },
          {
            name: $t('marketing.commons.grxxbj_901bb1'),
            id: updateInfoSiteId,
          },
          {
            name: $t('marketing.commons.mmxg_f7f7ed'),
            id: forgotPasswordSiteId,
          },
          {
            name: $t('marketing.pages.site.tgzx_a20708'),
            id: 'promotionHome',
            loginSiteId,
          },
        ]
      }
      return []
    },
    dragOptions() {
      return {
        animation: 200,
        group: 'classification',
        disabled: false,
      }
    },
  },
  watch: {
    editPageId() {
      console.log('editPageId', this.editPageId)
    },
    vData() {
      this.setPayFormCompOpened()
    },
  },
  beforeCreate() {
    console.log('beforeCreate>>>', namespace, componentStore)
    if (!this.$store.state[namespace]) {
      this.$store.registerModule(namespace, componentStore)
    }
    bindBrowserRefreshOrCloseTips()
  },
  beforeDestroy() {
    if (!module.hot) {
      this.$store.unregisterModule(namespace)
    }
    unbindBrowserRefreshOrCloseTips()
    clearCache()
  },
  mounted() {
    const { siteId, templateSiteId } = this.$route.params || {}
    const { isWebsite } = this.$route.query || {}
    this.queryBoundMiniappInfo()
    this.$store.dispatch('querySmsMarketingOpenStatus') // 短信主菜单跳转地址，子菜单可见性控制

    // 是否为微页面编辑态或模版编辑态
    this.isEdit = (siteId !== '0' && siteId !== 'template') || (siteId === 'template' && templateSiteId)

    // 是否为官网编辑态
    this.isWebsite = isWebsite === '1'

    // 是否为模版编辑态
    this.isTemplate = siteId === 'template'

    if (siteId === 'template') {
      this._siteId = templateSiteId || null
      this._templateSiteId = templateSiteId || null
    } else {
      this._siteId = siteId === '0' || siteId === 'template' ? null : siteId
      this._templateSiteId = this._siteId ? null : templateSiteId || null
    }

    // 是否为编辑态
    if (this._siteId) {
      this._forEditSite()
    } else {
      this._forCreateSite()
    }

    this.setPayFormCompOpened()
  },
  methods: {
    filterMarketingEventObjField(field) {
      const apiName = field.api_name || field.data
      if (field.api_name === 'event_type') {
        const showEventTypes = ['3', 'multivenue_marketing', 'live_marketing']
        field.options = field.options.filter(item => showEventTypes.indexOf(item.value) !== -1)
      }
      // 显示自定义字段
      if (field.define_type === 'custom') {
        return false
      }
      const showFields = [
        'name',
        'event_type',
        'biz_status',
        'status',
        'life_status',
        'parent_id',
        'begin_time',
        'end_time',
        'description',
        'location',
        'owner',
        'owner_department',
        'data_own_department',
      ]
      return showFields.indexOf(apiName) === -1
    },
    handleCreateWxwork() {
      FxUI.MessageBox.confirm($t('marketing.commons.sfywcqywxb_de5f28'), $t('marketing.commons.ts_02d981'), {
        confirmButtonText: $t('marketing.commons.ywc_fad522'),
        cancelButtonText: $t('marketing.commons.gb_b15d91'),
        type: 'warning',
      })
        .then(() => {
          // this.$store.dispatch("QywxManage/queryQywxOpenStatus");
          this.$store.dispatch('Global/getAddressBookSetting') // 企业微信管理主菜单跳转地址，子菜单可见性控制(改用这个接口查询)
        })
        .catch(() => {})
      const route = this.$router.resolve({
        name: 'qywx-manage',
      })
      window.open(route.href, '_blank')
    },
    setPayFormCompOpened() {
      const { bindWallet } = this.vData.uinfo || {}
      if (!bindWallet) {
        this.$refs.hexagon.updateOriginCompData({
          'typeValue:order': {
            disable: true,
            updateMessage: () => {
              this.payformPupop = true
            },
          },
        })
      }
    },
    // 获取营销通绑定的小程序，如未开通私有小程序则不能使用支付表单
    queryBoundMiniappInfo() {
      http
        .getBoundMiniappInfo({
          platformId: 'YXT',
        })
        .then(({ errCode, data }) => {
          if (errCode === 0 && data) {
            this.miniappType = data.realAppType
          }
        })
    },
    // 添加微页面到市场活动下
    addSiteToMarketingEvent() {
      const { marketingEventId } = this.$route.query || {}
      if (!marketingEventId || !this._siteId || this.siteStatus !== 1) return
      http.addMaterial({
        marketingEventId,
        objectId: this._siteId,
        objectType: 26,
      })
    },
    // 添加微页面到市场活动下
    resetConferenceFormBySite() {
      http.resetConferenceFormBySite({
        siteId: this._siteId,
      })
    },
    /**
     * 截图首页作为封面
     */
    homePageScreenshot(pages) {
      return new Promise(resolve => {
        try {
          const pageData = JSON.parse(pages.filter(item => item.isHomepage === 1)[0].content) || {}
          // 页面没有内容时，不做裁剪
          if (!pageData.components || !pageData.components.length) {
            resolve({})
            return
          }
          this.cutPageData = pageData
          this.$nextTick(() => {
            html2canvas(this.$refs.cutPage.$el).then(canvas => {
              this.uploadFile(b64toFile(canvas.toDataURL('image/jpeg'), `${+new Date()}.jpeg`), data => {
                if (data) {
                  resolve({
                    coverPath: data.path,
                  })
                } else {
                  resolve({})
                }
              })
            })
          })
        } catch (error) {
          resolve({})
        }
      })
    },
    // 设计器点击保存时
    hanldePageSave(curPage, pages, callback) {
      // 最终保存时，要将站点重新保存一遍，更改状态（为了让没有点保存按钮的站点，不出现在列表页）
      console.log('hanldePageSave', curPage, pages)
      if (!this._siteId) {
        FxUI.Message.error($t('marketing.commons.zdcjsbqsxy_49767a'))
        return
      }
      // 缓存当前编辑中的页面，以便批量保存
      curPage.shareOpts = curPage.shareOpts || {}
      cachePages('save', {
        ...cachePages('get', curPage.id),
        content: JSON.stringify(curPage),
        shareDesc: curPage.shareOpts.desc,
        shareUrl: curPage.shareOpts.imgUrl,
        sharePosterUrl: curPage.shareOpts.sharePosterUrl,
        sharePosterAPath: curPage.shareOpts.sharePosterAPath,
        originalImageAPath: curPage.shareOpts.originalImageAPath || '',
        cutOffsetList: curPage.shareOpts.cutOffsetList || [],
        shareTitle: curPage.shareOpts.title,
      })
      this.pages = cachePages('get')
      this.saveStatus = 1
      this._fakeEditPages(this._siteId, cachePages('get'), async sid => {
        this._fakeEditSite(
          {
            id: this._siteId,
            name: this.siteName,
            status: this.isEdit ? this.siteStatus : 1,
            // 是否绑官网页面
            needBindWebSite: this.isWebsite,
            // create_type等于private并且不是编辑模式时，属于创建专属微页面，自动关联到市场活动下
            system: !!(this.$route.query.createType === 'private' && !this.isEdit),
          },
          data => {
            this.siteStatus = 1
            const { siteId, templateSiteId } = this.$route.params || {}
            this.$router.replace({
              name: 'site-design',
              params: {
                siteId: siteId || null,
                templateSiteId: templateSiteId || null,
              },
              query: this.$route.query,
            })
            // 添加微页面到市场活动下
            this.addSiteToMarketingEvent()
            // 会议绑定表单
            this.resetConferenceFormBySite()
            if (typeof callback === 'function') {
              callback(data)
            }
          },
        )
      })
    },
    // 设计器点完成时
    hanldePageFinish(curPage, pages) {
      this.hanldePageSave(curPage, pages, data => {
        this.$emit('saveAndExt')
        // this.mainFormId = data.formId;
        // if (data.hadCrmMapping === false) {
        //   this.settingTipsDialogVisible = true;
        // };
        const { from, fromid, redirect } = this.$route.query
        let params
        if (from === 'dialog') {
          // 从素材选择弹窗点击新建过来的
          if (fromid) {
            appmarketingRPC.set(fromid, {
              type: 26,
              id: this._siteId,
            })
          }
          window.close()
        } else if (redirect) {
          const route = JSON.parse(redirect)
          params = {
            ...route,
            query: {
              ...route.query,
              callbackType: 26,
              callbackId: this._siteId,
            },
            params: {
              ...route.params,
            },
          }

          if (this.isWebsite) {
            params.query.isWebsite = this.$route.query.isWebsite
          }
          this.$router.replace(params)
        } else if (from === 'website') {
          this.$router.back()
        } else {
          params = {
            name: 'site-list',
            params: {
              type: this.isTemplate ? 'template' : 'index',
            },
            query: {},
          }
          if (data.hadCrmMapping === false) {
            params.query.needTipsSettingFormId = data.formId
            params.query.needTipsSettingSiteId = this._siteId
          }
          if (!this.isEdit) {
            params.query.needTipsSettingSiteId = this._siteId
          }
          this.$router.push(params)
        }
      })
    },
    handlePageClose() {
      this.$emit('close')
    },
    // 站点重命名弹窗的回调
    handleSiteNameSubmit(siteInfo) {
      this.siteName = siteInfo.name
      this.siteNameDialogVisible = false
      // this.handleSiteNameEditFinish(siteInfo.name, () => {
      //   this.siteName = siteInfo.name;
      //   this.siteNameDialogVisible = false;
      // });
    },
    // 完成站点名的编辑
    handleSiteNameEditFinish(siteName, callback) {
      this.siteName = siteName
      // this._fakeEditSite(
      //   {
      //     id: this._siteId,
      //     name: this.siteName
      //   },
      //   callback,
      //   () => {
      //     FxUI.Message.error("站点名修改失败，请重试");
      //   }
      // );
    },
    // 页面重命名弹窗的回调
    handlePageNameSubmit(siteInfo) {
      const oldPageData = cachePages('get', this.curOperationPageId)
      const newName = siteInfo.name
      let oldContent = {}
      try {
        oldContent = JSON.parse(oldPageData.content)
      } catch (error) {
        return
      }

      const newHexagonPage = {
        ...oldContent,
        name: newName,
      }
      cachePages('save', {
        ...oldPageData,
        name: newName,
        content: JSON.stringify(newHexagonPage),
      })
      this.pages = cachePages('get')
      const index = this.pages.findIndex(item => item.id === this.curOperationPageId)
      this.$set(this.pages, index, this.pages[index])

      if (this.curOperationPageId === this.editPageId) {
        this.$refs.hexagon.updateCurrentPage(newHexagonPage)
      }
      this.pageNameDialogVisible = false
    },
    // 设计器进行操作时
    handlePageOperation(type, page) {
      console.log('handlePageOperation', type, page)
      this.curOperationPageId = page.id
      switch (type) {
        case 'create':
          this._operationPageCreate()
          break
        case 'rename':
          this._operationPageRename(page)
          break
        case 'copy':
          this._operationPageCopy(page)
          break
        case 'preview':
          this._operationPagePreview()
          break
        case 'delete':
          this._operationPageDelete(page.id)
          break
        case 'setHomePage':
          this._operationPageHome(page.id)
          break
        default:
          break
      }
    },
    // 设计器组件变化时
    handlePageChange(nextId, id, page) {
      console.log('handlePageChange', nextId, id, page)
      page.shareOpts = page.shareOpts || {}
      cachePages('save', {
        ...cachePages('get', id),
        content: JSON.stringify(page),
        shareDesc: page.shareOpts.desc,
        shareUrl: page.shareOpts.imgUrl,
        sharePosterUrl: page.shareOpts.sharePosterUrl,
        sharePosterAPath: page.shareOpts.sharePosterAPath,
        originalImageAPath: page.shareOpts.originalImageAPath || '',
        cutOffsetList: page.shareOpts.cutOffsetList || [],
        shareTitle: page.shareOpts.title,
      })
      this.pages = cachePages('get')
    },
    // 设计器设置变化时（主要监听页面名称改动）
    handlePageSettingChange(page) {
      console.log('handlePageSettingChange', page)
      if (!page) return
      if (
        page.type === 'page'
        && this.editPageId === page.id
        && cachePages('get', this.editPageId).name !== page.name
      ) {
        // 如果是当前页，需把数据先存入缓存中，否则会丢失
        if (page.id === this.editPageId) {
          cachePages('save', {
            ...cachePages('get', page.id),
            content: JSON.stringify(this.$refs.hexagon.getCurrentPageData()),
          })
        }
        this.curOperationPageId = page.id
        this.handlePageNameSubmit({ name: page.name })
      }
      if (this.editPageId) {
        this.currPageData = this.$refs.hexagon.getCurrentPageData()
      }
    },
    handleRemoveActivity(index) {
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      console.log('handleRemoveActivity', compOriginData)
      compOriginData.items.splice(index, 1)
      if (compOriginData.marketingEventIds) {
        compOriginData.marketingEventIds.splice(index, 1)
      }
      if (compOriginData.contentObjectIds) {
        compOriginData.contentObjectIds.splice(index, 1)
      }
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        items: compOriginData.items,
        marketingEventIds: compOriginData.marketingEventIds || [],
        contentObjectIds: compOriginData.contentObjectIds || [],
      })
    },
    handleSortChange(e, data) {
      console.log('data: ', data)
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      console.log('compOriginData: ', compOriginData)
      let marketingEventIds = []; let contentObjectIds = []
      if (compOriginData.marketingEventIds && compOriginData.type === 'eventslist') {
        marketingEventIds = data.map(item => item.marketingEventId)
        this.$refs.hexagon.updateCurrentCompData({
          ...compOriginData,
          items: data,
          marketingEventIds,
        })
      }
      if (compOriginData.contentObjectIds && compOriginData.type === 'content') {
        contentObjectIds = data.map(item => item.id)
        this.$refs.hexagon.updateCurrentCompData({
          ...compOriginData,
          items: data,
          contentObjectIds,
        })
      }
    },

    handleRemoveProduct(item) {
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      const newClassification = compOriginData.classification.filter(i => item.key !== i.key)
      this.$emit('classification', newClassification)
      console.log(newClassification, 'newClassification')
      if (!newClassification.length) {
        this.$refs.hexagon.updateCurrentCompData({
          ...compOriginData,
          currentItem: '',
        })
      }
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        classification: newClassification,
      })
    },
    handleProduct(item) {
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      console.log(compOriginData, 'compOriginData')
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        currentItem: item.key,
      })
    },
    handleSiteGroupChange(data) {
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        siteGroupId: data.groupId || '-1',
      })
    },
    handleSiteArtChange(groupId) {
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        siteGroupId: groupId,
      })
    },
    handleSubmitTagsQueryDialog(formData) {
      this.queryDialogVisible = false

      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        materialTagFilter: formData,
      })
    },
    handleMarketingEventObjFilterChange(data) {
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        activityFilter: data,
      })
    },
    handleCloseTagsQueryDialog() {
      this.queryDialogVisible = false
    },
    handleShowTagsQueryDialog() {
      this.queryDialogVisible = true
    },
    // 选择了文章
    // handleArticleChoose(data) {
    //   const compOriginData = this.$refs.hexagon.getCurrentCompData();
    //   console.log("getCurrentCompData", compOriginData);
    //   this.$refs.hexagon.updateCurrentCompData({
    //     ...compOriginData,
    //     action: {
    //       ...compOriginData.action,
    //       type: "article",
    //       id: data.id
    //     },
    //     article: {
    //       ...compOriginData.article,
    //       id: data.id,
    //       title: data.title,
    //       desc: data.summary,
    //       author: data.creator,
    //       time: data.lastModifyTime,
    //       image: data.photoUrl,
    //       content: encodeURIComponent(data.content),
    //       style: compOriginData.article.style
    //     }
    //   });
    // },
    _forCreateSite() {
      this.siteName = this.isTemplate ? $t('marketing.commons.wmmmb_e8aae0') : $t('marketing.commons.wmmwym_e199f5')
      this.siteNameDialogVisible = true
      this._fakeEditSite(
        {
          name: this.siteName,
        },
        () => {
          this._isReady = true
          // 新建完站点后，后台会自定生成一个首页
          this._getPagesBySiteId(pages => {
            // 录入前端缓存
            pages.forEach(item => {
              console.log('_getPagesBySiteId: ', item)

              const _formatItem = this._formatPagesItem(item)
              cachePages('save', _formatItem)
            })
            this.pages = cachePages('get')
            // 将首页置为当前页
            this.editPageId = cachePages('get')[0] && cachePages('get')[0].id
            // 如有引用站点模板
            if (this._templateSiteId) {
              // 将模板站点渲染至本站点
              this._renderTemplate()
            }
          })
        },
      )
    },
    _forEditSite() {
      /**
       * 退出微页面编辑后，hexagon中的store仍然存在缓存数据，在某些情况下，再次进入编辑状态时候
       * 会使用到之前的旧数据，这里进入前，先清空下store。
       * 会议营销-->会议报名设置：进入微页面编辑，保存退出
       * 进入【编辑基本信息】：修改会议详情，保存退出
       * 然后再进入【会议报名设置】：进入微页面编辑，这个时候会议详情数据还是之前的旧数据
       */
      if (this.$refs.hexagon) {
        this.$refs.hexagon.resetStore()
      }

      this._getSiteById(site => {
        this.siteName = site.name
        this.siteStatus = site.status
        //CTA是否已被CTA使用
        this.$store.commit('hexagon/setState', {
          usedByCta: site.usedByCta,
        });
      })
      this._getPagesBySiteId(pages => {
        // 录入前端缓存
        pages.forEach(item => {
          const _formatItem = this._formatPagesItem(item)
          cachePages('save', _formatItem)
        })
        this.pages = cachePages('get')
        // 将首页置为当前页
        this.editPageId = cachePages('get')[0] && cachePages('get')[0].id
      })
    },
    // 获取站点详情
    _getSiteById(callback) {
      if (!this._siteId) {
        return
      }
      if (this.isTemplate) {
        http
          .getTemplateByTemplateId({
            id: this._templateSiteId,
          })
          .then(({ errCode, data }) => {
            console.log('getTemplateByTemplateId', data)
            if (typeof callback === 'function') {
              callback(data)
            }
          })
      } else {
        http.getSiteById({ id: this._siteId }).then(({ errCode, data }) => {
          console.log('getSiteById', data)
          if (typeof callback === 'function') {
            callback(data)
          }
        })
      }
    },
    /**
     * 将模板站点渲染至本站点
     * 处理原则： 1.按照模板站点的页面数，调用接口创建等量的空白页面（注意：由于3，因此实际调用次数为n-1）
     *          2.模板页面的content，在不点击右上角保存之前，只缓存于前端，不调接口存储
     *          3.刚入本页面时，会调用后台接口创建站点。后台会默认创建一个首页，需要找到模板中的首页，将数据对应缓存。
     */
    _renderTemplate() {
      // 获取模板站点的所有页面
      this._getPagesByTemplateSiteId(async templatePages => {
        const needCreateCount = templatePages.length - 1
        let afterCreateCount = 0
        // 首页数据置入
        const temphomepage = templatePages[0] || {}
        // 将站点名设为首页page.name
        this.siteName = temphomepage.name
        // 保存模版首页ID到新首页ID的映射关系
        this.tplPageIdToSitePageIdMap[temphomepage.id] = this.editPageId

        const { formId } = temphomepage
        let newFormId = formId
        if (formId) {
          newFormId = await this._replaceFormId(formId)
        }

        cachePages('save', {
          ...temphomepage,
          id: this.pages[0].id,
          content: JSON.stringify({
            ...JSON.parse(temphomepage.content),
            id: this.pages[0].id,
          }),
          hexagonSiteId: this._siteId,
          hexagonTemplateSiteId: this._siteId,
          shareUrl: temphomepage.sharePicH5url,
          formId: newFormId,
        })
        // 更新设计器内当前页面数据
        this.$refs.hexagon.updateCurrentPage(JSON.parse(cachePages('get', this.pages[0].id).content))

        // 后续页面的数据置入
        const promiseQueue = []
        for (let i = 0; i < needCreateCount; i += 1) {
          const curTemplatePage = templatePages[i + 1]
          promiseQueue.push(
            this._fakeEditPage(
              this._createAPageObject({
                type: 'page',
                name: curTemplatePage.name,
              }),
              // eslint-disable-next-line no-loop-func
              async (id, newPage) => {
                let pageFormId = curTemplatePage.formId
                if (pageFormId) {
                  pageFormId = await this._replaceFormId(pageFormId)
                }
                console.log('pageFormId >>>', pageFormId)
                cachePages('save', {
                  ...curTemplatePage,
                  id,
                  content: JSON.stringify({
                    ...JSON.parse(curTemplatePage.content),
                    id,
                  }),
                  hexagonSiteId: this._siteId,
                  hexagonTemplateSiteId: this._siteId,
                  shareUrl: curTemplatePage.sharePicH5url,
                  formId: pageFormId,
                })
                // 保存模版页面ID到生成新页面ID的映射关系
                this.tplPageIdToSitePageIdMap[curTemplatePage.id] = id
                afterCreateCount += 1
              },
            ),
          )
        }

        Promise.all(promiseQueue).then(() => {
          // 通过模版创建微页面时，替换模版内原有内部页面跳转页面ID成新页面ID
          cachePages('get').forEach(page => {
            Object.keys(this.tplPageIdToSitePageIdMap).forEach(tplPageId => {
              page.content = page.content.replace(new RegExp(tplPageId, 'g'), this.tplPageIdToSitePageIdMap[tplPageId])
              cachePages('save', page)
            })
          })

          // 更新设计器内当前页面数据
          this.$refs.hexagon.updateCurrentPage(JSON.parse(cachePages('get', this.pages[0].id).content))
        })
      })
    },
    async _replaceFormId(formId) {
      const resp1 = await http.getFormDataById({ id: formId })
      const { data, errCode } = resp1
      if (errCode === 0) {
        delete data.id
        delete data.createTime
        delete data.createTimeStamp

        const resp2 = await http.addFormData(data)
        if (resp2.errCode === 0) {
          console.log('pageFormId >>>', resp2)
          return resp2.data.id
        }
      }

      return ''
    },
    // 获取模板下的页面列表（新建站点的时候，可选空白站点或模板站点，模板站点时，需调用此方法）
    _getPagesByTemplateSiteId(callback) {
      if (!this._templateSiteId) {
        return
      }
      http
        .getPagesByTemplateId({
          templateSiteId: this._templateSiteId,
        })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            const templatePages = data || []
            if (typeof callback === 'function') {
              callback(templatePages)
            }
          }
        })
    },
    // 获取站点下的页面列表
    _getPagesBySiteId(callback) {
      if (!this._siteId) {
        return
      }

      // 如果是模版编辑态，则取站点模版下页面列表
      if (this.isTemplate) {
        this._getPagesByTemplateSiteId(callback)
      } else {
        http.getPagesBySiteId({ siteId: this._siteId }).then(({ errCode, data }) => {
          if (errCode === 0) {
            console.log('_getPagesBySiteId', data)
            // this.pages = data;
            // setTimeout(() => {

            if (typeof callback === 'function') {
              callback(data || [])
            }
            // }, 0);
          }
        })
      }
    },
    // 获取页面详情
    _pageLoad(pageId) {
      return new Promise((resolve, reject) => {
        const content = cachePages('get', pageId).content || '{}'
        try {
          const curPageData = jsonparse(content)
          resolve(curPageData || {})
        } catch (e) {
          try {
            const curPageData = jsonparse(content.replace(/<span class=\\"ql-cursor\\">.*?<\/span>/g, ''))
            resolve(curPageData || {})
          } catch (err) {
            FxUI.Message.error($t('marketing.commons.dqymczwfjx_fddde4'))
            resolve({})
          }
        }

        // http.getPageDetail({ id: pageId }).then(({ errCode, data }) => {if (errCode === 0) {resolve(data);}});
      })
    },
    getImageFileWidth(file) {
      return new Promise(resolve => {
        const fr = new FileReader()
        fr.onload = function () {
          const img = new Image()
          img.onload = function () {
            resolve(img.width)
          }
          img.src = fr.result
        }
        fr.readAsDataURL(file)
      })
    },
    uploadFile(f, callback) {
      const formData = new FormData()
      formData.append('file', f)
      formData.append('type', 1)
      formData.append('needApath', true)
      formData.append('needPermanent', true)
      try {
        http
          .uploadFile(formData)
          .then(({ errCode, data = {} }) => {
            if (errCode === 0) {
              callback(data)
            } else {
              FxUI.Message.error($t('marketing.commons.tpscsbqzs_468a42'))
              callback(false)
            }
          })
          .catch(err => {
            FxUI.Message.error($t('marketing.commons.tpscsbqzs_468a42'))
            callback(false)
          })
      } catch (e) {
        FxUI.Message.error($t('marketing.commons.tpscsbqzs_468a42'))
        callback(false)
      }
    },
    // 设计器操作：创建页面
    _operationPageCreate() {
      this.createEmptyPage()
    },
    createEmptyPage() {
      return new Promise((resolve, reject) => {
        const emptyPage = this._createAPageObject({})
        this._fakeEditPage(emptyPage, (id, newPage) => {
          // this._getPagesBySiteId();
          cachePages('save', newPage)
          this.pages = cachePages('get')
          this.editPageId = id
          resolve(newPage)
        })
      })
    },
    // 设计器操作：页面重命名
    _operationPageRename(page) {
      this.pageRenamePropsName = page.name
      if (page.id === this.editPageId) {
        cachePages('save', {
          ...cachePages('get', page.id),
          content: JSON.stringify(this.$refs.hexagon.getCurrentPageData()),
        })
      }
      this.pageNameDialogVisible = true
    },
    // 设计器操作：复制页面
    _operationPageCopy(page) {
      const copyPageData = {
        ...cachePages('get', page.id),
      }
      // 不允复制带有表单组件的微页面
      if (copyPageData.content.match(/"isFormComp":true/) !== null) {
        FxUI.Message.warning($t('marketing.commons.dqymhybdzj_8b587e'))
        return
      }
      if (page.id === this.editPageId) {
        copyPageData.content = JSON.stringify(this.$refs.hexagon.getCurrentPageData())
      }
      copyPageData.isHomepage = 2
      copyPageData.shareOpts = copyPageData.shareOpts || {}
      // 这里只需要构造一个空页面，数据缓存在前端，点击保存时统一上传
      const newName = $t('marketing.commons.fz_7a6e24', { data: { option0: copyPageData.name } })
      this._fakeEditPage(
        this._createAPageObject({
          type: 'page',
          name: newName,
        }),
        id => {
          cachePages('save', {
            ...copyPageData,
            id,
            content: JSON.stringify({
              ...JSON.parse(copyPageData.content),
              name: newName,
            }),
            name: newName,
            shareDesc: copyPageData.shareOpts.desc,
            shareUrl: copyPageData.shareOpts.imgUrl,
            sharePosterUrl: copyPageData.shareOpts.sharePosterUrl,
            sharePosterAPath: copyPageData.shareOpts.sharePosterAPath,
            originalImageAPath: copyPageData.shareOpts.originalImageAPath || '',
            cutOffsetList: copyPageData.shareOpts.cutOffsetList || [],
            shareTitle: copyPageData.shareOpts.title,
          })
          this.pages = cachePages('get')
          this.editPageId = id
          // this._getPagesBySiteId();
        },
      )
    },
    _operationPageDelete(pageId) {
      confirm($t('marketing.commons.sfyscgymm_9ebd54'), $t('marketing.commons.ts_02d981'), {
        callback: action => {
          if (action === 'confirm') {
            this._fakeDeletePage(pageId, () => {
              cachePages('delete', pageId)
              this.pages = cachePages('get')
            })
          }
        },
      })
    },
    // 设置站点首页
    _operationPageHome(pageId) {
      const pages = cachePages('get')
      const pageList = pages.reduce((arr, item) => {
        if (pageId === item.id) {
          item.isHomepage = 1
          arr = [item, ...arr]
        } else {
          item.isHomepage = 2
          arr.push(item)
        }
        return arr
      }, [])
      this.pages = pageList
      cachePages('saveAll', pageList)
    },
    /**
     * 创建/编辑站点
     * params: 1-启用状态 5-不可见(草稿)状态
     * */
    async _fakeEditSite(params, callback, errCallback) {
      const result = res => {
        if (res && res.errCode === 0) {
          this.saveStatus = 2
          this._siteId = res.data.id

          // 模版ID赋值
          if (this.isTemplate) {
            this._templateSiteId = res.data.id
          }
          console.log('_fakeEditSite', this._siteId)
          if (typeof callback === 'function') {
            callback(res.data)
          }
        } else {
          this.saveStatus = 3
          if (typeof errCallback === 'function') {
            errCallback()
          } else {
            FxUI.Message.error($t('marketing.commons.zdbcsbqzs_a640f0'))
          }
        }
        setTimeout(() => {
          this.saveStatus = 0
        }, 1000)
      }

      const { groupId } = this.$route.query
      if (groupId) {
        params.groupId = groupId
      }

      // 微页面模版编辑创建
      if (this.isTemplate) {
        const coverParams = await this.homePageScreenshot(this.pages)
        params = {
          ...params,
          ...coverParams,
        }
        http
          .editTemplateSite({
            status: this.siteStatus,
            ...params,
          })
          .then(result)
      } else {
        http
          .fakeEditSite({
            status: this.siteStatus,
            ...params,
          })
          .then(result, () => {
            if (typeof errCallback === 'function') {
              errCallback()
            } else {
              FxUI.Message.error($t('marketing.commons.zdbcsbqzs_a640f0'))
            }
          })
      }
    },

    // 快捷生成一个页面对象
    _createAPageObject(page = {}) {
      const pageObj = {
        id: page.id || '',
        category: '',
        hexagonSiteId: this._siteId,
        hexagonTemplateSiteId: this._siteId,
        content:
          page.content
          || JSON.stringify({
            type: 'page',
            name: page.name || $t('marketing.commons.dy_c1e444', { data: { option0: this.pages.length + 1 } }),
            isHomepage: page.isHomepage || 2, // 是否首页： 1-是首页 2-不是首页
          }),
        name: page.name || $t('marketing.commons.dy_c1e444', { data: { option0: this.pages.length + 1 } }),
        isHomepage: page.isHomepage || 2, // 是否首页： 1-是首页 2-不是首页
        shareDesc: page.shareDesc,
        shareUrl: page.shareUrl,
        shareTitle: page.shareTitle,
        status: 1,
      }

      const { groupId } = this.$route.query
      if (groupId) {
        pageObj.groupId = groupId
      }

      return pageObj
    },

    /**
     * 创建/编辑页面
     * */
    _fakeEditPage(page, callback) {
      if (!this._siteId) {
        FxUI.Message.error(`${$t('marketing.commons.ymxj_d25e0f')}/${$t('marketing.commons.bcsbqsxymz_c46f20')}`)
        return undefined
      }
      const result = ({ errCode, data }) => {
        if (errCode === 0) {
          if (typeof callback === 'function') {
            callback(data.id, {
              ...page,
              id: data.id,
            })
          }
        }
      }
      if (this.isTemplate) {
        return http.editTemplatePage(page).then(result)
      }
      return http.fakeEditPage(page).then(result)
    },
    /**
     * 创建/编辑页面
     * */
    _fakeDeletePage(pageId, callback) {
      if (!pageId) {
        FxUI.Message.error($t('marketing.commons.ymscsbqsxy_d598ec'))
        return
      }
      const result = ({ errCode, data }) => {
        if (errCode === 0 && typeof callback === 'function') {
          callback()
        }
      }
      if (this.isTemplate) {
        http.deleteTemplatePage({ id: pageId }).then(result)
      } else {
        http.fakeDeletePage({ id: pageId }).then(result)
      }
    },
    // 批量创建/编辑页面
    async _fakeEditPages(siteId, pages, callback) {
      let _fakeSucCount = 0
      pages.forEach(async (page, index) => {
        const pageContent = JSON.parse(page.content) || {};
        const { formId } = await FormModule.sumitPageForm(page.formId, pageContent);
        page.ctaIds = FormModule.getCtaIds(pageContent);
        function whenAllSuccess() {
          if (_fakeSucCount === pages.length && typeof callback === 'function') {
            // FxUI.Message.success("保存成功");
            callback(siteId)
          }
        }

        const result = res => {
          if (res && res.errCode === 0) {
            // 将formId塞到这个page下
            cachePages('save', {
              ...cachePages('get', page.id),
              formId,
            })
            _fakeSucCount += 1
            whenAllSuccess()
          }
        }

        const { groupId = '' } = this.$route.query

        // 编辑创建模版页面
        if (this.isTemplate) {
          http
            .editTemplatePage({
              ...page,
              formId,
              groupId,
              hexagonTemplateSiteId: (!this.isEdit && this._templateSiteId) || null, // 新建且有模板时，传给后台
            })
            .then(result)
        } else {
          http
            .fakeEditPage({
              ...page,
              formId,
              groupId,
              templateSiteId: (!this.isEdit && this._templateSiteId) || null, // 新建且有模板时，传给后台
            })
            .then(result)
        }

        // });
      })
    },
    _formatPagesItem(item) {
      return {
        content: item.content,
        formId: item.formId,
        hexagonSiteId: item.hexagonSiteId,
        hexagonTemplateSiteId: item.hexagonTemplateSiteId,
        id: item.id,
        isHomepage: item.isHomepage,
        name: item.name,
        shareDesc: item.shareDesc,
        sharePicH5Path: item.sharePicH5Path,
        sharePicMpPath: item.sharePicMpPath,
        shareTitle: item.shareTitle,
        status: 1,
      }
    },

    /**
     * 组件数据钩子
     * @param data
     */
    beforeCompDataCreate(data) {
      const { value } = data

      switch (value) {
        case 'dhtComp':
          // 判断是否开启订货通插件
          const { marketingOrderIntegration } = this.vData.uinfo

          if (!marketingOrderIntegration) {
            // 没有开启，隐藏订货通组件
            data.hide = true
          }
          return data
        case 'memberComp':
          // 判断是否开启会员组件
          const { memberLoyaltyPlanEnabled } = this.vData.uinfo

          if (!memberLoyaltyPlanEnabled) {
            // 没有开启，隐藏会员组件
            data.hide = true
          }
          return data
        default:
          return data
      }
    },
    handleEventOrderTypeChange(val) {
      const compOriginData = this.$refs.hexagon.getCurrentCompData()
      this.$refs.hexagon.updateCurrentCompData({
        ...compOriginData,
        orderType: val || 0,
      })
    },
  },
}
</script>

<style lang="less">
.marketing-site__designer {
  box-sizing: border-box;
  &--agent {
    overflow: auto;
    height: 100%;
  }
  :global{
    .el-textarea__inner{
      border: none;
    }
  }
  .hexagon-saving-tip {
    color: @color-subtitle;
    font-size: 12px;
    padding: 0 10px;
    opacity: 0;
    transition: all 0.3s ease;
    &.showtip {
      opacity: 1;
    }
  }
  * {
    box-sizing: border-box;
  }
  ol {
    list-style: decimal;
    padding-left: 16px;
  }
  ul {
    list-style: disc;
    padding-left: 16px;
  }
  div[class^='hexagon__logo_'] {
    flex: 1;
    min-width: 0;
  }
  .site_header {
    display: flex;
    align-items: center;
  }
  .site_header__label {
    font-size: 22px;
    color: #212b36;
    display: flex;
    flex-shrink: 0;
  }
  .site_header__dividing {
    width: 1px;
    height: 18px;
    background: #e9edf5;
    margin: 0 22px;
  }
  .site_header__name {
    color: #545861;
    font-size: 16px;
    min-width: 0;
  }
  .name__display {
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name__edit {
    .edit__input {
      input {
        border: 0;
        border-bottom: 1px solid #ddd;
        font-size: 16px;
      }
    }
  }
  .el-tabs--border-card {
    border: 0;
  }
  .fs__vue-quill {
    -moz-user-select: text;
    -webkit-user-select: text;
    -ms-user-select: text;
    user-select: text;
    .ql-container.ql-snow {
      border-top: 0;
    }
  }
  .site__function-select {
    cursor: pointer;
    &-btn {
      border: 1px solid #e9edf5;
      height: 32px;
      display: block;
      color: var(--color-primary06, #407fff);
      z-index: 1;
      padding-left: 15px;
      line-height: 32px;
      i {
        margin-right: 5px;
        font-weight: bolder;
      }
    }
    .function-input {
      input {
        cursor: pointer;
      }
    }
  }
  .site__function-title {
    color: #545861;
    font-size: 12px;
    margin-bottom: 10px;
    margin-top: 20px;
  }
  .site__function-params {
    border: 1px solid @border-color-base;
    padding: 8px 15px;
    margin-top: 10px;
    border-radius: 4px;
    min-height: 100px;
    user-select: text;
    word-break: break-all;
    background: #f8f8fa;
  }

  .hexagon__activity {
    margin-top: -10px;
    padding: 0 14px 0 154px;
    .activity-item {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid @border-color-base;
      margin-bottom: 10px;
      .left-content{
        display: flex;
        align-items: center;
        flex: 1;
        width: 0;
        .image {
          width: 63px;
          height: 50px;
          object-fit: cover;
        }
        .title {
          font-size: 13px;
          color: @color-title;
          flex: 1;
          padding: 0 10px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .right-content{
        width: 65px;
        display: flex;
        align-items: center;
        .drag_handle{
          font-size: 16px;
          margin: 0 16px 0 5px;
          cursor: pointer;
        }
      .remove {
        font-size: 16px;
        cursor: pointer;
        margin-right: 13px;
      }
      }
    }

    .hexagon__activity_tags {
      min-height: 32px;
      border: 1px solid #DCDFE6;
      border-radius: 4px;
      line-height: 32px;
      padding: 0 15px;
      cursor: pointer;
      color: #407FFF;

      .tag-item {
        border: 1px solid #DCDFE6;
        color: #181c25;
        padding: 2px 4px;
        margin-right: 6px;
        word-break: keep-all;
        display: inline-block;
        line-height: 16px;
        border-radius: 2px;
        background: #f2f4fb;
      }
    }
  }
  .sort-tips{
    padding: 10px 14px 10px 154px;
    color: rgb(145, 149, 158);
    font-size: 12px;
  }
  .marketing-hexagon__setting_item{
    padding: 0 14px;
    margin-top: 15px;
    display: flex;
    .marketing-hexagon__setting_title{
      width: 140px;
      padding: 4px 0;
      line-height: 20px;
      overflow: inherit;
      word-wrap: normal;
      text-overflow: initial;
      white-space: inherit;
    }
  }
}
.hexagon__picker-inputbar {
  margin-top: 5px;
  width: 100%;
  height: 32px;
  display: flex;
  border: 1px solid #e9edf5;
  box-sizing: border-box;
  border-radius: 3px;
  cursor: pointer;
  input {
    display: block;
    flex: 1 0 auto;
    padding: 7px 0 7px 10px;
    border-color: transparent;
    cursor: pointer;
    border-right: 1px solid #e9edf5;
  }
  > div {
    &:last-child {
      width: 36px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      > .el-icon-search {
        font-size: 16px;
        color: #b4b6c0;
        font-weight: 600;
      }
    }
  }
  .disabled {
    background-color: #f5f7fa;
    // border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }
}
.site__payform-pupop {
  .site__payform-pupop-item {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
