<template>
  <div :class="$style.vibeMarketing">
    <div :class="$style.header">
      <fx-menu
        :default-active="currentTab"
        mode="horizontal"
        @select="handleTabChange"
      >
        <div :class="$style.menutitle">
          <span>Vibe Marketing</span>
          <span :class="$style.splititem" />
        </div>
        <fx-menu-item
          :class="$style.menuItem"
          index="vibe-marketing-plan"
        >
          策划
        </fx-menu-item>
        <fx-menu-item
          :class="$style.menuItem"
          index="vibe-marketing-plan-new"
        >
          策划（新）
        </fx-menu-item>
        <fx-menu-item
          :class="$style.menuItem"
          index="vibe-marketing-workspace"
        >
          工作空间
        </fx-menu-item>
        <fx-menu-item
          :class="$style.menuItem"
          index="vibe-marketing-idea"
        >
          创意
        </fx-menu-item>
        <fx-menu-item
          :class="$style.menuItem"
          index="vibe-marketing-setting"
        >
          营销大脑
        </fx-menu-item>
      </fx-menu>
    </div>
    <div :class="$style.content">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  name: 'VibeMarketing',
  data() {
    return {}
  },
  computed: {
    currentTab() {
      return this.$route.name || 'vibe-marketing-plan'
    },
  },
  methods: {
    handleTabChange(type) {
      this.$router.push({
        name: type,
      })
    },

  },
}
</script>

<style lang="less" module>
.vibeMarketing {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  .header {
    .menutitle {
      float: left;
      height: 100%;
      color: #181c25;
      font-size: 16px;
      vertical-align: middle;
      height: 55px;
      line-height: 55px;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: 17px;

      .splititem {
        height: 12px;
        width: 0;
        border-left: 1px solid #e8e8e8;
        margin-left: 27px;
        margin-right: 26px;
      }
    }

    .menuItem {
      margin: 0 40px 0 0;
      padding: 0;
      height: 55px;
      line-height: 55px;
    }
  }

  .content {
    flex: 1;
    overflow: auto;
    &::-webkit-scrollbar{
      width: 6px;
      height: 10px;
      background-color: #dfe3e8;
    }
  }
}
</style>
