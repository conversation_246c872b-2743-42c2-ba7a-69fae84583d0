<template>
  <div :class="classPrefix">
    <div :class="`${classPrefix}__content`">
      <apply-tmp-form
        :prompt="promptDetail.objectData.content"
        :pre-chat-result="{}"
        @onChange="handlePromptTextChange"
      />
    </div>
  </div>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import http from '@/services/http/index.js'
import ApplyTmpForm from './apply-tmp-form.vue'

export default {
  name: 'ApplyTemplate',
  components: {
    ApplyTmpForm,
  },
  props: {
    promptDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      classPrefix: 'ai-apply-tmp__page',
      iconSend: `${$cdnPath}/images/ai-icon-send-white.svg`,
      aiHelperList: [],
    }
  },
  created() {
    this.promptResultsRef = {}
    this.fetchAiHelperList()
  },
  methods: {
    fetchAiHelperList() {
      const params = {
        objectAPIName: 'AIHelperObj',
        pageNum: 1,
        pageSize: 50,
        selectFields: ['name', 'role', 'task', 'paas_agent'],
        query: JSON.stringify({ filters: [] }),
      }

      http.pageQueryObjDatas(params)
        .then(res => {
          const { errCode, data = {} } = res
          if (errCode === 0) {
            this.aiHelperList = data.result || {}
          }
        })
    },
    handlePromptTextChange(e) {
      this.promptResultsRef = {
        ...this.promptResultsRef,
        ...e,
      }
    },
    getSendPayload() {
      if (!this.promptResultsRef.isValidate) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qtcsycs_13e979'))
        return null
      }

      const {
        objectData: {
          aihelper_id: aiHelperId = '',
          _id,
        } = {},
      } = this.promptDetail

      const activeAgent = this.aiHelperList.find(item => item._id === aiHelperId)
      const payload = {
        prompt: this.promptResultsRef.promptText,
        templateId: _id,
        defaultHelperName: activeAgent ? activeAgent.name : $t('marketing_taro.reducers.mrzs_31e25b'),
        activeAgentId: activeAgent ? activeAgent._id : '',
      }

      if (this.promptResultsRef.objects && this.promptResultsRef.objects.length) {
        payload.objects = this.promptResultsRef.objects
      }

      return payload
    },
  },
}
</script>

<style lang="less" scoped>
.ai-apply-tmp__page {
  display: flex;
  flex-direction: column;
  background-color: #F7F8FA;
  height: 100%;

  &__content {
    background-color: #F7F8FA;
    box-sizing: border-box;
    height: 0;
    width: 100%;
    flex: 1;
  }
}
</style>
