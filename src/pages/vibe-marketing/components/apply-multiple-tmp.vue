<template>
  <div
    :class="[classPrefix, 'wide', 'marketing-scrollbar-theme']"
    :v-loading="isLoading"
  >
    <Empty
      v-if="templateList.length === 0"
      class="empty"
      :title="$t('marketing_taro.pkgs.kkry_6afb79')"
    />
    <template v-else>
      <div :class="`${classPrefix}__stage`">
        <div
          v-for="(item, index) in templateList"
          :key="item._id"
          :class="[`${classPrefix}__stage-item`, { active: index <= stageIndex }]"
        >
          <div :class="`${classPrefix}__stage-item-content`">
            <img
              v-if="index < stageIndex"
              :src="iconCompleted"
              :class="`${classPrefix}__stage-item-icon`"
            >
            <div
              v-else
              :class="`${classPrefix}__stage-item-serial`"
            >
              {{ index + 1 }}
            </div>
            <div :class="`${classPrefix}__stage-item-title`">
              {{ item.title }}
            </div>
          </div>
          <img
            :src="iconDots"
            :class="`${classPrefix}__stage-item-dot`"
          >
        </div>
      </div>
      <div :class="`${classPrefix}__content-wrap`">
        <div
          v-for="(item, index) in templateList"
          :key="item._id"
          :class="[`${classPrefix}__content`, { active: index === stageIndex }]"
        >
          <apply-tmp-form
            :class="`${classPrefix}__tmp-form`"
            :prompt="item.content"
            :pre-chat-result="chatResult"
            @onChange="e => handlePromptTextChange(e, index)"
            @onPrePromptChange="handlePrePromptChange"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import http from '@/services/http/index.js'
import Empty from '@/components/common/empty.vue'
import ApplyTmpForm from './apply-tmp-form.vue'

const POLLING_TIMEOUT = 5 * 60 * 1000 // 5 min
const POLLING_RATE = 5000

export default {
  name: 'ApplyMultipleTemplate',
  components: {
    Empty,
    ApplyTmpForm,
  },
  props: {
    promptDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      classPrefix: 'ai-apply-multiple-tmp__page',
      isLoading: true,
      templateList: [],
      stageIndex: 0,
      isAIHandling: false,
      chatResult: {},
      aiHelperList: [],
      iconDots: `${$cdnPath}/images/ai-icon-dots.svg`,
      iconSend: `${$cdnPath}/images/ai-icon-send-white.svg`,
      iconCompleted: `${$cdnPath}/images/ai-icon-completed.svg`,
    }
  },
  watch: {
    templateList() {
      this.$emit('update:data', {
        templateList: this.templateList,
      })
    },
    stageIndex() {
      this.$emit('update:data', {
        stageIndex: this.stageIndex,
      })
    },
    isAIHandling() {
      this.$emit('update:data', {
        isAIHandling: this.isAIHandling,
      })
    },
  },
  created() {
    this.promptResultsRef = {}
    this.pollingRef = null
    this.initTemplateList()
    this.fetchAiHelperList()
  },
  beforeDestroy() {
    if (this.pollingRef) {
      clearInterval(this.pollingRef)
    }
  },
  methods: {
    fetchAiHelperList() {
      const params = {
        objectAPIName: 'AIHelperObj',
        pageNum: 1,
        pageSize: 50,
        selectFields: ['name', 'role', 'task', 'paas_agent'],
        query: JSON.stringify({ filters: [] }),
      }

      http.pageQueryObjDatas(params)
        .then(res => {
          const { errCode, data = {} } = res
          if (errCode === 0) {
            this.aiHelperList = data.result || {}
          }
        })
    },
    async initTemplateList() {
      const {
        objectData: {
          _id,
        } = {},
      } = this.promptDetail

      const payload = {
        pageSize: 20,
        pageNum: 1,
        objectAPIName: 'PromptObj',
        selectFields: [
          'name', 'category', 'title', 'content', 'status',
          'last_modified_time', 'aihelper_id', 'step_split',
          'pre_prompt', 'parent_prompt',
        ],
        query: JSON.stringify({
          wheres: [
            {
              connector: 'OR',
              filters: [
                {
                  field_name: 'parent_prompt',
                  field_values: [_id],
                  operator: 'EQ',
                },
              ],
            },
            {
              connector: 'OR',
              filters: [
                {
                  field_name: '_id',
                  field_values: [_id],
                  operator: 'EQ',
                },
              ],
            },
          ],
        }),
      }

      this.isLoading = true

      try {
        const res = await http.pageQueryObjDatas(payload)
        const { errCode, data } = res || {}
        if (errCode === 0) {
          const { result = [] } = data || {}
          let lastOne = result.find(item => !item.pre_prompt)
          const sortedTemplateList = [lastOne]

          while (lastOne) {
            const currentId = lastOne._id
            lastOne = result.find(item => item.pre_prompt === currentId)
            if (lastOne) {
              sortedTemplateList.push(lastOne)
            }
          }

          this.templateList = sortedTemplateList
        }
      } catch (error) {
        console.error(error)
      } finally {
        this.isLoading = false
      }
    },
    handlePromptTextChange(e, index) {
      const { _id } = this.templateList[index]
      const result = this.promptResultsRef[_id] || {}
      this.$set(this.promptResultsRef, _id, {
        ...result,
        ...e,
      })
    },
    handlePrePromptChange(key, value) {
      this.chatResult = {
        ...this.chatResult,
        [key]: value,
      }
    },
    handlePrevStage(prevIndex) {
      clearInterval(this.pollingRef)
      this.isAIHandling = false
      this.stageIndex = prevIndex
    },
    async handleNextStage(nextIndex) {
      if (this.isAIHandling) return

      const { _id, aihelper_id: aiHelperId } = this.templateList[this.stageIndex]
      const { promptText, isValidate, objects = {} } = this.promptResultsRef[_id] || {}

      if (!isValidate) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qtcsycs_13e979'))
        return
      }

      this.isAIHandling = true
      const activeAgent = this.aiHelperList.find(item => item._id === aiHelperId)
      const payload = {
        prompt: decodeURIComponent(promptText),
        defaultHelperName: activeAgent ? activeAgent.name : $t('marketing_taro.reducers.mrzs_31e25b'),
        templateId: _id,
        arg: {
          objects,
        },
      }

      http.chatCompleteWithoutSession(payload)
        .then(res => {
          const { errCode, errMsg } = res
          if (errCode !== 0) {
            throw new Error(errMsg)
          }

          return res.data
        })
        .then(chatId => {
          clearInterval(this.pollingRef)

          return new Promise((resolve, reject) => {
            let timeoutRef = null
            this.pollingRef = setInterval(() => {
              http.getChatCompleteResult({ id: chatId })
                .then(resp => {
                  if (resp.errCode === 0 && resp.data) {
                    clearInterval(this.pollingRef)
                    clearTimeout(timeoutRef)

                    resolve(resp.data)
                  }

                  if (resp.errCode !== 0) {
                    clearInterval(this.pollingRef)
                    clearTimeout(timeoutRef)
                    reject()
                  }
                })
            }, POLLING_RATE)

            timeoutRef = setTimeout(() => {
              clearInterval(this.pollingRef)
              resolve()
            }, POLLING_TIMEOUT)
          })
        })
        .then(chat => {
          this.isAIHandling = false
          if (chat && chat.content) {
            const { title } = this.templateList[this.stageIndex]
            this.$set(this.chatResult, title, chat.content)
            this.stageIndex = nextIndex
          }
        })
        .catch(() => {
          this.isAIHandling = false
        })
    },
    getSendPayload() {
      const { _id, aihelper_id: aiHelperId = '' } = this.templateList[this.stageIndex]
      const { promptText, isValidate, objects } = this.promptResultsRef[_id] || {}

      if (!isValidate) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qtcsycs_13e979'))
        return null
      }

      const activeAgent = this.aiHelperList.find(item => item._id === aiHelperId)
      const payload = {
        prompt: promptText,
        templateId: _id,
        defaultHelperName: activeAgent ? activeAgent.name : $t('marketing_taro.reducers.mrzs_31e25b'),
        activeAgentId: activeAgent ? activeAgent._id : '',
      }

      if (objects && objects.length) {
        payload.objects = objects
      }

      return payload
    },
  },
}
</script>

<style lang="less" scoped>
.ai-apply-multiple-tmp__page {
  display: flex;
  flex-direction: column;
  background-color: #F7F8FA;
  height: 100%;
  overflow-y: auto;

  &__stage {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    border-bottom: 0.5px solid #DEE1E8;
    background-color: #fff;
  }

  &__stage-item {
    display: flex;
    align-items: center;
    color: #545861;
    margin-right: 8px;

    &.active {
      color: #181C25;

      .ai-apply-multiple-tmp__page__stage-item-serial {
        background: #5B70EA;
      }
    }

    &:last-of-type {
      margin-right: 0;

      .ai-apply-multiple-tmp__page__stage-item-dot {
        display: none;
      }
    }
  }

  &__content-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  &__stage-item-content {
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  &__stage-item-icon {
    width: 20px;
    height: 20px;
  }

  &__stage-item-serial {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #C1C5CE;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    color: #FFF;
    flex: none;
  }

  &__stage-item-title {
    font-size: 13px;
    line-height: 20px;
    margin-top: 4px;
  }

  &__stage-item-dot {
    width: 24px;
    height: 3px;
    margin-left: 4px;
  }

  &__content {
    display: none;

    &.active {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  &__tmp-form {
    flex: 1;
  }

  &__bottom {
    padding-bottom: 24px;
    display: flex;
    box-sizing: border-box;
    margin-top: 24px;
    justify-content: flex-end;

    .ai-apply-multiple-tmp__page__bottom--btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      background-color: #5B70EA;
      border: 0.5px solid #5B70EA;
      border-radius: 2px;
      font-weight: 400;
      font-size: 13px;
      color: #fff;
      cursor: pointer;
      padding: 0 15px;
      margin: inherit;
      margin-left: 10px;
      width: auto;
      min-width: 70px;

      &::after {
        display: none;
      }

      &.loading {
        cursor: wait;
      }

      &.prev {
        border-color: #DEE1E8;
        background: #FFF;
        color: #181C25;
      }

      .icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        font-size: 0;
      }
    }
  }

  &.wide {
    .ai-apply-multiple-tmp__page__stage-item-content {
      flex-direction: row;
    }
    .ai-apply-multiple-tmp__page__stage-item-title {
      margin-left: 4px;
      margin-top: 0;
    }

    .ai-apply-multiple-tmp__page__bottom--btn {
      min-width: 100px;
    }
  }
}
</style>
