<template>
  <div
    :class="[classPrefix, 'wide']"
    @click="handleApply"
  >
    <div :class="`${classPrefix}__left`">
      <div :class="`${classPrefix}__title`">
        {{ promptDetail.objectData.title }}
      </div>
      <div :class="`${classPrefix}__info`">
        <div :class="`${classPrefix}__avatar`">
          <img
            :src="promptDetail.avatar || defaultAvatarIcon"
          >
        </div>
        <div :class="`${classPrefix}__username`">
          {{ promptDetail.userName }}
        </div>
        <img
          v-if="promptDetail.isMine"
          :class="`${classPrefix}__edit`"
          :src="iconEdit"
          @click.stop="handleToEdit"
        >
      </div>
    </div>
    <div :class="`${classPrefix}__right`">
      <div :class="`${classPrefix}__collect`">
        <img
          :class="`${classPrefix}__collect-icon`"
          :src="isMark ? iconCollected : iconUncollected"
          @click.stop="handleCollect"
        >
        <div
          v-if="collectCount > 0"
          :class="`${classPrefix}__collect-count`"
        >
          {{ collectCount }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import { $cdnPath } from '@/utils/constant.js'

export default {
  name: 'TemplateItem',
  props: {
    promptDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      classPrefix: 'ai-template-item',
      iconCollected: `${$cdnPath}/images/ai-icon-collected.svg`,
      iconUncollected: `${$cdnPath}/images/ai-icon-uncollected2.svg`,
      defaultAvatarIcon: `${$cdnPath}/images/default_personal_header.png`,
      iconEdit: `${$cdnPath}/images/ai-icon-edit.svg`,
      isMark: false,
      collectCount: 0,
      applyTmpDialogVisible: false,
    }
  },
  created() {
    const {
      objectData = {},
      isMark = false,
    } = this.promptDetail

    this.collectCount = parseInt(objectData.number_of_collections || 0, 10) || 0
    this.isMark = !!isMark
    this.isLoading = false
  },
  methods: {
    handleApply() {
      const {
        objectData: {
          _id = '',
          category = '',
        } = {},
      } = this.promptDetail
      
      if(category === '9') {
        // 当创意分类为图片时，直接大概AI生成图片接口
        this.$emit('openAiPosterDialog', this.promptDetail.objectData)
      }else {
        this.$router.push({
          name: 'vibe-marketing-apply-tmp',
          query: {
            id: _id,
          },
        })
      }
    },
    handleCollect() {
      if (this.isLoading) return

      this.isLoading = true

      const {
        objectData: {
          _id = '',
        } = {},
      } = this.promptDetail

      http.aiChatMarkPrompt({ id: _id })
        .then(res => {
          this.isLoading = false
          const { errCode } = res || {}
          if (errCode === 0) {
            const delta = this.isMark ? -1 : 1
            this.isMark = !this.isMark
            this.collectCount += delta
          }
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    handleToEdit(e) {
      this.$router.push({
        name: 'vibe-marketing-create-tmp',
        query: {
          id: this.promptDetail.objectData._id,
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
.ai-template-item {
  box-sizing: border-box;
  padding: 12px;
  display: flex;
  width: 100%;
  margin-bottom: 12px;
  cursor: pointer;
  background: #FFF;
  border-radius: 6px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

  &.wide {
    width: calc((100% - 24px) / 3);
    margin-right: 12px;

    &:nth-child(3n) {
      margin-right: 0;
    }
  }

  .multi-ellipsis(@row) {
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: @row;
    overflow: hidden;
  }

  &__left {
    flex: 1;
  }

  &__title {
    font-weight: 400;
    font-size: 14px;
    color: #181C25;
    line-height: 20px;
    .multi-ellipsis(1);
  }

  &__info {
    display: flex;
    align-items: center;
    margin-top: 10px;
  }
  &__avatar {
    width: 16px;
    height: 16px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      overflow: hidden;
    }
  }
  &__username {
    color: #91959E;
    font-size: 14px;
    line-height: 20px;
    margin-left: 4px;
  }
  &__edit {
    width: 16px;
    height: 16px;
    margin: 0 0 0 4px;
    cursor: pointer;
  }
  &__right {
    display: flex;
    align-items: center;
    padding: 0 12px;
  }
  &__collect {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  &__collect-icon {
    width: 20px;
    height: 20px;
    margin: 0;
  }
  &__collect-count {
    color: #91959E;
    font-size: 12px;
    line-height: 18px;
    margin-top: 4px;
  }
}
</style>
