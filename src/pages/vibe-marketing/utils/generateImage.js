/**
 * AI海报生成相关接口
 */

/**
 * 解析SSE消息数据
 * @param {string} data SSE消息数据
 * @returns {Object} 解析后的数据
 */
const parseData = (data) => {
  try {
    return JSON.parse(JSON.parse(data))
  } catch (error) {
    return data
  }
}

/**
 * 生成AI海报
 * @param {Object} params 生成参数
 * @param {string} params.prompt 提示词
 * @param {string} params.size 图片尺寸
 * @param {string} params.quality 图片质量
 * @param {number} params.n 生成数量
 * @param {string} [params.image] 参考图片路径
 * @param {Object} options 请求配置
 * @param {AbortSignal} options.signal 用于取消请求的信号
 * @returns {Promise<string[]>} 返回base64图片列表的Promise
 */
export const generateImage = (params, options = {}) => {
  const { signal } = options
  const employee = FS.contacts.getCurrentEmployee() || {}
  const enterpriseId = employee.enterpriseId || '84847'
  const enterpriseAccount = employee.enterpriseAccount || 'fsceshi003'
  const employeeId = employee.employeeId || '1017'

  return new Promise((resolve, reject) => {
    const images = []

    Fx.sseApi('/FHS/EM1HAIGC/sse/imageGenerate', {
      body: {
        model: 'gpt-image-1',
        prompt: params.prompt,
        size: params.size,
        quality: params.quality,
        n: params.n,
        image: params.image
      },
      headers: {
        'x-fs-enterprise-id': enterpriseId,
        'X-fs-Enterprise-Account': enterpriseAccount,
        'x-fs-employee-id': employeeId
      },
      signal,
      openWhenHidden: true,
      onmessage: msg => {
        console.log('generateImage onmessage:', msg)
        if (msg.event === 'heartbeat') return
        const { data } = parseData(msg.data)
        if (data && Array.isArray(data)) {
          const newImages = data.map(item =>
            `data:image/png;base64,${item.b64_json}`
          ).filter(Boolean)
          images.push(...newImages)
        }
      },
      onerror: error => {
        console.log('generateImage onerror:', error)
        reject(error)
      }
    }).then(() => {
      console.log('generateImage resolve:', images)
      resolve(images)
    }).catch(error => {
      console.log('generateImage reject:', error)
      reject(error)
    })
  })
}
