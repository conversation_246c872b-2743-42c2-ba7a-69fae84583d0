<template>
  <div
    ref="applyTmpPage"
    :class="$style['apply-tmp__page']"
  >
    <div
      ref="applyTmpPageHeader"
      :class="$style['apply-tmp__page__header']"
    >
      <content-header
        :title="crumbs"
        :border="true"
      >
        <div
          slot="right"
          :class="$style['apply-tmp__page__options']"
        >
          <fx-button
            size="small"
            type="primary"
            @click="handleOpenApplyTmpDialog"
          >
            发送
          </fx-button>
        </div>
      </content-header>
    </div>
    <div :class="$style['apply-tmp__page__content']">
      <div
        ref="applyTmpPageContentWrapper"
        :class="$style['apply-tmp__page__content-wrapper']"
      >
        <div :class="$style['apply-tmp__page__content__left']">
          <BaseShareGPT
            ref="ShareGPT"
            :default-helper-name="promptDetail.objectData && promptDetail.objectData.aihelper_id"
            @onReady="handleShareGPTReady"
          />
        </div>
        <div :class="$style['apply-tmp__page__content__right']">
          <ArticleTinymceEditor
            :height="editorHeight"
            :content="message && message.previewContent"
            @setEditorContent="handleSetEditorContent"
          />
        </div>
      </div>
    </div>

    <ApplyTmpDialog
      v-if="applyTmpDialogVisible"
      :visible="applyTmpDialogVisible"
      :prompt-detail="promptDetail"
      @onClose="handleCloseApplyTmpDialog"
      @onSend="handleSend"
    />
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import ContentHeader from '@/components/content-header/index.vue'
import ArticleTinymceEditor from '@/pages/article/edit/ArticleTinymceEditor.vue'
import ApplyTmpDialog from '../../components/apply-tmp-dialog.vue'
import BaseShareGPT from '../../components/baseShareGPT.vue'

export default {
  components: {
    ContentHeader,
    ApplyTmpDialog,
    BaseShareGPT,
    ArticleTinymceEditor,
  },
  data() {
    return {
      crumbs: [
        {
          text: 'Vibe Marketing',
          to: { name: 'vibe-marketing-init' },
        },
        {
          text: '创意',
          to: { name: 'vibe-marketing-idea' },
        },
        {
          text: '',
          to: false,
        },
      ],
      promptDetail: {},
      applyTmpDialogVisible: false,
      message: null,
      editorHeight: 200,
    }
  },
  computed: {
    bizSessionId() {
      const { id } = this.$route.query
      const { enterpriseAccount, employeeId } = FS.contacts.getCurrentEmployee() || {}
      return `apply_template_${enterpriseAccount || 0}_${employeeId || 0}_${id}`
    },
  },
  created() {
    this.fetchTemplateDetail()
  },
  mounted() {
    // 这里需要延迟等编辑器初始化好
    setTimeout(() => {
      this.initEditorHeight()
    }, 1000)
    window.addEventListener('resize', this.initEditorHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initEditorHeight)
  },
  methods: {
    initEditorHeight() {
      const { applyTmpPage, applyTmpPageHeader } = this.$refs

      const { height: applyTmpPageHeight } = applyTmpPage.getBoundingClientRect()
      const { height: applyTmpPageHeaderHeight } = applyTmpPageHeader.getBoundingClientRect()

      const height = applyTmpPageHeight - applyTmpPageHeaderHeight - 16 - 16
      this.editorHeight = height
    },
    fetchTemplateDetail() {
      const { id } = this.$route.query
      if (!id) return

      http.queryObjDataById({
        objectAPIName: 'PromptObj',
        selectFields: [
          'name',
          'category',
          'title',
          'content',
          'status',
          'last_modified_time',
          'aihelper_id',
          'step_split',
          'created_by',
          'number_of_collections',
          'scope_type',
        ],
        id,
      }).then(res => {
        if (res && res.errCode === 0) {
          this.promptDetail = {
            objectData: res.data,
          }

          if (res.data.title) {
            this.crumbs[2].text = res.data.title
          }

          this.applyTmpDialogVisible = true
        }
      })
    },
    handleOpenApplyTmpDialog() {
      this.applyTmpDialogVisible = true
    },
    handleCloseApplyTmpDialog() {
      this.applyTmpDialogVisible = false
    },
    handleSend(payload) {
      if (!this.marktingAIHelper) return
      const {
        activeAgentId = '',
        defaultHelperName = '',
        objects = [],
        prompt = '',
        templateId = '',
      } = payload

      this.marktingAIHelper.sendConfigData({
        activeAgentId,
        defaultHelperName,
      })

      const message = {
        msg: prompt,
        templateId,
        objects,
      }
      this.marktingAIHelper.sendMessageToAI(message, res => {
        console.log('sendMessageToAI', res)
        const { errMsg, errCode } = res
        if (errCode === 0 && errMsg === 'receivePeriod:resolve') {
          this.message = res.message
        }
      })
    },
    handleShareGPTReady(marktingAIHelper) {
      this.marktingAIHelper = marktingAIHelper

      marktingAIHelper.sendConfigData({
        bizSessionId: this.bizSessionId,
      })
    },
    handleSetEditorContent(content) {
      if (!this.message) return
      this.message.previewContent = content
    },
  },
}
</script>

<style lang="less" module>
.apply-tmp__page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .apply-tmp__page__options {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .apply-tmp__page__content {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
    padding: 16px;

    .apply-tmp__page__content-wrapper {
      display: flex;
      height: 100%;
      align-items: stretch;
      flex: 1;
    }

    .apply-tmp__page__content__left {
      margin-right: 16px;
      border: 1px solid #dee1e8;
      border-radius: 8px;
      overflow: hidden;
    }

    .apply-tmp__page__content__right {
      flex: 1;
    }
  }
}
</style>
