<template>
  <div :class="$style.vibeMarketingSessionList">
    <div
      ref="sessionList"
      v-loading="isLoading"
      :class="$style.vibeMarketingSessionList__wrapper"
    >
      <div
        :class="$style.vibeMarketingSessionList__item"
      >
        <fx-card
          v-for="item in listData"
          :key="item.id"
          :class="$style.vibeMarketingSessionList__card"
          shadow="hover"
        >
          <div
            slot="header"
            :class="$style.vibeMarketingSessionList__card__header"
          >
            <span>AI智能沟通</span>
            <fx-dropdown
              size="small"
              @command="command => handleCommand(command, node, item)"
            >
              <span class="el-dropdown-link">
                管理<i class="fx-icon-arrow-down el-icon--right" />
              </span>
              <fx-dropdown-menu slot="dropdown">
                <fx-dropdown-item command="edit">
                  修改名称
                </fx-dropdown-item>
                <fx-dropdown-item
                  v-if="!item.isTop"
                  command="top"
                >
                  置顶会话
                </fx-dropdown-item>
                <fx-dropdown-item
                  v-if="item.isTop"
                  command="cancelTop"
                >
                  取消置顶
                </fx-dropdown-item>
                <fx-dropdown-item command="delete">
                  删除会话
                </fx-dropdown-item>
              </fx-dropdown-menu>
            </fx-dropdown>
          </div>
          <div :class="$style.vibeMarketingSessionList__item__content">
            <div :class="$style.vibeMarketingSessionList__item__content__icon">
              <img :src="iconChat">
            </div>
            <div :class="$style.vibeMarketingSessionList__item__content__title">
              {{ item.title }}
            </div>
          </div>
        </fx-card>
      </div>

      <Empty
        v-if="listData.length === 0"
        class="empty"
        :title="$t('marketing.commons.zwsj_21efd8')"
      />
    </div>

    <div :class="$style.vibeMarketingSessionList__pagination">
      <v-pagen
        :pagedata.sync="pageData"
        @change="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import http from '@/services/http/index.js'
import VPagen from '@/components/kitty/pagen.vue'
import Empty from '@/components/common/empty.vue'

export default {
  components: {
    VPagen,
    Empty,
  },
  data() {
    return {
      iconChat: `${$cdnPath}/images/ai-icon-chat.svg`,
      isLoading: true,
      listData: [],
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 40,
        pageSizes: [10, 20, 30, 40],
      },
    }
  },
  mounted() {
    this.fetchSessionList()
  },
  methods: {
    fetchSessionList() {
      this.isLoading = true
      const payload = {
        pageSize: this.pageData.pageSize,
        pageNum: this.pageData.pageNum,
        sceneId: null,
      }

      http.pageQuerySessions(payload).then(res => {
        const { errCode, data } = res || {}
        if (errCode === 0) {
          const {
            pageNum, pageSize, totalCount, result = [],
          } = data || {}
          this.listData = result
          this.pageData.pageNum = pageNum
          this.pageData.pageSize = pageSize
          this.pageData.totalCount = totalCount

          this.$nextTick(() => {
            this.$refs.sessionList.scrollTop = 0
          })
        }

        this.isLoading = false
      })
        .catch(() => {
          this.isLoading = false
        })
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.fetchSessionList()
    },
    handleCommand(command, node, chat) {
      if (command === 'edit') {
        this.handleEditChat(chat)
      }

      if (command === 'top') {
        this.handleTopChat(chat)
      }

      if (command === 'cancelTop') {
        this.handleCancelTopChat(chat)
      }

      if (command === 'delete') {
        this.handleDeleteChat(chat)
      }
    },
    handleEditChat(chat) {
      FxUI.MessageBox.prompt(
        $t('marketing_taro.pkgs.hhmc_5ddca3'),
        $t('marketing_taro.pkgs.xghhmc_1f3624'),
        {
          confirmButtonText: $t('marketing_taro.commons.bc_be5fbb'),
          cancelButtonText: $t('marketing_taro.commons.qx_625fb2'),
        },
      )
        .then(({ value }) => {
          if (!value.trim()) {
            FxUI.Message.error('请输入会话名称')
            return
          }

          const payload = {
            id: chat.id,
            title: value.trim(),
          }
          http.editSession(payload).then(res => {
            const { errCode } = res || {}
            if (errCode === 0) {
              FxUI.Message.success($t('marketing_taro.pkgs.xgcg_69be67'))
              this.fetchSessionList()
            }
          })
        })
    },
    handleTopChat(chat) {
      FxUI.MessageBox.confirm(
        $t('marketing_taro.pkgs.sfzdhh_aa4f03'),
        $t('marketing_taro.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing_taro.commons.zd_3d43ff'),
          cancelButtonText: $t('marketing_taro.commons.qx_625fb2'),
          type: 'warning',
        },
      )
        .then(() => {
          const payload = {
            id: chat.id,
            isTop: true,
          }
          http.setSessionTop(payload).then(res => {
            const { errCode } = res || {}
            if (errCode === 0) {
              FxUI.Message.success($t('marketing_taro.commons.czcg_33130f'))
              this.fetchSessionList()
            }
          })
        })
    },
    handleCancelTopChat(chat) {
      FxUI.MessageBox.confirm(
        $t('marketing_taro.pkgs.sfqxzd_7ed195'),
        $t('marketing_taro.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing_taro.pkgs.qxzd_84e4fa'),
          cancelButtonText: $t('marketing_taro.commons.qx_625fb2'),
          type: 'warning',
        },
      )
        .then(() => {
          const payload = {
            id: chat.id,
            isTop: false,
          }
          http.setSessionTop(payload).then(res => {
            const { errCode } = res || {}
            if (errCode === 0) {
              FxUI.Message.success($t('marketing_taro.commons.czcg_33130f'))
              this.fetchSessionList()
            }
          })
        })
    },
    handleDeleteChat(chat) {
      FxUI.MessageBox.confirm(
        $t('marketing_taro.pkgs.sfschh_573cc7'),
        $t('marketing_taro.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing_taro.pkgs.schh_78fb22'),
          cancelButtonText: $t('marketing_taro.commons.qx_625fb2'),
          type: 'warning',
        },
      )
        .then(() => {
          http.deleteSession({ id: chat.id }).then(res => {
            const { errCode } = res || {}
            if (errCode === 0) {
              FxUI.Message.success($t('marketing_taro.pkgs.sccg_0007d1'))
              this.listData = this.listData.filter(item => item.id !== chat.id)
            }
          })
        })
    },
    handleToSiteAgent() {
      this.$router.push({
        name: 'vibe-marketing-agent',
        query: {
          name: 'site',
        },
      })
    },
  },
}
</script>

<style lang="less" module>
.vibeMarketingSessionList {
  height: 100%;
  display: flex;
  flex-direction: column;

  .vibeMarketingSessionList__wrapper {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;

    .vibeMarketingSessionList__item {
      display: flex;
      flex-wrap: wrap;
      margin-left: 16px;
      margin-right: 16px;

      .vibeMarketingSessionList__card {
        width: calc((100% - 32px) / 3);
        box-sizing: border-box;
        margin-bottom: 16px;
        margin-right: 16px;

        &:nth-child(3n) {
          margin-right: 0;
        }

        .vibeMarketingSessionList__card__header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
        }

        .vibeMarketingSessionList__item__content {
          cursor: pointer;
        }
      }
    }
  }
}
</style>
