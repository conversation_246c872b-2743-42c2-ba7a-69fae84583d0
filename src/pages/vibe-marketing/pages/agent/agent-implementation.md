# Agent 组件架构设计与实现

## 1. 背景介绍

在营销系统中，我们经常需要为不同的业务场景提供智能助手功能。为了统一管理这些智能助手，我们设计了一套基于 BaseAgent 的组件架构，实现了代码复用和业务解耦。

## 2. 架构设计

### 2.1 核心思想
- 采用继承扩展模式
- 基于 Vue 组件系统
- 统一的交互模式
- 灵活的业务定制

### 2.2 组件架构图

```mermaid
%%{init: {"flowchart": {"defaultRenderer": "elk"}} }%%
flowchart LR
 subgraph s1 ["入口层"]
        B["动态加载器"]
        A["index.vue"]
  end
 subgraph s2 ["基础层"]
        D["Header组件"]
        C["BaseAgent"]
        E["Chat组件"]
        F["Content组件"]
        G["Mixins"]
  end
 subgraph s3 ["业务层"]
        H["SiteAgent"]
        I["ArticleAgent"]
        J["其他Agent"]
  end
 subgraph s4 ["上下文管理"]
        L["$content"]
        K["$context"]
        M["$user"]
        N["其他模块"]
  end
    A --> B
    C --> D & E & F & G
    H --> C
    I --> C
    J --> C
    K --> L & M & N
    B --> H & I & J
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style H fill:#bfb,stroke:#333,stroke-width:2px
    style I fill:#bfb,stroke:#333,stroke-width:2px
    style J fill:#bfb,stroke:#333,stroke-width:2px
    style K fill:#fbb,stroke:#333,stroke-width:2px
```

### 2.3 目录结构
```
agent/
├── conponents/
│   └── BaseAgent/         # 基础Agent组件
│       ├── index.vue     # 核心实现
│       ├── mixins/       # 通用逻辑
│       └── components/   # 基础组件
├── SiteAgent/            # 微页面Agent实现
├── ArticleAgent/         # 文章Agent实现
├── index.vue            # 动态加载入口
└── README.md            # 技术文档
```

## 3. 核心实现

### 3.1 BaseAgent 组件详解

#### 3.1.1 关键属性与方法
- `agentApiName`：Agent 唯一标识，子类必须指定
- `crumbs`：面包屑导航，通过 `getCrumbs` 方法获取
- `onAgentReady(agent)`：Agent 初始化回调
- `onAgentMessage(data)`：Agent 消息处理回调
- `onHeaderEvent(args)`：Header 事件处理
- `getSessionId()`：生成会话唯一 ID
- `getCrumbs()`：返回面包屑导航数组

#### 3.1.2 组件扩展方式
- 通过 `mixins` 复用通用逻辑
- 子组件可覆盖 `data` 和 `methods`，实现自定义

### 3.2 页面布局
每个 Agent 页面都遵循统一的三段式布局：
```
+------------------------+
|        Header         |
+--------+-------------+
|        |             |
| Chat   |  Content    |
|        |             |
+--------+-------------+
```

### 3.3 关键特性

#### 3.3.1 动态加载
```javascript
// index.vue
export default {
  methods: {
    getComponentLoader(name) {
      if (name === 'article') {
        return () => import('./ArticleAgent/index.vue')
      } else if (name === 'site') {
        return () => import('./SiteAgent/index.vue')
      }
    }
  }
}
```

#### 3.3.2 上下文管理
使用 `$context` 进行组件间通信：
```javascript
// 示例：数据共享
this.$context.$content.setPageData(pageData.page)
```

#### 3.3.3 错误处理
```javascript
try {
  const content = data.message.content.replace(/\n/g, '').replace(/\t/g, '');
  const pageData = JSON.parse(content);
  this.$context.$content.setPageData(pageData.page);
} catch (error) {
  // 错误处理
}
```

## 4. 最佳实践

### 4.1 创建新的 Agent

1. **目录结构**
```bash
NewAgent/
├── index.vue
└── components/
    ├── Content.vue
    └── Chat.vue
```

2. **基础实现**
```javascript
import BaseAgent from '../conponents/BaseAgent/index.vue'

export default {
  name: 'NewAgent',
  extends: BaseAgent,
  data() {
    return {
      agentApiName: 'YOUR_AGENT_API_NAME',
    }
  },
  methods: {
    getCrumbs() {
      return [
        { text: '一级导航', to: { name: 'route-name' } },
        { text: '二级导航', to: false },
      ]
    }
  }
}
```

### 4.2 开发建议

1. **命名规范**
   - Agent 名称使用 PascalCase
   - API 名称使用下划线命名
   - 组件名与目录名保持一致

2. **错误处理**
   - 使用 try-catch 包装异步操作
   - 提供友好的错误提示
   - 记录关键错误日志

3. **性能优化**
   - 使用动态导入
   - 合理使用缓存
   - 避免不必要的重渲染

4. **代码组织**
   - 业务逻辑与UI分离
   - 使用 mixins 复用通用逻辑
   - 保持组件的单一职责

## 5. 常见问题

### 5.1 如何添加新的 Agent？
1. 在 agent 目录下创建新的 Agent 目录
2. 继承 BaseAgent 组件
3. 实现必要的业务方法
4. 在 index.vue 中添加动态加载配置

### 5.2 如何处理跨组件通信？
- 使用 `$context` 进行数据共享
- 通过事件总线处理跨组件事件
- 合理使用 Vuex 管理全局状态

### 5.3 如何扩展基础功能？
- 通过 mixins 添加通用逻辑
- 覆盖 BaseAgent 的方法
- 扩展 `$context` 对象

## 6. 未来规划

1. **功能增强**
   - 支持更多类型的 Agent
   - 优化交互体验
   - 增强错误处理机制

2. **性能优化**
   - 实现组件懒加载
   - 优化数据流转
   - 提升渲染性能

3. **开发体验**
   - 提供更多开发工具
   - 完善文档和示例
   - 添加单元测试