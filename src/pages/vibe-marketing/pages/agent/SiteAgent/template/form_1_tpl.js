
/**
 * 高端研讨会报名表单、科技风格报名表单模版
 */

const tpl = {
    "id": "248e9353493246958138f780de09ee1e",
    "type": "page",
    "name": "",
    "title": "",
    "version": "4.4.0-32",
    "cover": "",
    "shareOpts": {
        "title": "",
        "desc": "",
        "link": "",
        "imgUrl": ""
    },
    "style": {
        "width": 375,
        "backgroundColor": "#fff",
        "backgroundSize": "100%",
        "backgroundRepeat": "no-repeat",
        "backgroundImage": ""
    },
    "backgroundFillType": "filling",
    "dataSourceAction": {

    },
    "components": [
        {

            "id": 1638784345222,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1638784412448,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 23px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">脑重大疾病精准医学蛋白质组</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 158,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784412448,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 44px; letter-spacing: 0px;\">高端研讨会</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 109,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449059",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(33, 79, 201); line-height: 24px; font-size: 14px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">会议时间：2021年7月18日</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 229,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_4d003939a6e94a42b669bc10616c6775.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 99,
                        "height": 20.064,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 29,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "id": 1638784517507,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "image",
            "fillMethod": "stretch",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 532,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": "url(https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_490404661c1d4fb788e236d63dd7aabc.png&ea=fs)",
                "backgroundSize": "100% 100%",
                "backgroundRepeat": "no-repeat"
            },
            "sort": 0,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638861556826,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_b5bc2b6c37134e92b4c8b74309a8e97c.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 0,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784610878,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 318,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 47,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_0af7ba2ba51c4f879bd3e30e7beff904.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": -5,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "id": "1638777449060",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784785907,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">会议邀请脑重大疾病精准医学领域著名专家学者和企业研发领袖，围绕脑损伤、脑肿瘤、脑血管病、神经退行性疾病相关重大疾病的发病机制、精准诊断和个体化治疗等做专题演讲和讨论，旨在提速神经科学、脑重大疾病研究与蛋白质组学的交叉融合，增进蛋白组学在基础研究、临床应用方面的交流与合作，共同推动我国脑重大疾病精准医学的长远发展。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 47,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 342,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_99b4144d7a6443bfa9fa2cb2c494caf3.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 311,
                        "height": 129,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 10,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 233,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "id": 1638784853182,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449063",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); font-size: 16px; line-height: 24px; letter-spacing: 0px;\">会议主题</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 4,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 397,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(64, 99, 229, 1)",
                "backgroundImage": ""
            },
            "sort": 1,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638844635548,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 340,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 47,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 5,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "id": "1638777449060",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784785907,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 124,
                        "top": 64,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 368,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 83,
                        "height": 99,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 65,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "id": 1638784853182,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449063",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); font-size: 16px; line-height: 24px; letter-spacing: 0px;\">会议嘉宾</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 14,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449064",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">北京大学附属医院副教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 188,
                        "top": 67,
                        "width": 156,
                        "position": "absolute"
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449065",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">妇产科副主任医师,2002年毕业以来，一直从事妇产科的临床工作。擅长治疗产科合并症，并发症。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 129,
                        "top": 94,
                        "width": 220,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449066",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 124,
                        "top": 177,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 83,
                        "height": 99,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 180,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 9,
                    "id": "1638777449067",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449068",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">北京大学附属医院副教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 188,
                        "top": 179,
                        "width": 156,
                        "position": "absolute"
                    },
                    "sort": 10,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449069",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">妇产科副主任医师,2002年毕业以来，一直从事妇产科的临床工作。擅长治疗产科合并症，并发症。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 128,
                        "top": 209,
                        "width": 220,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 11,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449070",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 124,
                        "top": 292,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 12,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 83,
                        "height": 99,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 295,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 13,
                    "id": "1638777449071",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449072",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">北京大学附属医院副教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 188,
                        "top": 294,
                        "width": 156,
                        "position": "absolute"
                    },
                    "sort": 14,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449073",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">妇产科副主任医师,2002年毕业以来，一直从事妇产科的临床工作。擅长治疗产科合并症，并发症。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 124,
                        "top": 319,
                        "width": 220,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 15,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 426,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(64, 99, 229, 1)",
                "backgroundImage": ""
            },
            "sort": 2,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638844633252,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 324,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 66,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 365,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_0af7ba2ba51c4f879bd3e30e7beff904.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 12,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "id": 1638844744198,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "文本",
                    "type": "text",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 66,
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 155,
                        "top": 21,
                        "position": "absolute",
                        "fontSize": 14
                    },
                    "sort": 3,
                    "id": "1638777449074",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    },
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 16px; font-family: Helvetica, Arial, sans-serif;\">会议流程</strong></p>"
                },
                {
                    "id": 1638861613963,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 69,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449972",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">会议致辞</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 136,
                        "top": 66,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 114,
                        "top": 82,
                        "position": "absolute"
                    },
                    "sort": 6,
                    "id": 1638861645099,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449973",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 107,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449974",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">基于微流控液滴技术的单细胞分析</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 136,
                        "top": 105,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 114,
                        "top": 120,
                        "position": "absolute"
                    },
                    "sort": 9,
                    "id": "1638777449975",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449976",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 136,
                        "top": 131,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 10,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449977",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 165,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 11,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449978",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">基于微流控液滴技术的单细胞分析</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 136,
                        "top": 163,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 12,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 114,
                        "top": 178,
                        "position": "absolute"
                    },
                    "sort": 13,
                    "id": "1638777449979",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449980",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 136,
                        "top": 189,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 14,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449986",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 23,
                        "top": 226,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 15,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449987",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">基于微流控液滴技术的单细胞分析</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 134,
                        "top": 224,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 16,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449988",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 134,
                        "top": 250,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 17,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 114,
                        "top": 239,
                        "position": "absolute"
                    },
                    "sort": 18,
                    "id": "1638777449989",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449990",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 23,
                        "top": 289,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 19,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449991",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">基于微流控液滴技术的单细胞分析</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 134,
                        "top": 288,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 20,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449992",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 134,
                        "top": 313,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 114,
                        "top": 303,
                        "position": "absolute"
                    },
                    "sort": 22,
                    "id": "1638777449993",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449994",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 352,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 23,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449995",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">抽奖、自由交流环节</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 134,
                        "top": 350,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 24,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 114,
                        "top": 365,
                        "position": "absolute"
                    },
                    "sort": 25,
                    "id": "1638777449996",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 424,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(64, 99, 229, 1)",
                "backgroundImage": ""
            },
            "sort": 3,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638861562161,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 277,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 66,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 315,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_0af7ba2ba51c4f879bd3e30e7beff904.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 12,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "id": 1638844744198,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "文本",
                    "type": "text",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 66,
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 155,
                        "top": 21,
                        "position": "absolute",
                        "fontSize": 14
                    },
                    "sort": 4,
                    "id": "1638777449074",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    },
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 16px; font-family: Helvetica, Arial, sans-serif;\">会议概要</strong></p>"
                },
                {
                    "id": 1638847060239,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">会议时间：2021年7月18日 14:00-17:30</span></p><p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">咨询电话：0755-23456543</span></p><p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">会议地址：深圳大冲国际中心22楼</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 66,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_fddde12686f04169ae742a9bc3587c05.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 317,
                        "height": 172,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 165,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "id": 1638847082311,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 377,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(64, 99, 229, 1)",
                "backgroundImage": ""
            },
            "sort": 4,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638847043047,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_e37e459b39d24d02b89036d2a2c914d8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 76,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": -6,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638847343078,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638848944511,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 16px; font-family: Helvetica, Arial, sans-serif;\">提交以下信息马上报名</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 14,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 61,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(64, 99, 229, 1)",
                "backgroundImage": ""
            },
            "sort": 5,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638847361071,
            "name": "表单",
            "key": "form-container",
            "type": "container",
            "typeValue": "form",
            "components": [
                {
                    "id": "1638777449356",
                    "label": "提交",
                    "name": "提交",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": true,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 45,
                        "width": 345,
                        "fontSize": 16,
                        "background": "rgba(64, 99, 229, 1)",
                        "borderRadius": 6,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "position": "absolute",
                        "left": 15,
                        "top": 356,
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638847410569,
                    "label": "姓名",
                    "name": "姓名",
                    "title": "姓名",
                    "type": "input",
                    "typeValue": "text",
                    "fieldName": "name",
                    "defaultValueOpen": false,
                    "defaultValue": "",
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "placeholder": "请输入姓名",
                    "isFormComp": true,
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 18,
                        "position": "absolute"
                    },
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 1
                },
                {
                    "id": 1638847422081,
                    "label": "手机号",
                    "name": "手机号",
                    "title": "手机号",
                    "type": "input",
                    "typeValue": "number",
                    "fieldName": "phone",
                    "pattern": "^1[0-9]\\d{9}$",
                    "defaultValue": "",
                    "defaultValueOpen": false,
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "verify": true,
                    "weChatAuthorizationButton": false,
                    "placeholder": "请输入手机号",
                    "isFormComp": true,
                    "weChatAuthorizationButtonStyle": {
                        "color": "#fff",
                        "background": "#09BB07",
                        "fontSize": 14,
                        "borderStyle": "solid",
                        "borderWidth": 0,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5"
                    },
                    "verifyButtonStyle": {
                        "color": "rgba(64, 99, 229, 1)",
                        "background": "#ffffff",
                        "fontSize": 14,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "rgba(64, 99, 229, 1)"
                    },
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 199,
                        "position": "absolute"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 2
                },
                {
                    "id": 1638847424113,
                    "label": "邮箱",
                    "name": "邮箱",
                    "title": "邮箱",
                    "type": "input",
                    "typeValue": "text",
                    "fieldName": "email",
                    "pattern": "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$",
                    "defaultValue": "",
                    "defaultValueOpen": false,
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "placeholder": "请输入邮箱",
                    "isFormComp": true,
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 111,
                        "position": "absolute"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 3
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 412,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 6
        }
    ],
    "language": "zh-CN"
}
export default tpl;