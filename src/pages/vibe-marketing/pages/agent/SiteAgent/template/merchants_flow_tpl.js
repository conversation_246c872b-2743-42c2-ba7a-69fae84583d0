
/**
 * 品牌加盟流程模版
 */

const tpl = {
    "id": "d29c760e4dd5431f8e6b124cce0aa49a",
    "type": "page",
    "name": "品牌加盟",
    "title": "",
    "version": "4.4.4",
    "cover": "",
    "shareOpts": {
        "title": "",
        "desc": "",
        "link": "",
        "imgUrl": ""
    },
    "style": {
        "width": 375,
        "backgroundColor": null,
        "backgroundSize": "cover",
        "backgroundRepeat": "no-repeat",
        "backgroundImage": "",
        "backgroundPosition": "center center"
    },
    "backgroundFillType": "filling",
    "dataSourceAction": {

    },
    "components": [
        {
            "id": "1645164898717",
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_175d6189d66349e6a369175aba39c5f5.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 159,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 1,
                        "top": 0,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": "1645164898718",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898719",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 28px; font-family: SimHei, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">中国茶叶加盟流程优势</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 10,
                        "top": 60,
                        "width": 354,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 159,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 0,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645428033248,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_f2a5613e1db4467da7256a252f4bb3a3.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 100,
                        "height": 75,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 28,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "id": "1645424840679",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840682",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">1.初步洽谈</strong></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 137,
                        "top": 28,
                        "width": 85,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840683",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">了解总部要求、标准和条件后</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">获取招商资料，考察公司、门店</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 137,
                        "top": 59,
                        "width": 221,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_87c3a36ff5fa4aeca7154aeb47256b92.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 100,
                        "height": 75,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 135,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "id": "1645424840688",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840689",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">2.提交资料</strong></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 140,
                        "top": 135,
                        "width": 85,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840690",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">提交个人或公司相关证件与资料</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">预估店面面积、位置等信息</span></p><p><br></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 140,
                        "top": 166,
                        "width": 221,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_43ea332cf9be4bb19b9b6c6ef8de837a.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 98,
                        "height": 73,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 14,
                        "top": 249,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 6,
                    "id": "1645424840691",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840692",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">3.总部审核</strong></p><p><br></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 140,
                        "top": 236,
                        "width": 85,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840693",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">总部审核提交过来的商圈及市场信息</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">针对店铺不合格或没有店面的意向客户</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">招商经理会主动选址</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 140,
                        "top": 267,
                        "width": 223,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_536f3c833d2041819e7e05d99938a85f.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 100,
                        "height": 75,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 13,
                        "top": 368,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 9,
                    "id": "1645424840694",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840695",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">4.签订合约</strong></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 140,
                        "top": 368,
                        "width": 85,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 10,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840696",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">经过层层审核，取得经营执照后与我们签订合约</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 140,
                        "top": 398,
                        "width": 213,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 11,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_24941269ad234bf98f2c51467524f526.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 100,
                        "height": 75,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 478,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 12,
                    "id": "1645424840697",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840698",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">5.装修及验收</strong></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 138,
                        "top": 472,
                        "width": 100,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 13,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840699",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">按照统一标准装修店面，并经验收合格后方可开业</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 137,
                        "top": 502,
                        "width": 215,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 14,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_5533b02297f140478af5a2ccc76698d7.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 100,
                        "height": 75,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 16,
                        "top": 589,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 15,
                    "id": "1645424840700",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840701",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">总部审核提交过来的商圈及市场信息</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">针对店铺不合格或没有店面的意向客户</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">招商经理会主动选址</span></p><p><br></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 152,
                        "top": 1128,
                        "width": 221,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 16,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840702",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">6.筹备及开业</strong></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 138,
                        "top": 582,
                        "width": 95,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 17,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840703",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">总部人员协助招聘并开办开业前培训，营运经理帮助门店配货、陈列宣传等</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 3,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 138,
                        "top": 613,
                        "width": 221,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 18,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 719,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(242, 238, 229, 100)",
                "backgroundImage": ""
            },
            "sort": 1,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        }
    ],
    "language": "zh-CN",
    "placeholderStyle": {
        "color": "#cbcccf"
    },
    "inputStyle": {
        "color": "#181c25"
    }
}
export default tpl;