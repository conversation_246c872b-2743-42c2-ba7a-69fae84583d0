
/**
 * 互联网科技大会报名表单模版
 */

const tpl = {
    "id": "dc8477c4ab77447d9734b3dd1fda7988",
    "type": "page",
    "name": "",
    "title": "",
    "version": "4.4.0-32",
    "cover": "",
    "shareOpts": {
        "title": "",
        "desc": "",
        "link": "",
        "imgUrl": ""
    },
    "style": {
        "width": 375,
        "backgroundColor": "#fff",
        "backgroundSize": "100%",
        "backgroundRepeat": "no-repeat",
        "backgroundImage": ""
    },
    "backgroundFillType": "filling",
    "dataSourceAction": {

    },
    "components": [
        {
            
            "id": 1638784345222,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1638784412448,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 31px; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">年互联网科技发展大会</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 33,
                        "top": 175,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784412448,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 44px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">2021</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 33,
                        "top": 124,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_4d003939a6e94a42b669bc10616c6775.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 99,
                        "height": 20.064,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 41,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "id": 1638784517507,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_500b7860d5e44ff7892eea1bd899b091.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 245,
                        "height": 28.093333333333334,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 33,
                        "top": 241,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "id": 1638957026142,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449059",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(255, 255, 255); line-height: 24px; font-size: 12px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">会议时间：2021年7月18日-2021年7月22日</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 46,
                        "top": 237,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "image",
            "fillMethod": "stretch",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 350,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": "url(https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_661ace7b2e7b4bd69ebb6155c4878143.png&ea=fs)",
                "backgroundSize": "100% 100%",
                "backgroundRepeat": "no-repeat"
            },
            "sort": 0,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638785013124,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 291,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 40,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_0af7ba2ba51c4f879bd3e30e7beff904.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": -9,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "id": "1638777449060",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784785907,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(68, 68, 68); font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">大会以“智能升级、产业赋能、增长与变革”为主题，围绕工业数字化、自动化与智能化升级，智能工厂与智慧供应链，数字化管理与精细化运营等三大方向展开研讨，并深入不同应用场景举办数十个平行分论坛。同时组委会还将邀请多位行业权威专家及各领域顶尖的智造解决方案代表厂商参会，在线直播分享行业前沿科技。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 47,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 316,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_99b4144d7a6443bfa9fa2cb2c494caf3.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 311,
                        "height": 129,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 10,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 32,
                        "top": 208,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "id": 1638784853182,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449063",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); font-size: 16px; line-height: 24px; letter-spacing: 0px;\">会议主题</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 0,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 373,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(13, 25, 77, 1)",
                "backgroundImage": ""
            },
            "sort": 1,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638844635548,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 353,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 47,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 5,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "id": "1638777449060",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784785907,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 43,
                        "top": 153,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 386,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 0,
                    "style": {
                        "display": "flex",
                        "width": 80,
                        "height": 80,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 375,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 75,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "id": 1638784853182,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449063",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); font-size: 16px; line-height: 24px; letter-spacing: 0px;\">会议嘉宾</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 14,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449064",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 12px; color: rgb(51, 51, 51); letter-spacing: 0px;\">高新发展科技有限公司 CEO</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 22,
                        "top": 178,
                        "width": 99,
                        "position": "absolute"
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449946",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 160,
                        "top": 153,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 80,
                        "height": 80,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 375,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 147,
                        "top": 75,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 8,
                    "id": "1638777449947",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449949",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 276,
                        "top": 153,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 9,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 80,
                        "height": 80,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 375,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 264,
                        "top": 75,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 11,
                    "id": "1638777449950",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449961",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 12px; color: rgb(51, 51, 51); letter-spacing: 0px;\">成都发展投资有限公司 董事长</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 139,
                        "top": 178,
                        "width": 99,
                        "position": "absolute"
                    },
                    "sort": 11,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449962",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\">清华大学 </p><p style=\"text-align: center;\">博士</p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 254,
                        "top": 178,
                        "width": 99,
                        "position": "absolute"
                    },
                    "sort": 12,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449963",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 42,
                        "top": 335,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 13,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 80,
                        "height": 80,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 375,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 257,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 14,
                    "id": "1638777449964",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449965",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 12px; color: rgb(51, 51, 51); letter-spacing: 0px;\">高新发展科技有限公司 CEO</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 21,
                        "top": 360,
                        "width": 99,
                        "position": "absolute"
                    },
                    "sort": 15,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449966",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 158,
                        "top": 335,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 16,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 80,
                        "height": 80,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 375,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 146,
                        "top": 257,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 17,
                    "id": "1638777449967",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449968",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 12px; color: rgb(51, 51, 51); letter-spacing: 0px;\">成都发展投资有限公司 董事长</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 137,
                        "top": 360,
                        "width": 99,
                        "position": "absolute"
                    },
                    "sort": 18,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449969",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(64, 99, 229); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 274,
                        "top": 335,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 19,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 80,
                        "height": 80,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 375,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 262,
                        "top": 257,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 20,
                    "id": "1638777449970",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449971",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\">北京大学 </p><p style=\"text-align: center;\">博士后</p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 252,
                        "top": 360,
                        "width": 99,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 446,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(13, 25, 77, 1)",
                "backgroundImage": ""
            },
            "sort": 2,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638861891464,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 372,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 66,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 409,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_0af7ba2ba51c4f879bd3e30e7beff904.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 12,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "id": 1638844744198,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "文本",
                    "type": "text",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 66,
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 155,
                        "top": 21,
                        "position": "absolute",
                        "fontSize": 14
                    },
                    "sort": 3,
                    "id": "1638777449074",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    },
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 16px; font-family: Helvetica, Arial, sans-serif;\">会议流程</strong></p>"
                },
                {
                    "id": 1638863047716,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 71,
                        "width": 76,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449997",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">嘉宾签到、产品展示、播放企业宣传片</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 130,
                        "top": 71,
                        "width": 221,
                        "position": "absolute"
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 111,
                        "top": 84,
                        "position": "absolute"
                    },
                    "sort": 6,
                    "id": 1638863124206,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449998",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 130,
                        "width": 76,
                        "position": "absolute"
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449999",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">特邀嘉宾致辞</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 130,
                        "top": 130,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 111,
                        "top": 143,
                        "position": "absolute"
                    },
                    "sort": 9,
                    "id": "1638777450000",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450001",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">成都高新科技公司董事长 张茹</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 130,
                        "top": 157,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 10,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450002",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 195,
                        "width": 76,
                        "position": "absolute"
                    },
                    "sort": 11,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450003",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">技术分享 畅想下一代人工智能科技</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 130,
                        "top": 195,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 12,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 111,
                        "top": 208,
                        "position": "absolute"
                    },
                    "sort": 13,
                    "id": "1638777450004",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450005",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">成都高新科技公司董事长 张茹</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 130,
                        "top": 222,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 14,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450006",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 24,
                        "top": 262,
                        "width": 76,
                        "position": "absolute"
                    },
                    "sort": 15,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450007",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">技术分享 畅想下一代人工智能科技</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 129,
                        "top": 262,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 16,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 111,
                        "top": 275,
                        "position": "absolute"
                    },
                    "sort": 17,
                    "id": "1638777450008",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450009",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">成都高新科技公司董事长 张茹</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 129,
                        "top": 289,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 18,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450010",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 328,
                        "width": 76,
                        "position": "absolute"
                    },
                    "sort": 19,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450011",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">技术分享 畅想下一代人工智能科技</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 130,
                        "top": 328,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 20,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 112,
                        "top": 341,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "id": "1638777450012",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450013",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">成都高新科技公司董事长 张茹</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 130,
                        "top": 355,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 22,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450014",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 24,
                        "top": 396,
                        "width": 76,
                        "position": "absolute"
                    },
                    "sort": 23,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777450015",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(64, 99, 229); line-height: 24px; letter-spacing: 0px;\">会后答疑、抽奖环节</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 129,
                        "top": 396,
                        "width": 220,
                        "position": "absolute"
                    },
                    "sort": 24,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_b5036f17cc0743aca89b979a705e05cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 7,
                        "height": 7.000000000000001,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 111,
                        "top": 409,
                        "position": "absolute"
                    },
                    "sort": 25,
                    "id": "1638777450016",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 467,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(13, 25, 77, 1)",
                "backgroundImage": ""
            },
            "sort": 3,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638844633252,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 277,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 66,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 315,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_0af7ba2ba51c4f879bd3e30e7beff904.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 12,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "id": 1638844744198,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "文本",
                    "type": "text",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 66,
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 155,
                        "top": 21,
                        "position": "absolute",
                        "fontSize": 14
                    },
                    "sort": 4,
                    "id": "1638777449074",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    },
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 16px; font-family: Helvetica, Arial, sans-serif;\">会议概要</strong></p>"
                },
                {
                    "id": 1638847060239,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">会议时间：2021年7月18日 14:00-17:30</span></p><p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">咨询电话：0755-23456543</span></p><p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">会议地址：深圳大冲国际中心22楼</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 66,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_fddde12686f04169ae742a9bc3587c05.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 317,
                        "height": 172,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 165,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "id": 1638847082311,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 377,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(13, 25, 77, 1)",
                "backgroundImage": ""
            },
            "sort": 4,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638847043047,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_e37e459b39d24d02b89036d2a2c914d8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 76,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": -6,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638847343078,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638848944511,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); line-height: 24px; letter-spacing: 0px; font-size: 16px; font-family: Helvetica, Arial, sans-serif;\">提交以下信息马上报名</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 14,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 61,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(13, 25, 77, 1)",
                "backgroundImage": ""
            },
            "sort": 5,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638847361071,
            "name": "表单",
            "key": "form-container",
            "type": "container",
            "typeValue": "form",
            "components": [
                {
                    "id": "1638777449356",
                    "label": "提交",
                    "name": "提交",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": true,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 45,
                        "width": 345,
                        "fontSize": 16,
                        "background": "rgba(64, 99, 229, 1)",
                        "borderRadius": 6,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "position": "absolute",
                        "left": 15,
                        "top": 356,
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638847410569,
                    "label": "姓名",
                    "name": "姓名",
                    "title": "姓名",
                    "type": "input",
                    "typeValue": "text",
                    "fieldName": "name",
                    "defaultValueOpen": false,
                    "defaultValue": "",
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "placeholder": "请输入姓名",
                    "isFormComp": true,
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 18,
                        "position": "absolute"
                    },
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 1
                },
                {
                    "id": 1638847422081,
                    "label": "手机号",
                    "name": "手机号",
                    "title": "手机号",
                    "type": "input",
                    "typeValue": "number",
                    "fieldName": "phone",
                    "pattern": "^1[0-9]\\d{9}$",
                    "defaultValue": "",
                    "defaultValueOpen": false,
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "verify": true,
                    "weChatAuthorizationButton": false,
                    "placeholder": "请输入手机号",
                    "isFormComp": true,
                    "weChatAuthorizationButtonStyle": {
                        "color": "#fff",
                        "background": "#09BB07",
                        "fontSize": 14,
                        "borderStyle": "solid",
                        "borderWidth": 0,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5"
                    },
                    "verifyButtonStyle": {
                        "color": "rgba(64, 99, 229, 1)",
                        "background": "#ffffff",
                        "fontSize": 14,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "rgba(64, 99, 229, 1)"
                    },
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 199,
                        "position": "absolute"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 2
                },
                {
                    "id": 1638847424113,
                    "label": "邮箱",
                    "name": "邮箱",
                    "title": "邮箱",
                    "type": "input",
                    "typeValue": "text",
                    "fieldName": "email",
                    "pattern": "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$",
                    "defaultValue": "",
                    "defaultValueOpen": false,
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "placeholder": "请输入邮箱",
                    "isFormComp": true,
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 111,
                        "position": "absolute"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 3
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 412,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 6
        }
    ],
    "language": "zh-CN"
}
export default tpl;