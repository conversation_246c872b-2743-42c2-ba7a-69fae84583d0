
/**
 * 加入品牌加盟模版
 */

const tpl = {
    "id": "423051f18b424b37873b9880ef568d60",
    "type": "page",
    "name": "品牌加盟",
    "title": "",
    "version": "4.4.3",
    "cover": "",
    "shareOpts": {
        "title": "",
        "desc": "",
        "link": "",
        "imgUrl": ""
    },
    "style": {
        "width": 375,
        "backgroundColor": "#fff",
        "backgroundSize": "cover",
        "backgroundRepeat": "no-repeat",
        "backgroundImage": "",
        "backgroundPosition": "center center"
    },
    "backgroundFillType": "filling",
    "dataSourceAction": {

    },
    "components": [
        {
            "id": "1645164898717",
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_175d6189d66349e6a369175aba39c5f5.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 188,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 0,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": "1645164898718",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898719",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; font-family: SimHei, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">中国茶叶连锁品牌</strong></p><p><strong style=\"font-size: 14px; font-family: SimHei, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">全国门店超2000家</strong></p><p><strong style=\"font-size: 14px; font-family: SimHei, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">加盟中国茶叶</strong></p><p><strong style=\"font-size: 14px; font-family: SimHei, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">开店全程扶持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 40,
                        "width": 345,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 187,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 0
        },
        {
            "id": 1643014340276,
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(255, 255, 255, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 1,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645166203348,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1643009161475,
                    "label": "按钮",
                    "name": "",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": false,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 30,
                        "width": 83,
                        "fontSize": 16,
                        "background": "rgba(170, 117, 41, 1)",
                        "borderRadius": 0,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 8,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1643009153172",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">公司简介</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 5,
                        "width": 69,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_cb20e0e3404d4de1ba7f2df3d705d392.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 0,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 128,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 38,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "id": "1645164898713",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898714",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: justify;\">源自百年制茶世家，中国联合专卖领先品牌，全国门店超2000家。作为国家级非遗铁观音技艺代表性传承人创立品牌，已有300多年历史的铁观音技艺凝练成霸道制茶工艺，并以24定律研制好茶。连续10年全国销量领先。</p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 170,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 295,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 2,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1643014350902,
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(245, 245, 245, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 3,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645165128287,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1643009161475,
                    "label": "按钮",
                    "name": "",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": false,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 30,
                        "width": 83,
                        "fontSize": 16,
                        "background": "rgba(170, 117, 41, 1)",
                        "borderRadius": 0,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 8,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1643009153172",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">加盟案例</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 5,
                        "width": 69,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_b6e4095abef34e7aa74a117afa3e9abd.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 0,
                    "style": {
                        "display": "flex",
                        "width": 355,
                        "height": 157,
                        "paddingBottom": 5,
                        "paddingLeft": 5,
                        "paddingRight": 5,
                        "paddingTop": 5,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 48,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 2,
                    "id": "1645164898713",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898714",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>甘肃天水团队加盟案例</p><p style=\"text-align: justify;\"><br></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 209,
                        "width": 345,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898720",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: justify;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">店面标准化就是简单复制，且可持续优化和创新</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 235,
                        "width": 345,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_b6e4095abef34e7aa74a117afa3e9abd.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 0,
                    "style": {
                        "display": "flex",
                        "width": 356,
                        "height": 157,
                        "paddingBottom": 5,
                        "paddingLeft": 5,
                        "paddingRight": 5,
                        "paddingTop": 5,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 10,
                        "top": 262,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "id": "1645164898721",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898722",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>甘肃天水团队加盟案例</p><p style=\"text-align: justify;\"><br></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 429,
                        "width": 345,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898723",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: justify;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">店面标准化就是简单复制，且可持续优化和创新</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 459,
                        "width": 345,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_b110a524f4814020939d5d709d8dde7d.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 81,
                        "height": 82.728,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": -396,
                        "top": -57,
                        "position": "absolute"
                    },
                    "sort": 8,
                    "id": "1645164898751",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_b110a524f4814020939d5d709d8dde7d.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 52,
                        "height": 53.10933333333334,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 160,
                        "top": 100,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 9,
                    "id": "1645164898752",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_b110a524f4814020939d5d709d8dde7d.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 52,
                        "height": 53.10933333333334,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 162,
                        "top": 314,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 10,
                    "id": "1645164898753",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 495,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 4,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": "1643009153196",
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(245, 245, 245, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 5,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645166498757,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1643009161475,
                    "label": "按钮",
                    "name": "",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": false,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 30,
                        "width": 83,
                        "fontSize": 16,
                        "background": "rgba(170, 117, 41, 1)",
                        "borderRadius": 0,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 8,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1643009153172",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">联系我们</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 5,
                        "width": 69,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_65c847556d764680b9b0105ad67178a4.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 24,
                        "height": 24,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 46,
                        "top": 67,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 2,
                    "id": "1645164898754",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_eba6f855b6d3459d98bbf9dff2c4ef0b.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 24,
                        "height": 22,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 213,
                        "top": 68,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "id": "1645164898755",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898756",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>在线咨询</p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 82,
                        "top": 54,
                        "width": 77,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898757",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">客服答疑</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 82,
                        "top": 74,
                        "width": 77,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898758",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">0755-8767876</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 248,
                        "top": 74,
                        "width": 90,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898759",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>加盟热线</p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 248,
                        "top": 54,
                        "width": 77,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 123,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 6,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "label": "侧边悬浮",
            "name": "按钮",
            "type": "suspension",
            "required": false,
            "isFormComp": false,
            "backgroundFillType": "filling",
            "showType": "text",
            "wrapStyle": {
                "position": "fixed",
                "zIndex": 10,
                "left": 310,
                "top": 375,
                "width": 50,
                "height": 50
            },
            "style": {
                "width": 50,
                "height": 50,
                "borderRadius": 50,
                "borderStyle": "solid",
                "borderWidth": 1,
                "borderColor": "#e9edf5",
                "color": "#333",
                "fontSize": 14,
                "backgroundColor": "#fff",
                "boxShadow": "2px 2px 12px rgba(0,0,0,.1)",
                "boxShadowLeft": 2,
                "boxShadowTop": 2,
                "boxShadowRadius": 12,
                "boxShadowColor": "rgba(0,0,0,.1)"
            },
            "sort": 7,
            "id": "1645164898742",
            "components": [

            ]
        }
    ],
    "language": "zh-CN",
    "placeholderStyle": {
        "color": "#cbcccf"
    },
    "inputStyle": {
        "color": "#181c25"
    }
}
export default tpl;