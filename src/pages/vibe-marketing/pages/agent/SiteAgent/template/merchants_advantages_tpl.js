
/**
 * 加盟优势模版
 */

const tpl = {
    "id": "3f7b405c318d421b8a1f233845b19d5a",
    "type": "page",
    "name": "品牌加盟",
    "title": "",
    "version": "4.4.4",
    "cover": "",
    "shareOpts": {
        "title": "",
        "desc": "",
        "link": "",
        "imgUrl": ""
    },
    "style": {
        "width": 375,
        "backgroundColor": "#fff",
        "backgroundSize": "cover",
        "backgroundRepeat": "no-repeat",
        "backgroundImage": "",
        "backgroundPosition": "center center"
    },
    "backgroundFillType": "filling",
    "dataSourceAction": {

    },
    "components": [
        {
            "id": "1645164898717",
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_713d0eb3c7a04dae859b6ade63601702.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 168,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 1,
                        "top": 0,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": "1645164898718",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898719",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 28px; font-family: SimHei, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">中国茶叶加盟优势</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 11,
                        "top": 65,
                        "width": 354,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 168,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 0,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1643014340276,
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(255, 255, 255, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 1,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645166203348,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1643009161475,
                    "label": "按钮",
                    "name": "",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": false,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 30,
                        "width": 83,
                        "fontSize": 16,
                        "background": "rgba(170, 117, 41, 1)",
                        "borderRadius": 0,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 8,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1643009153172",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">品牌优势</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 5,
                        "width": 69,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_18_642b16b8aea247faaab0004c8f4d1820.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 0,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 191,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 38,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 2,
                    "id": "1645164898713",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898714",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\">国家级非遗铁观音技艺代表性创立品牌</p><p style=\"text-align: center;\">中国茶艺连锁店领先品牌</p><p style=\"text-align: center;\">截止2020年12月31日，全国门店超2000家</p><p style=\"text-align: justify;\"><br></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 235,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 315,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 2,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1643014350902,
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(245, 245, 245, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 3,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645165128287,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1643009161475,
                    "label": "按钮",
                    "name": "",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": false,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 30,
                        "width": 83,
                        "fontSize": 16,
                        "background": "rgba(170, 117, 41, 1)",
                        "borderRadius": 0,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 8,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1643009153172",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">茶品优势</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 5,
                        "width": 69,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898760",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">我们拥有可管控生态茶园、国家农业部制定乌龙茶示范基地</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">全球首条乌龙茶全程自动化、清洁化、不落地生产线</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">覆盖六大茶类十大名茶，安全、对口、正宗、稳定为标准</span></p><p><br></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 43,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 138,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 4,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645427101174,
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(245, 245, 245, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 5,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1645168826083,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "id": 1643009161475,
                    "label": "按钮",
                    "name": "",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": false,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 30,
                        "width": 83,
                        "fontSize": 16,
                        "background": "rgba(170, 117, 41, 1)",
                        "borderRadius": 0,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 8,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1643009153172",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(255, 255, 255); letter-spacing: 0px;\">加盟支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 5,
                        "width": 69,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898761",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">招聘&amp;培训支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 49,
                        "width": 345,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645164898762",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">免费协助门店招聘，提供免费驻场培训、定期线下培训和免费内部线上体系化培训</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 84,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840661",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(84, 88, 97); letter-spacing: 0px;\">专业茶艺培训</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 29,
                        "top": 230,
                        "width": 84,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840662",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(84, 88, 97); letter-spacing: 0px;\">门店经营培训</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 146,
                        "top": 230,
                        "width": 84,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840663",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(84, 88, 97); letter-spacing: 0px;\">销售拓展培训</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 268,
                        "top": 230,
                        "width": 84,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840664",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">标准化运营支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 281,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840665",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">国内首创全套标准化茶店运营作业流程，满足老板、大客户、店长、茶艺师的需求</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 16,
                        "top": 309,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840670",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">选址支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 446,
                        "width": 64,
                        "position": "absolute"
                    },
                    "sort": 13,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840671",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">装修设计</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 115,
                        "top": 446,
                        "width": 64,
                        "position": "absolute"
                    },
                    "sort": 14,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840672",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">培训支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 196,
                        "top": 446,
                        "width": 64,
                        "position": "absolute"
                    },
                    "sort": 16,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840673",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">招聘支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 278,
                        "top": 446,
                        "width": 64,
                        "position": "absolute"
                    },
                    "sort": 17,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840674",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">营运支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 30,
                        "top": 534,
                        "width": 64,
                        "position": "absolute"
                    },
                    "sort": 18,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840675",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">调货支持</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 115,
                        "top": 534,
                        "width": 64,
                        "position": "absolute"
                    },
                    "sort": 19,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840676",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">智能门店终端</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 189,
                        "top": 534,
                        "width": 79,
                        "position": "absolute"
                    },
                    "sort": 20,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1645424840677",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">新品研发定制</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 278,
                        "top": 534,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_f0b62984777f4f78ace944a1631af8b9.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 65,
                        "height": 65,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 39,
                        "top": 157,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "id": 1656052680212,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_71a4e9e0274c4ff28d42dc033bf90c1a.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 65,
                        "height": 65,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 155,
                        "top": 157,
                        "position": "absolute"
                    },
                    "sort": 22,
                    "id": "1656052670648",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_edc6622806864e0a877285b2aaa2aa96.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 65,
                        "height": 65,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 277,
                        "top": 157,
                        "position": "absolute"
                    },
                    "sort": 23,
                    "id": "1656052670649",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_f539fb9fcc2744c6961a8feb7d32c5b2.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 36,
                        "top": 393,
                        "position": "absolute"
                    },
                    "sort": 20,
                    "id": "1656052670650",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_32e08a238094469497d2ad9e19eeedd5.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 122,
                        "top": 393,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "id": "1656052670651",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_01af9d98685742649e62a18a8b72f404.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 202,
                        "top": 393,
                        "position": "absolute"
                    },
                    "sort": 22,
                    "id": "1656052670652",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_6973dc2929364e5d850c7c5b5aca89e1.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 286,
                        "top": 393,
                        "position": "absolute"
                    },
                    "sort": 23,
                    "id": "1656052670653",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_9917cec549fa4e65a602fb4757822b1f.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 36,
                        "top": 482,
                        "position": "absolute"
                    },
                    "sort": 24,
                    "id": "1656052670654",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_8df8d5d317084ad198c37ebe47d9f832.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 122,
                        "top": 482,
                        "position": "absolute"
                    },
                    "sort": 25,
                    "id": "1656052670655",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_b99221fc6b674dedad616d5af9f7c6f3.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 202,
                        "top": 482,
                        "position": "absolute"
                    },
                    "sort": 26,
                    "id": "1656052670656",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202206_24_d9348988ab5a4d76b08ad1537626027e.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 50,
                        "height": 50,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 286,
                        "top": 482,
                        "position": "absolute"
                    },
                    "sort": 27,
                    "id": "1656052670657",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 606,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 6,
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        }
    ],
    "language": "zh-CN",
    "placeholderStyle": {
        "color": "#cbcccf"
    },
    "inputStyle": {
        "color": "#181c25"
    }
}
export default tpl;