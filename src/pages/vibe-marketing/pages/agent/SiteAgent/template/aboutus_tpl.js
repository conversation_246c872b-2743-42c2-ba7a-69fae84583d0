
/**
 * 公司介绍模版
 */

const tpl = {
    "id": "ef356b20b63248c1a7b67ddf198c7702",
    "type": "page",
    "name": "关于我们",
    "title": "",
    "version": "4.4.3",
    "cover": "",
    "shareOpts": {
        "title": "",
        "desc": "",
        "link": "",
        "imgUrl": ""
    },
    "style": {
        "width": 375,
        "backgroundColor": "#fff",
        "backgroundSize": "cover",
        "backgroundRepeat": "no-repeat",
        "backgroundImage": "",
        "backgroundPosition": "center center"
    },
    "backgroundFillType": "filling",
    "dataSourceAction": {

    },
    "components": [
        {
            "name": "图片",
            "type": "image",
            "images": [
                {
                    "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_74fcbaf604e645ae9b7bad22bdbbfecb.png&ea=fs",
                    "action": {

                    }
                }
            ],
            "imageGap": 4,
            "style": {
                "display": "flex",
                "width": 375,
                "height": 167,
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "borderRadius": 0,
                "background": "rgba(255, 255, 255, 0)",
                "backgroundRepeat": "no-repeat",
                "backgroundSize": "cover",
                "backgroundPosition": "center center",
                "borderWidth": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 0,
            "id": "1644475966750",
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1644549813024,
            "name": "文本",
            "type": "text",
            "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">公司简介</strong></p>",
            "style": {
                "paddingBottom": 6,
                "paddingLeft": 12,
                "paddingRight": 12,
                "paddingTop": 12,
                "background": "rgba(255, 255, 255, 0)",
                "fontSize": 14,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5",
                "opacity": 100
            },
            "sort": 1,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": "1644475966752",
            "name": "文本",
            "type": "text",
            "value": "<p style=\"text-align: justify;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">公司总部位于中国深圳，是一家专注体外诊断医疗器械及试剂研发、生产、销售、服务的国家级高新技术企业。</span></p><p style=\"text-align: justify;\"><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">公司坚持创新引领，研发人员占比超40%，年度研发投入超营收20%，拥有CNAS认证的国家级血细胞参考测量实验室。截至目前，公司申请专利500余件，全部产品通过国际权威质量体系认证。</span></p>",
            "style": {
                "paddingBottom": 20,
                "paddingLeft": 12,
                "paddingRight": 12,
                "paddingTop": 0,
                "background": "rgba(255, 255, 255, 0)",
                "fontSize": 14,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5",
                "opacity": 100
            },
            "sort": 2,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1644551144682,
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(245, 245, 245, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 3,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1644551129713,
            "name": "文本",
            "type": "text",
            "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">企业文化</strong></p>",
            "style": {
                "paddingBottom": 6,
                "paddingLeft": 12,
                "paddingRight": 12,
                "paddingTop": 12,
                "background": "rgba(255, 255, 255, 0)",
                "fontSize": 14,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5",
                "opacity": 100
            },
            "sort": 4,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": "1644475966754",
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_11_cc1d234743dd4a16a1ceec697eba63f8.png&ea=fs",
                            "action": {

                            }
                        },
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_11_68d270d061eb4be0bd684574aac9cad1.png&ea=fs",
                            "uploadType": "upload",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 14,
                    "style": {
                        "display": "flex",
                        "width": 357,
                        "height": 93,
                        "paddingBottom": 0,
                        "paddingLeft": 2,
                        "paddingRight": 2,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 8,
                        "top": 10,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "id": "1644475966755",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_11_8fae029a19da4582a3869d155bf7eb85.png&ea=fs",
                            "action": {

                            }
                        },
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_11_0b757b2f0bd74fd1a371331b546f74c8.png&ea=fs",
                            "uploadType": "upload",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 14,
                    "style": {
                        "display": "flex",
                        "width": 358,
                        "height": 94,
                        "paddingBottom": 0,
                        "paddingLeft": 2,
                        "paddingRight": 2,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 8,
                        "top": 195,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": "1644475966756",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966757",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>企业使命</p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 13,
                        "top": 112,
                        "width": 150,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966758",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">以创新医疗呵护生命健康</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 13,
                        "top": 138,
                        "width": 149,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966759",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>企业愿景</p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 195,
                        "top": 112,
                        "width": 150,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966760",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">成为全球领先的联检产品服务供应商</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 195,
                        "top": 138,
                        "width": 159,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966761",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>价值观</p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 13,
                        "top": 299,
                        "width": 149,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966762",
                    "name": "文本",
                    "type": "text",
                    "value": "<p>行为准则</p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 195,
                        "top": 299,
                        "width": 159,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966763",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 17px; color: rgb(145, 149, 158); letter-spacing: 0px;\">以客户为中心</span></p><p><span style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 17px; color: rgb(145, 149, 158); letter-spacing: 0px;\">坚持创新引领  坚持自我批判</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 13,
                        "top": 324,
                        "width": 159,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966764",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">把奋斗进行到底</span></p><p><span style=\"font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\">把不可能变为可能</span></p>",
                    "style": {
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 195,
                        "top": 321,
                        "width": 150,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 9,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 382,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 5
        },
        {
            "id": 1644559825415,
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(245, 245, 245, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 6,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1644559832743,
            "name": "文本",
            "type": "text",
            "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">公司资讯</strong></p>",
            "style": {
                "paddingBottom": 6,
                "paddingLeft": 12,
                "paddingRight": 12,
                "paddingTop": 12,
                "background": "rgba(255, 255, 255, 0)",
                "fontSize": 14,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5",
                "opacity": 100
            },
            "sort": 7,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "name": "内容",
            "type": "content",
            "url": "",
            "range": "article",
            "layout": "row",
            "contentObjectType": 6,
            "contentObjectIds": [

            ],
            "siteGroupId": "-1",
            "titleColor": "#181C25",
            "descColor": "#91959E",
            "style": {
                "width": 375,
                "height": "auto",
                "paddingBottom": 6,
                "paddingLeft": 12,
                "paddingRight": 12,
                "paddingTop": 6,
                "background": "rgba(0,0,0,0)",
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 8,
            "id": "1644475966774",
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            },
            "items": [

            ]
        },
        {
            "id": "1644475966753",
            "name": "分割线",
            "type": "line",
            "line": {
                "style": {
                    "width": 345,
                    "borderBottomWidth": 10,
                    "borderBottomColor": "rgba(245, 245, 245, 1)",
                    "borderBottomStyle": "solid",
                    "display": "inline-block"
                }
            },
            "style": {
                "paddingBottom": 0,
                "paddingLeft": 0,
                "paddingRight": 0,
                "paddingTop": 0,
                "width": 375,
                "background": "rgba(255, 255, 255, 0)",
                "textAlign": "center",
                "lineHeight": 0,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5"
            },
            "sort": 9,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "name": "文本",
            "type": "text",
            "url": "",
            "items": [

            ],
            "range": "all",
            "layout": "row",
            "marketingEventTypes": [
                "conference",
                "live"
            ],
            "marketingEventIds": [

            ],
            "titleColor": "#181C25",
            "descColor": "#91959E",
            "style": {
                "paddingBottom": 6,
                "paddingLeft": 12,
                "paddingRight": 12,
                "paddingTop": 12,
                "background": "rgba(255, 255, 255, 0)",
                "fontSize": 14,
                "borderWidth": 0,
                "borderRadius": 0,
                "borderStyle": "none",
                "borderColor": "#e9edf5",
                "opacity": 100
            },
            "sort": 10,
            "id": 1644549813024,
            "components": [

            ],
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            },
            "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">公司地址</strong></p>"
        },
        {
            "id": "1644475966775",
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202202_11_b9f0d86df416475589c28eb2e3d9181d.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 356,
                        "height": 161.38666666666666,
                        "paddingBottom": 0,
                        "paddingLeft": 8,
                        "paddingRight": 8,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 9,
                        "top": 13,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "id": "1644475966776",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1644475966777",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 26px; color: rgb(51, 51, 51); letter-spacing: 0px;\">公司地址：深圳大冲国际中心22楼</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 26px; color: rgb(51, 51, 51); letter-spacing: 0px;\">联系电话：0755-234567</span></p><p><span style=\"font-size: 13px; font-family: Helvetica, Arial, sans-serif; line-height: 26px; color: rgb(51, 51, 51); letter-spacing: 0px;\">公司邮箱：<EMAIL></span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 16,
                        "top": 186,
                        "width": 329,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 279,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 11
        }
    ],
    "language": "zh-CN",
    "placeholderStyle": {
        "color": "#cbcccf"
    },
    "inputStyle": {
        "color": "#181c25"
    }
}
export default tpl;