
/**
 * 教育机构培训课程报名表单模版
 */

const tpl = {
  "id": "47039933c350444db28de165d67a6107",
  "type": "page",
  "name": "教育机构培训",
  "title": "",
  "version": "4.4.2",
  "cover": "",
  "shareOpts": {
      "title": "",
      "desc": "",
      "link": "",
      "imgUrl": ""
  },
  "style": {
      "width": 375,
      "backgroundColor": "#fff",
      "backgroundSize": "cover",
      "backgroundRepeat": "no-repeat",
      "backgroundImage": "",
      "backgroundPosition": "center center"
  },
  "backgroundFillType": "filling",
  "dataSourceAction": {

  },
  "components": [
      {
          "name": "图片",
          "type": "image",
          "images": [
              {
                  "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_6255d3ef554f417b8a2b42c759d9fe30.png&ea=fs",
                  "action": {

                  }
              }
          ],
          "imageGap": 4,
          "style": {
              "display": "flex",
              "width": 375,
              "height": 151,
              "paddingBottom": 0,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 0,
              "borderRadius": 0,
              "background": "rgba(255, 255, 255, 0)",
              "backgroundRepeat": "no-repeat",
              "backgroundSize": "cover",
              "backgroundPosition": "center center",
              "borderWidth": 0,
              "borderStyle": "none",
              "borderColor": "#e9edf5"
          },
          "sort": 0,
          "id": "1643007361878",
          "components": [

          ],
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": "1643007361884",
          "name": "菜单",
          "type": "menu",
          "value": "",
          "layout": "grid4",
          "menus": [
              {
                  "name": "英语课程",
                  "icon": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_4240a691c57b4ff6b058fc56bc24598f.jpg",
                  "iconStyle": {
                      "color": "#f56c6c",
                      "fontSize": 22
                  },
                  "action": {
                      "type": "form",
                      "label": "跳转我的报名"
                  },
                  "iconType": "picture",
                  "style": {

                  }
              },
              {
                  "name": "师资活动",
                  "icon": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_1f35aa78b08e4751816aac9e36664b5e.jpg",
                  "iconStyle": {
                      "color": "rgba(255, 215, 0, 1)",
                      "fontSize": 22
                  },
                  "action": {
                      "type": "distribution",
                      "label": "跳转我的分销"
                  },
                  "iconType": "picture",
                  "style": {

                  }
              },
              {
                  "name": "优惠活动",
                  "icon": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_40df3d5c6e424960b0dfa39fcac51aae.jpg",
                  "iconStyle": {
                      "color": "rgba(255, 120, 0, 1)",
                      "fontSize": 22
                  },
                  "action": {
                      "type": "businessCardHolder",
                      "label": "跳转名片夹"
                  },
                  "iconType": "picture",
                  "style": {

                  }
              },
              {
                  "name": "教学环境",
                  "icon": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_1d05bddc249845b49bd30d1add7095ee.jpg",
                  "iconStyle": {
                      "color": "#545861",
                      "fontSize": 22
                  },
                  "action": {

                  },
                  "iconType": "picture",
                  "style": {

                  }
              }
          ],
          "style": {
              "paddingBottom": 10,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 10,
              "background": "rgba(255, 255, 255, 0)",
              "fontSize": 14,
              "color": "#181c25",
              "borderWidth": 0,
              "borderRadius": 0,
              "borderStyle": "none",
              "borderColor": "#e9edf5"
          },
          "sort": 1,
          "components": [

          ],
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1643014340276,
          "name": "分割线",
          "type": "line",
          "line": {
              "style": {
                  "width": 345,
                  "borderBottomWidth": 10,
                  "borderBottomColor": "rgba(245, 245, 245, 1)",
                  "borderBottomStyle": "solid",
                  "display": "inline-block"
              }
          },
          "style": {
              "paddingBottom": 0,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 0,
              "width": 375,
              "background": "rgba(255, 255, 255, 0)",
              "textAlign": "center",
              "lineHeight": 0,
              "borderWidth": 0,
              "borderRadius": 0,
              "borderStyle": "none",
              "borderColor": "#e9edf5"
          },
          "sort": 2,
          "components": [

          ],
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1643009158933,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "id": 1643009161475,
                  "label": "按钮",
                  "name": "",
                  "tip": "提交成功",
                  "type": "button",
                  "position": "none",
                  "required": false,
                  "isFormComp": false,
                  "wrapStyle": {
                      "position": "none",
                      "background": "rgba(255,255,255,.9)"
                  },
                  "style": {
                      "height": 15,
                      "width": 6,
                      "fontSize": 16,
                      "background": "#409EFF",
                      "borderRadius": 0,
                      "color": "#fff",
                      "letterSpacing": 0,
                      "lineHeight": 45,
                      "textAlign": "center",
                      "margin": "0 auto",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 12,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 0,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153172",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">推荐课程</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 2,
                      "width": 345,
                      "position": "absolute"
                  },
                  "sort": 1,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_89979cfd4b82453bb374cb315428407e.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 167,
                      "height": 91,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 9,
                      "top": 40,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "id": "1643009153173",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_c5e3b6b52968450eae4d6b284c6e0e93.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 171,
                      "height": 93.024,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 194,
                      "top": 38,
                      "position": "absolute"
                  },
                  "sort": 3,
                  "id": "1643009153175",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153176",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 0px; color: rgb(51, 51, 51); letter-spacing: 0px;\">成人商务英语</span></p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 19,
                      "top": 142,
                      "width": 148,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 4,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153177",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 0px; color: rgb(51, 51, 51); letter-spacing: 0px;\">青少年英语课程</span></p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 203,
                      "top": 142,
                      "width": 153,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 5,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_8be8e6caed2a433b953c494f03b58a40.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 165,
                      "height": 88.88,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 9,
                      "top": 175,
                      "position": "absolute"
                  },
                  "sort": 6,
                  "id": 1643009569250,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153178",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">零基础口语进阶课程</span></p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 19,
                      "top": 274,
                      "width": 145,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 7,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_2e71c72f471f46ff90f082ac5b6a92d3.png&ea=fs",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 170,
                      "height": 92.47999999999999,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 194,
                      "top": 172,
                      "position": "absolute"
                  },
                  "sort": 8,
                  "id": "1643009153180",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153181",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">英语口语小班1v1</span></p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 203,
                      "top": 274,
                      "width": 153,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 9,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 310,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "",
              "backgroundImage": ""
          },
          "sort": 3,
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1643014344260,
          "name": "分割线",
          "type": "line",
          "line": {
              "style": {
                  "width": 345,
                  "borderBottomWidth": 10,
                  "borderBottomColor": "rgba(245, 245, 245, 1)",
                  "borderBottomStyle": "solid",
                  "display": "inline-block"
              }
          },
          "style": {
              "paddingBottom": 0,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 0,
              "width": 375,
              "background": "rgba(255, 255, 255, 0)",
              "textAlign": "center",
              "lineHeight": 0,
              "borderWidth": 0,
              "borderRadius": 0,
              "borderStyle": "none",
              "borderColor": "#e9edf5"
          },
          "sort": 4,
          "components": [

          ],
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": "1643009153182",
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "id": "1643009153183",
                  "label": "按钮",
                  "name": "",
                  "tip": "提交成功",
                  "type": "button",
                  "position": "none",
                  "required": false,
                  "isFormComp": false,
                  "wrapStyle": {
                      "position": "none",
                      "background": "rgba(255,255,255,.9)"
                  },
                  "style": {
                      "height": 15,
                      "width": 6,
                      "fontSize": 16,
                      "background": "#409EFF",
                      "borderRadius": 0,
                      "color": "#fff",
                      "letterSpacing": 0,
                      "lineHeight": 30,
                      "textAlign": "center",
                      "margin": "0 auto",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 12,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 0,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153184",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">明星老师</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 20,
                      "top": 2,
                      "width": 345,
                      "position": "absolute"
                  },
                  "sort": 1,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                          "action": {

                          },
                          "uploadType": "upload"
                      },
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_532074ad828c491498ff04186c8399b0.png&ea=fs",
                          "uploadType": "upload",
                          "action": {

                          }
                      },
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_924a232bfdeb46918bf6a42e2a9a486c.png&ea=fs",
                          "uploadType": "upload",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 30,
                  "style": {
                      "display": "flex",
                      "width": 330,
                      "height": 90,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 375,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 22,
                      "top": 50,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 2,
                  "id": "1643009153185",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153186",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\">杨宝峰</p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 40,
                      "top": 147,
                      "width": 58,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 3,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153187",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\">Billy Holmes</p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 148,
                      "top": 147,
                      "width": 79,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 4,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153189",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\"><span style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">Nathan Bennett</span></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 255,
                      "top": 140,
                      "width": 105,
                      "position": "absolute"
                  },
                  "sort": 5,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_a5f7221edd704a22853e4308c7778322.png&ea=fs",
                          "action": {

                          }
                      },
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_d5273f8ff52e43c287630b752ecc328e.png&ea=fs",
                          "uploadType": "upload",
                          "action": {

                          }
                      },
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_449679f9029143f58d196267b884ca36.png&ea=fs",
                          "uploadType": "upload",
                          "action": {

                          }
                      }
                  ],
                  "imageGap": 30,
                  "style": {
                      "display": "flex",
                      "width": 330,
                      "height": 90,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 244,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 22,
                      "top": 181,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 6,
                  "id": "1643009153190",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153191",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\">Cecelia Fuller</p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 20,
                      "top": 274,
                      "width": 94,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 7,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153192",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\">颜娟兰</p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 153,
                      "top": 274,
                      "width": 74,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 8,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153193",
                  "name": "文本",
                  "type": "text",
                  "value": "<p style=\"text-align: center;\">Nathan Bennett</p>",
                  "style": {
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 255,
                      "top": 274,
                      "width": 110,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 9,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 320,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "",
              "backgroundImage": ""
          },
          "sort": 5
      },
      {
          "id": 1643014350902,
          "name": "分割线",
          "type": "line",
          "line": {
              "style": {
                  "width": 345,
                  "borderBottomWidth": 10,
                  "borderBottomColor": "rgba(245, 245, 245, 1)",
                  "borderBottomStyle": "solid",
                  "display": "inline-block"
              }
          },
          "style": {
              "paddingBottom": 0,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 0,
              "width": 375,
              "background": "rgba(255, 255, 255, 0)",
              "textAlign": "center",
              "lineHeight": 0,
              "borderWidth": 0,
              "borderRadius": 0,
              "borderStyle": "none",
              "borderColor": "#e9edf5"
          },
          "sort": 6,
          "components": [

          ],
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": 1643011307842,
          "name": "自定义布局",
          "key": "auto-container",
          "type": "container",
          "typeValue": "auto",
          "components": [
              {
                  "id": 1643009161475,
                  "label": "按钮",
                  "name": "",
                  "tip": "提交成功",
                  "type": "button",
                  "position": "none",
                  "required": false,
                  "isFormComp": false,
                  "wrapStyle": {
                      "position": "none",
                      "background": "rgba(255,255,255,.9)"
                  },
                  "style": {
                      "height": 15,
                      "width": 6,
                      "fontSize": 16,
                      "background": "#409EFF",
                      "borderRadius": 0,
                      "color": "#fff",
                      "letterSpacing": 0,
                      "lineHeight": 45,
                      "textAlign": "center",
                      "margin": "0 auto",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 12,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 0,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153172",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">教学环境</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 2,
                      "width": 345,
                      "position": "absolute"
                  },
                  "sort": 1,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "name": "图片",
                  "type": "image",
                  "images": [
                      {
                          "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202201_24_eded4d1a97884b03ad01af7a5755267c.png&ea=fs",
                          "action": {

                          },
                          "uploadType": "upload"
                      }
                  ],
                  "imageGap": 4,
                  "style": {
                      "display": "flex",
                      "width": 355,
                      "height": 252,
                      "paddingBottom": 0,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 0,
                      "borderRadius": 0,
                      "background": "rgba(255, 255, 255, 0)",
                      "backgroundRepeat": "no-repeat",
                      "backgroundSize": "cover",
                      "backgroundPosition": "center center",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 11,
                      "top": 47,
                      "position": "absolute"
                  },
                  "sort": 2,
                  "id": "1643009153175",
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 310,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "",
              "backgroundImage": ""
          },
          "sort": 7,
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": "1643009153196",
          "name": "分割线",
          "type": "line",
          "line": {
              "style": {
                  "width": 345,
                  "borderBottomWidth": 10,
                  "borderBottomColor": "rgba(245, 245, 245, 1)",
                  "borderBottomStyle": "solid",
                  "display": "inline-block"
              }
          },
          "style": {
              "paddingBottom": 0,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 0,
              "width": 375,
              "background": "rgba(255, 255, 255, 0)",
              "textAlign": "center",
              "lineHeight": 0,
              "borderWidth": 0,
              "borderRadius": 0,
              "borderStyle": "none",
              "borderColor": "#e9edf5"
          },
          "sort": 8,
          "components": [

          ],
          "placeholderStyle": {
              "color": "#cbcccf"
          },
          "inputStyle": {
              "color": "#181c25"
          }
      },
      {
          "id": "1643009153410",
          "name": "表单",
          "key": "form-container",
          "type": "container",
          "typeValue": "form",
          "components": [
              {
                  "id": "1643009153411",
                  "label": "提交",
                  "name": "量身定制学习方案",
                  "tip": "提交成功",
                  "type": "button",
                  "position": "none",
                  "required": false,
                  "isFormComp": true,
                  "wrapStyle": {
                      "position": "none",
                      "background": "rgba(255,255,255,.9)"
                  },
                  "style": {
                      "height": 45,
                      "width": 345,
                      "fontSize": 16,
                      "background": "rgba(64, 172, 255, 1)",
                      "borderRadius": 4,
                      "color": "#fff",
                      "letterSpacing": 0,
                      "lineHeight": 45,
                      "textAlign": "center",
                      "margin": "0 auto",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "position": "absolute",
                      "left": 15,
                      "top": 371,
                      "opacity": 100
                  },
                  "sort": 0,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153440",
                  "label": "姓名",
                  "name": "姓名",
                  "title": "姓名",
                  "type": "input",
                  "typeValue": "text",
                  "fieldName": "name",
                  "defaultValueOpen": false,
                  "defaultValue": "",
                  "globalCacheField": "",
                  "defaultValueType": "manual",
                  "required": true,
                  "placeholder": "请输入姓名",
                  "isFormComp": true,
                  "style": {
                      "color": "#181C25",
                      "width": 345,
                      "fontSize": 14,
                      "paddingBottom": 0,
                      "paddingTop": 0,
                      "paddingLeft": 12,
                      "paddingRight": 12,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 46,
                      "position": "absolute"
                  },
                  "titleStyle": {
                      "color": "#181C25",
                      "fontSize": 14,
                      "lineHeight": 16,
                      "paddingBottom": 6,
                      "paddingTop": 6,
                      "whiteSpace": "normal"
                  },
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "height": 45,
                      "color": "#181c25",
                      "background": "#fff"
                  },
                  "sort": 1
              },
              {
                  "id": "1643009153610",
                  "label": "按钮",
                  "name": "",
                  "tip": "提交成功",
                  "type": "button",
                  "position": "none",
                  "required": false,
                  "isFormComp": false,
                  "wrapStyle": {
                      "position": "none",
                      "background": "rgba(255,255,255,.9)"
                  },
                  "style": {
                      "height": 15,
                      "width": 6,
                      "fontSize": 16,
                      "background": "#409EFF",
                      "borderRadius": 0,
                      "color": "#fff",
                      "letterSpacing": 0,
                      "lineHeight": 45,
                      "textAlign": "center",
                      "margin": "0 auto",
                      "borderWidth": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 0,
                      "top": 12,
                      "position": "absolute",
                      "opacity": 100
                  },
                  "sort": 2,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153779",
                  "name": "文本",
                  "type": "text",
                  "value": "<p><strong style=\"font-size: 16px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">预约报名免费试课</strong></p>",
                  "style": {
                      "paddingBottom": 6,
                      "paddingLeft": 0,
                      "paddingRight": 0,
                      "paddingTop": 6,
                      "background": "rgba(255, 255, 255, 0)",
                      "fontSize": 14,
                      "borderWidth": 0,
                      "borderRadius": 0,
                      "borderStyle": "none",
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 3,
                      "width": 345,
                      "position": "absolute"
                  },
                  "sort": 3,
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "color": "#181c25"
                  }
              },
              {
                  "id": "1643009153864",
                  "label": "单行文本",
                  "name": "感兴趣的课程",
                  "title": "感兴趣的课程",
                  "type": "input",
                  "typeValue": "text",
                  "fieldName": "text5_1643009153857",
                  "defaultValue": "",
                  "defaultValueOpen": false,
                  "globalCacheField": "",
                  "defaultValueType": "manual",
                  "required": true,
                  "placeholder": "请输入",
                  "isFormComp": true,
                  "titleStyle": {
                      "color": "#181C25",
                      "fontSize": 14,
                      "lineHeight": 16,
                      "paddingBottom": 6,
                      "paddingTop": 6,
                      "whiteSpace": "normal"
                  },
                  "style": {
                      "color": "#181C25",
                      "width": 345,
                      "fontSize": 14,
                      "paddingBottom": 0,
                      "paddingTop": 0,
                      "paddingLeft": 12,
                      "paddingRight": 12,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 129,
                      "position": "absolute"
                  },
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "height": 45,
                      "color": "#181c25",
                      "background": "#fff"
                  },
                  "sort": 4
              },
              {
                  "id": "1643009154044",
                  "label": "手机号",
                  "name": "手机号",
                  "title": "手机号",
                  "type": "input",
                  "typeValue": "number",
                  "fieldName": "phone",
                  "pattern": "^1[0-9]\\d{9}$",
                  "defaultValue": "",
                  "defaultValueOpen": false,
                  "globalCacheField": "",
                  "defaultValueType": "manual",
                  "required": true,
                  "verify": true,
                  "weChatAuthorizationButton": false,
                  "placeholder": "请输入手机号",
                  "isFormComp": true,
                  "weChatAuthorizationButtonStyle": {
                      "color": "#fff",
                      "background": "#09BB07",
                      "fontSize": 14,
                      "borderStyle": "solid",
                      "borderWidth": 0,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5"
                  },
                  "verifyButtonStyle": {
                      "color": "rgba(43, 156, 250, 1)",
                      "background": "#ffffff",
                      "fontSize": 14,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "rgba(43, 156, 250, 1)"
                  },
                  "titleStyle": {
                      "color": "#181C25",
                      "fontSize": 14,
                      "lineHeight": 16,
                      "paddingBottom": 6,
                      "paddingTop": 6,
                      "whiteSpace": "normal"
                  },
                  "style": {
                      "color": "#181C25",
                      "width": 345,
                      "fontSize": 14,
                      "paddingBottom": 0,
                      "paddingTop": 0,
                      "paddingLeft": 12,
                      "paddingRight": 12,
                      "borderStyle": "solid",
                      "borderWidth": 1,
                      "borderRadius": 3,
                      "borderColor": "#e9edf5",
                      "left": 15,
                      "top": 211,
                      "position": "absolute"
                  },
                  "placeholderStyle": {
                      "color": "#cbcccf"
                  },
                  "inputStyle": {
                      "height": 45,
                      "color": "rgba(43, 156, 250, 1)",
                      "background": "#fff"
                  },
                  "sort": 5
              }
          ],
          "current": 0,
          "slideIndex": 0,
          "layout": "single",
          "fillType": "color",
          "fillMethod": "filling",
          "typesetting": "absolute",
          "style": {
              "width": 375,
              "height": 477,
              "overflow": "hidden",
              "position": "relative",
              "backgroundColor": "",
              "backgroundImage": ""
          },
          "sort": 9
      }
  ],
  "language": "zh-CN",
  "placeholderStyle": {
      "color": "#cbcccf"
  },
  "inputStyle": {
      "color": "#181c25"
  }
}
export default tpl;