
/**
 * 教育行业报名表单、考研冲刺训练营，活动报名表单模版
 */

const tpl = {
    "id": "42c61dba585a4fe2802f491aec6955bc",
    "type": "page",
    "name": "",
    "title": "",
    "version": "4.4.0-32",
    "cover": "",
    "shareOpts": {
        "title": "",
        "desc": "",
        "link": "",
        "imgUrl": ""
    },
    "style": {
        "width": 375,
        "backgroundColor": "#fff",
        "backgroundSize": "100%",
        "backgroundRepeat": "no-repeat",
        "backgroundImage": ""
    },
    "backgroundFillType": "filling",
    "dataSourceAction": {

    },
    "components": [
        {
            "id": 1638784345222,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_13_173a292a96664e6fbadbb578017359dd.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 279,
                        "height": 59.52,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 48,
                        "top": 169,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "id": "1639361801835",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449059",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(0, 0, 0); line-height: 24px; font-size: 14px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">时间：2021.12.10—2021.12.20</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 76,
                        "top": 181,
                        "width": 251,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_4d003939a6e94a42b669bc10616c6775.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 99,
                        "height": 20.064,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 29,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "id": 1638784517507,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1639367188855,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"font-size: 44px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(254, 254, 254); letter-spacing: 0px;\">考研冲刺训练营</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 110,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "image",
            "fillMethod": "stretch",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 480,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": "url(https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_13_4bacbaf977eb43cc8f882aaac1131518.png&ea=fs)",
                "backgroundSize": "100% 100%",
                "backgroundRepeat": "no-repeat"
            },
            "sort": "0",
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638861556826,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 303,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 47,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 40,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 22,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "id": "1638777449060",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784785907,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(0, 0, 0);\">本次活动以“技巧精讲，高效备考”为主题，10天课程速功考研政治英语近3年真题，考研政治真题选择题考点分布统计，分析考研英语理念真题常见词组及短语</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 108,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 344,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 3,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_99b4144d7a6443bfa9fa2cb2c494caf3.png&ea=fs",
                            "action": {

                            },
                            "uploadType": "upload"
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 311,
                        "height": 145,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 10,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 33,
                        "top": 215,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "id": 1638784853182,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449063",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(41, 178, 171); font-size: 18px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">活动主题</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 32,
                        "top": 66,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_13_aab1035e770b48c8a1fc1aa6ca5d7bbd.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 180,
                        "height": 42,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 99,
                        "top": 14,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 6,
                    "id": "1639361801836",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 403,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(93, 196, 177, 1)",
                "backgroundImage": ""
            },
            "sort": "1",
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638844635548,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 40,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 1,
                        "top": 15,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "id": "1639621926206",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 360,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 1,
                        "top": 54,
                        "position": "absolute"
                    },
                    "sort": 1,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_13_aab1035e770b48c8a1fc1aa6ca5d7bbd.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 180,
                        "height": 42,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 97,
                        "top": 13,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 2,
                    "id": "1638777449060",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638784785907,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(24, 28, 37); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 125,
                        "top": 106,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 1,
                        "top": 410,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 4,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 83,
                        "height": 99,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 32,
                        "top": 107,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "id": 1638784853182,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449063",
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(255, 255, 255); font-size: 16px; line-height: 24px; letter-spacing: 0px;\">会议嘉宾</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 71,
                        "top": 162,
                        "width": 313,
                        "position": "absolute"
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449064",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">考研政治名师</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 193,
                        "top": 108,
                        "width": 156,
                        "position": "absolute"
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449065",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">政治经济学博士，从事考研政治教学辅导，17年考研选择题命中率85%，押题《考前100题》作者。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 129,
                        "top": 131,
                        "width": 220,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449066",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(24, 28, 37); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 125,
                        "top": 219,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 9,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 83,
                        "height": 99,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 222,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 10,
                    "id": "1638777449067",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449068",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">考研政治名师</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 189,
                        "top": 221,
                        "width": 156,
                        "position": "absolute"
                    },
                    "sort": 11,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449069",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">政治经济学博士，从事考研政治教学辅导，17年考研选择题命中率85%，押题《考前100题》作者。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 129,
                        "top": 251,
                        "width": 220,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 12,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449070",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"color: rgb(24, 28, 37); font-size: 17px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">杨宝峰</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 125,
                        "top": 334,
                        "width": 56,
                        "position": "absolute"
                    },
                    "sort": 13,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_dc31e6d966d646de871908a0d02f3001.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 83,
                        "height": 99,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 32,
                        "top": 337,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 14,
                    "id": "1638777449071",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449072",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">考研政治名师</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 189,
                        "top": 336,
                        "width": 156,
                        "position": "absolute"
                    },
                    "sort": 15,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449073",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 11px; color: rgb(84, 88, 97);\">政治经济学博士，从事考研政治教学辅导，17年考研选择题命中率85%，押题《考前100题》作者。</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 125,
                        "top": 361,
                        "width": 220,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 16,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1639622182760,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"line-height: 24px; font-size: 18px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif; color: rgb(41, 178, 171);\">活动嘉宾</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 25,
                        "top": 62,
                        "width": 327,
                        "position": "absolute"
                    },
                    "sort": 17,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 473,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(93, 196, 177, 1)",
                "backgroundImage": ""
            },
            "sort": "2",
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638844633252,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 482,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 2,
                        "top": 56,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 2,
                        "top": 510,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 40,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 2,
                        "top": 18,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "id": 1638844744198,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638861613963,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 37,
                        "top": 131,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 3,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449972",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">开课典礼</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 145,
                        "top": 128,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449973",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 37,
                        "top": 183,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 5,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449974",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">考研政治知识点梳理</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 145,
                        "top": 181,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 6,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449976",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 145,
                        "top": 207,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 7,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449977",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 37,
                        "top": 262,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 8,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449978",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(24, 28, 37); line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">考研英语阅读</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 147,
                        "top": 261,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 9,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449980",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 147,
                        "top": 290,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 10,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449986",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 37,
                        "top": 342,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 11,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449987",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; color: rgb(24, 28, 37); line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">考研英语作文培训要点</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 147,
                        "top": 341,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 12,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449988",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"color: rgb(51, 51, 51); font-size: 12px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\">浙江大学&nbsp;方群教授</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 147,
                        "top": 372,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 13,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449990",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 37,
                        "top": 429,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 14,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1638777449991",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">模拟考试</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 147,
                        "top": 426,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 15,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_13_aab1035e770b48c8a1fc1aa6ca5d7bbd.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 180,
                        "height": 42,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 95,
                        "top": 16,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 19,
                    "id": 1639622435833,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1639622457674,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"line-height: 24px; font-size: 18px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif; color: rgb(41, 178, 171);\">活动流程</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 26,
                        "top": 78,
                        "width": 327,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 20,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1639623941954,
                    "name": "分割线",
                    "type": "line",
                    "line": {
                        "style": {
                            "width": 345,
                            "borderBottomWidth": 1,
                            "borderBottomColor": "rgba(228, 228, 228, 1)",
                            "borderBottomStyle": "solid",
                            "display": "inline-block"
                        }
                    },
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "paddingTop": 6,
                        "width": 330,
                        "background": "rgba(255, 255, 255, 0)",
                        "textAlign": "center",
                        "lineHeight": 0,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 26,
                        "top": 159,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1639621926242",
                    "name": "分割线",
                    "type": "line",
                    "line": {
                        "style": {
                            "width": 345,
                            "borderBottomWidth": 1,
                            "borderBottomColor": "rgba(228, 228, 228, 1)",
                            "borderBottomStyle": "solid",
                            "display": "inline-block"
                        }
                    },
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "paddingTop": 6,
                        "width": 330,
                        "background": "rgba(255, 255, 255, 0)",
                        "textAlign": "center",
                        "lineHeight": 0,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 23,
                        "top": 242,
                        "position": "absolute"
                    },
                    "sort": 22,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1639621926243",
                    "name": "分割线",
                    "type": "line",
                    "line": {
                        "style": {
                            "width": 345,
                            "borderBottomWidth": 1,
                            "borderBottomColor": "rgba(228, 228, 228, 1)",
                            "borderBottomStyle": "solid",
                            "display": "inline-block"
                        }
                    },
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "paddingTop": 6,
                        "width": 330,
                        "background": "rgba(255, 255, 255, 0)",
                        "textAlign": "center",
                        "lineHeight": 0,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 22,
                        "top": 324,
                        "position": "absolute"
                    },
                    "sort": 23,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1639621926244",
                    "name": "分割线",
                    "type": "line",
                    "line": {
                        "style": {
                            "width": 345,
                            "borderBottomWidth": 1,
                            "borderBottomColor": "rgba(228, 228, 228, 1)",
                            "borderBottomStyle": "solid",
                            "display": "inline-block"
                        }
                    },
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "paddingTop": 6,
                        "width": 330,
                        "background": "rgba(255, 255, 255, 0)",
                        "textAlign": "center",
                        "lineHeight": 0,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 24,
                        "top": 406,
                        "position": "absolute"
                    },
                    "sort": 21,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1639621926245",
                    "name": "分割线",
                    "type": "line",
                    "line": {
                        "style": {
                            "width": 345,
                            "borderBottomWidth": 1,
                            "borderBottomColor": "rgba(228, 228, 228, 1)",
                            "borderBottomStyle": "solid",
                            "display": "inline-block"
                        }
                    },
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "paddingTop": 6,
                        "width": 330,
                        "background": "rgba(255, 255, 255, 0)",
                        "textAlign": "center",
                        "lineHeight": 0,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 22,
                        "top": 464,
                        "position": "absolute"
                    },
                    "sort": 22,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1639621926246",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"font-size: 14px;\">14:00-14:10</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 37,
                        "top": 487,
                        "width": 78,
                        "position": "absolute"
                    },
                    "sort": 23,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": "1639621926247",
                    "name": "文本",
                    "type": "text",
                    "value": "<p><strong style=\"font-size: 14px; font-family: Helvetica, Arial, sans-serif; line-height: 24px; color: rgb(51, 51, 51); letter-spacing: 0px;\">活动总结</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 147,
                        "top": 484,
                        "width": 210,
                        "position": "absolute"
                    },
                    "sort": 24,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 566,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(93, 196, 177, 1)",
                "backgroundImage": ""
            },
            "sort": "3",
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638861562161,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 321,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 57,
                        "position": "absolute"
                    },
                    "sort": 0,
                    "id": 1638784674592,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_16b2d29489b54f81b502f31e5e98acc8.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 55,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 366,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": "1638777449061",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 40,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 18,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "id": 1638844744198,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "文本",
                    "type": "text",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_7315009c530140e3ac4b755b93984199.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 72,
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 153,
                        "top": 68,
                        "position": "absolute",
                        "fontSize": 14
                    },
                    "sort": 3,
                    "id": "1638777449074",
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    },
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(41, 178, 171); line-height: 24px; letter-spacing: 0px; font-size: 18px; font-family: Helvetica, Arial, sans-serif;\">活动概要</strong></p>"
                },
                {
                    "id": 1638847060239,
                    "name": "文本",
                    "type": "text",
                    "value": "<p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">会议时间：2021年7月18日 14:00-17:30</span></p><p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">咨询电话：0755-23456543</span></p><p><span style=\"letter-spacing: 0px; color: rgb(51, 51, 51); line-height: 28px; font-family: Helvetica, Arial, sans-serif;\">会议地址：深圳大冲国际中心22楼</span></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 31,
                        "top": 109,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 4,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_07_fddde12686f04169ae742a9bc3587c05.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 317,
                        "height": 172,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 29,
                        "top": 206,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 5,
                    "id": 1638847082311,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_13_aab1035e770b48c8a1fc1aa6ca5d7bbd.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 180,
                        "height": 42,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 99,
                        "top": 10,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 6,
                    "id": 1639623092887,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 427,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(93, 196, 177, 1)",
                "backgroundImage": ""
            },
            "sort": "4",
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638847043047,
            "name": "自定义布局",
            "key": "auto-container",
            "type": "container",
            "typeValue": "auto",
            "components": [
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_08_63d58035fc504d41a95fe836bb73a13e.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 375,
                        "height": 43.62666666666666,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 0,
                        "top": 0,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 0,
                    "id": 1638847343078,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "name": "图片",
                    "type": "image",
                    "images": [
                        {
                            "url": "https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202112_06_29cfb3f63a544bceaea76f1124ce65cb.png&ea=fs",
                            "action": {

                            }
                        }
                    ],
                    "imageGap": 4,
                    "style": {
                        "display": "flex",
                        "width": 410,
                        "height": 64,
                        "paddingBottom": 0,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 0,
                        "borderRadius": 0,
                        "background": "rgba(255, 255, 255, 0)",
                        "backgroundRepeat": "no-repeat",
                        "backgroundSize": "cover",
                        "backgroundPosition": "center center",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": -18,
                        "top": 42,
                        "position": "absolute",
                        "opacity": 100
                    },
                    "sort": 1,
                    "id": 1639623235717,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1639623309751,
                    "name": "文本",
                    "type": "text",
                    "value": "<p style=\"text-align: center;\"><strong style=\"color: rgb(41, 178, 171); font-size: 18px; line-height: 24px; letter-spacing: 0px; font-family: Helvetica, Arial, sans-serif;\">提交以下信息立即报名</strong></p>",
                    "style": {
                        "paddingBottom": 6,
                        "paddingLeft": 0,
                        "paddingRight": 0,
                        "paddingTop": 6,
                        "background": "rgba(255, 255, 255, 0)",
                        "fontSize": 14,
                        "borderWidth": 0,
                        "borderRadius": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "left": 14,
                        "top": 32,
                        "width": 345,
                        "position": "absolute"
                    },
                    "sort": 2,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 80,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "rgba(93, 196, 177, 1)",
                "backgroundImage": ""
            },
            "sort": "5",
            "placeholderStyle": {
                "color": "#cbcccf"
            },
            "inputStyle": {
                "color": "#181c25"
            }
        },
        {
            "id": 1638847361071,
            "name": "表单",
            "key": "form-container",
            "type": "container",
            "typeValue": "form",
            "components": [
                {
                    "id": "1638777449356",
                    "label": "提交",
                    "name": "提交",
                    "tip": "提交成功",
                    "type": "button",
                    "position": "none",
                    "required": false,
                    "isFormComp": true,
                    "wrapStyle": {
                        "position": "none",
                        "background": "rgba(255,255,255,.9)"
                    },
                    "style": {
                        "height": 45,
                        "width": 345,
                        "fontSize": 16,
                        "background": "rgba(41, 178, 171, 1)",
                        "borderRadius": 6,
                        "color": "#fff",
                        "letterSpacing": 0,
                        "lineHeight": 45,
                        "textAlign": "center",
                        "margin": "0 auto",
                        "borderWidth": 0,
                        "borderStyle": "none",
                        "borderColor": "#e9edf5",
                        "position": "absolute",
                        "left": 15,
                        "top": 356,
                        "opacity": 100
                    },
                    "sort": 0,
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "color": "#181c25"
                    }
                },
                {
                    "id": 1638847410569,
                    "label": "姓名",
                    "name": "姓名",
                    "title": "姓名",
                    "type": "input",
                    "typeValue": "text",
                    "fieldName": "name",
                    "defaultValueOpen": false,
                    "defaultValue": "",
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "placeholder": "请输入姓名",
                    "isFormComp": true,
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 18,
                        "position": "absolute"
                    },
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 1
                },
                {
                    "id": 1638847422081,
                    "label": "手机号",
                    "name": "手机号",
                    "title": "手机号",
                    "type": "input",
                    "typeValue": "number",
                    "fieldName": "phone",
                    "pattern": "^1[0-9]\\d{9}$",
                    "defaultValue": "",
                    "defaultValueOpen": false,
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "verify": true,
                    "weChatAuthorizationButton": false,
                    "placeholder": "请输入手机号",
                    "isFormComp": true,
                    "weChatAuthorizationButtonStyle": {
                        "color": "#fff",
                        "background": "#09BB07",
                        "fontSize": 14,
                        "borderStyle": "solid",
                        "borderWidth": 0,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5"
                    },
                    "verifyButtonStyle": {
                        "color": "rgba(41, 178, 171, 1)",
                        "background": "#ffffff",
                        "fontSize": 14,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "rgba(41, 178, 171, 1)"
                    },
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 199,
                        "position": "absolute"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 2
                },
                {
                    "id": 1638847424113,
                    "label": "邮箱",
                    "name": "邮箱",
                    "title": "邮箱",
                    "type": "input",
                    "typeValue": "text",
                    "fieldName": "email",
                    "pattern": "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$",
                    "defaultValue": "",
                    "defaultValueOpen": false,
                    "globalCacheField": "",
                    "defaultValueType": "manual",
                    "required": true,
                    "placeholder": "请输入邮箱",
                    "isFormComp": true,
                    "titleStyle": {
                        "color": "#181C25",
                        "fontSize": 14,
                        "lineHeight": 16,
                        "paddingBottom": 6,
                        "paddingTop": 6,
                        "whiteSpace": "normal"
                    },
                    "style": {
                        "color": "#181C25",
                        "width": 345,
                        "fontSize": 14,
                        "paddingBottom": 0,
                        "paddingTop": 0,
                        "paddingLeft": 12,
                        "paddingRight": 12,
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "borderRadius": 3,
                        "borderColor": "#e9edf5",
                        "left": 15,
                        "top": 111,
                        "position": "absolute"
                    },
                    "placeholderStyle": {
                        "color": "#cbcccf"
                    },
                    "inputStyle": {
                        "height": 45,
                        "color": "#181c25",
                        "background": "#fff"
                    },
                    "sort": 3
                }
            ],
            "current": 0,
            "slideIndex": 0,
            "layout": "single",
            "fillType": "color",
            "fillMethod": "filling",
            "typesetting": "absolute",
            "style": {
                "width": 375,
                "height": 412,
                "overflow": "hidden",
                "position": "relative",
                "backgroundColor": "",
                "backgroundImage": ""
            },
            "sort": 6
        }
    ],
    "language": "zh-CN"
}
export default tpl;