# 微页面Agent需求文档

通过「用户输入」识别用户网页制作意图并引导用户补充内容后执行微页面制作流程

## 1. 功能目标

- [x] 识别用户输入的网页类型和目标
- [x] 在信息不全时，智能识别缺失部分并通过对话方式引导用户补充
- [x] 结合「用户输入」和「补充信息」执行微页面制作流程
  - 微页面流程第一步：根据用户输入规划完整的页面需求和明确网页内容结构
  - 微页面流程第二步：设计网页UI和视觉风格
  - 微页面流程第三步：根据微页面低代码平台`src/components/Hexagon/config/components.js`文件中组件定义（`config`字段下的内容）组装页面数据结构和组件布局，无需将组件定义的数据全部组合进来，只需要可用于识别组件类型的唯一字段即可，如：`type`、`fieldName`、`name`和`typeValue`等
  - 微页面流程第四步：完善各组件内容，如文本组件的富文本内容
  - 微页面流程第五步：优化各组件的样式设计和UI，完善组件的style样式属性
- [x] 根据微页面制作流程返回的微页面粗略结构再结合`src/components/Hexagon/config/components.js`文件中组件定义`config`字段下的内容进行合并（重点是字段补齐和样式字段合并）后输出完整的数据结构

## 技术实现
- 直接通过调用大模型的开放API接口通过代码编排调用流程实现，具体可参考文件`src/components/AiGenerator/core/BaseGenerator.js`内的调用方式
- `messages`会话列表做一个调整，列表项增加一个`type`字段用来决定消息列表渲染的类型，默认值是`default`，如果值是`flow`是则显示流程卡片，如微`页面制作流程`

## 2. UI设计
- 整体交互采用LLM对话交互的方式
- `微页面制作流程`采用一个卡片的方式展示，整体按上下分为三段式布局，顶部标题区域、中间执行任务列表项和底部执行成功或失败文案描述区域
  - 卡片布局示意描述：标题为`微页面制作流程`，列表项为微页面的1～5的执行步骤名称，每一项可以点击展开执行的结果，默认收起
  - 列表每一项前面有执行结果反馈图标，如：`执行失败`、`执行中`、`执行成功`，后面有执行耗时，如`1.2s`

### Agent会话流程示例

```
┌─────────────────────────────────────────────┐
│ 👤 用户                                    │
├─────────────────────────────────────────────┤
│ 我想制作一个产品展示页面，展示我们的新款    │
│ 智能手表，需要包含产品图片、价格和功能特点  │
└─────────────────────────────────────────────┘

┌─────────────────────────────────────────────┐
│ 🤖 Agent                                   │
├─────────────────────────────────────────────┤
│ 好的，我理解您想要制作一个智能手表的产品    │
│ 展示页面。为了更好地完成页面制作，我需要   │
│ 补充一些信息：                             │
│                                             │
│ 1. 产品图片是否已经准备好？                │
│ 2. 产品的具体价格是多少？                  │
│ 3. 需要重点展示哪些功能特点？              │
└─────────────────────────────────────────────┘

┌─────────────────────────────────────────────┐
│ 👤 用户                                    │
├─────────────────────────────────────────────┤
│ 产品图片已经准备好了，价格是1999元，主要    │
│ 功能包括：心率监测、睡眠分析、运动追踪     │
└─────────────────────────────────────────────┘


┌─────────────────────────────────────────────┐
│ 微页面制作流程                              │
├─────────────────────────────────────────────┤
│ ✅ 规划完整的页面需求和明确网页内容结构    1.2s   │
│ 🔄 设计网页UI和视觉风格              2.5s   │
│ ⏳ 组件结构组装                      0.0s   │
│ ⏳ 完善各组件内容                    0.0s   │
│ ⏳ 优化组件样式设计                  0.0s   │
├─────────────────────────────────────────────┤
│ 执行中... 已完成1/5个步骤                  │
└─────────────────────────────────────────────┘
```
说明：
- ❌ 执行失败：红色错误图标
- 🔄 执行中：蓝色加载图标
- ✅ 执行成功：绿色对勾图标
- ⏳ 等待执行：灰色时钟图标