<template>
  <div :class="$style['site-agent-chat']">
    <ChatLLM
      ref="baseChat"
      :placeholder="placeholder"
      :initial-messages="initialMessages"
      :workflow-steps="workflowSteps"
      :system-prompt="defaultSystemPrompt"
      :confirm-phrases="confirmPhrases"
      :auto-scroll="true"
      :enable-typing-effect="true"
      :api-config="apiConfig"
      @workflow-start="handleWorkflowStart"
      @workflow-complete="handleWorkflowComplete"
      @workflow-error="handleWorkflowError"
      @output-data="handleOutputData"
      @onAgentMessage="_handleAgentMessage"
    />
  </div>
</template>

<script>
import ChatLLM from '../../conponents/BaseAgent/components/ChatLLM.vue'
import http from '@/services/http'
import { AI_CONFIG } from '@/components/AiGenerator/config'
import guidePrompt from '../prompt/guide'
import design from '../prompt/design'
import prdPrompt from '../prompt/prd'
import makePrompt from '../prompt/make'
import imageGenPrompt from '../prompt/imageGen'
// import contentPrompt from '../prompt/content'
import { getDefaultConfigByType } from '@/components/Hexagon/config/components'

export default {
  name: 'SiteAgentChat',
  components: {
    ChatLLM
  },
  data() {
    return {
      marketingImages: [],
      activityInfo: {},
      placeholder: '请告诉我您想要创建什么类型的微页面...',
      initialMessages: [],
      pageData: null,
      apiConfig: {
        endpoint: AI_CONFIG.api.endpoint,
        model: AI_CONFIG.api.defaultModel,
        temperature: 0.7,
        maxTokens: 4096
      },
      // 微页面特有的工作流步骤 - 按照最新的flow方式配置
      workflowSteps: [
        {
          name: '需求分析与补充',
          shortName: '需求分析',
          description: '分析用户原始需求，结合最佳实践引导用户补充关键内容',
          prompt: guidePrompt,
          actionType: 'guide',
          output: {
            type: 'text',
            fieldName: 'input'
          }
        },
        {
          name: '微页面创作流程',
          shortName: '页面创作',
          description: '创作微页面',
          actionType: 'flow',
          actionList: [
            {
              name: '编写需求',
              shortName: '编写需求',
              description: '根据用户输入规划完整的页面需求和明确网页内容结构',
              prompt: prdPrompt,
              output: {
                type: 'text',
                fieldName: 'prd'
              }
            },
            {
              name: 'UI设计',
              shortName: 'UI设计',
              description: '设计网页UI和视觉风格',
              prompt: design,
              output: {
                type: 'text',
                fieldName: 'design'
              }
            },
            {
              name: '生成配图',
              shortName: '配图生成',
              description: '根据页面内容生成配图',
              actionType: 'imageGen',
              prompt: imageGenPrompt,
              output: {
                type: 'text',
                fieldName: 'images'
              }
            },
            // {
            //   name: '生成页面内容',
            //   shortName: '内容制作',
            //   description: '完善各组件内容，如文本组件的富文本内容',
            //   prompt: contentPrompt
            // },
            {
              name: '生成页面结构',
              shortName: '生成页面结构',
              description: '根据微页面低代码平台组件定义组装页面数据结构和组件布局',
              prompt: makePrompt,
              output: {
                type: 'json',
                fieldName: 'pageData'
              },
              validator(res = {}) {
                //页面id不存在直接报错
                if (!res.id) {
                  return false
                } else if (!res.components) {
                  return false
                }
                return true
              }
            },
            {
              name: '完成制作',
              shortName: '完成制作',
              description: '完成制作，输出页面数据结构',
              actionType: 'done',
            },
            // {
            //   name: '样式优化',
            //   shortName: '样式优化',
            //   description: '优化各组件的样式设计和UI，完善组件的style样式属性',
            //   type: 'auto',
            //   prompt: '请优化各组件的样式设计和UI，完善组件的style样式属性。调整组件的颜色、字体、间距、边框等样式属性，提升页面的美观度和用户体验。'
            // },
            // {
            //   name: '检查页面输出是否正确',
            //   shortName: '输出结果',
            //   description: '输出最终页面数据结构，供前端调用微页面设计器方法渲染',
            //   type: 'manus',
            //   prompt: '检查输出最终页面数据结构，供前端调用微页面设计器方法渲染。确保输出的JSON格式正确，包含所有必要的组件信息和样式属性。'
            // }
          ]
        }
      ],
      // 微页面特有的系统提示词
      defaultSystemPrompt: '',
      // 微页面特有的欢迎消息
      welcomeMessage: '欢迎使用微页面设计助手！我可以帮您创建精美的微页面。\n\n请告诉我您想要创建什么类型的页面？例如：\n- 产品介绍页\n- 活动宣传页\n- 表单收集页\n- 企业介绍页\n\n请尽可能详细地描述您的需求，包括页面目的、主要内容、风格偏好等。',
      // 确认短语 - 用于识别用户确认
      confirmPhrases: ['是', '好', '确认', '继续', '可以', '同意', '开始', '执行', '没问题', '行', '对', 'yes', 'ok', 'sure', 'confirm']
    };
  },
  created() {
    
    // 初始化系统提示词
    this.initSystemPrompt()

    // 初始化欢迎消息
    this.initialMessages = [
      {
        role: 'assistant',
        content: this.welcomeMessage
      }
    ]

    // 获取营销图片和活动信息
    this.getMarketingImages()
    this.getActivityInfo()

    // 添加Agent就绪监听器
    this.$context.addAgentReadyListener(() => {
      console.log('SiteAgent ready')
    })
  },
  methods: {
    mergeComponents(components) {
      return components.map(item => {
        //button组件不做合并
        if(item.type === 'button') {
          return item
        }
        const matchKeys = {
          type: item.type,//只有type是必须的，其他按需添加
        }
        if(item.typeValue) {
          matchKeys.typeValue = item.typeValue
        }
        if(item.key) {
          matchKeys.key = item.key
        }
        if(item.fieldName) {
          matchKeys.fieldName = item.fieldName
        }
        const defaultConfig = getDefaultConfigByType(matchKeys)
        if(defaultConfig) {
          // 深度合并整个对象
          const mergedItem = this.deepMerge(defaultConfig, item)
          
          // 特殊处理样式对象，确保深度合并
          if('style' in defaultConfig && mergedItem.style) {
            mergedItem.style = this.deepMerge(defaultConfig.style, mergedItem.style)
          }
          
          if('titleStyle' in defaultConfig && mergedItem.titleStyle) {
            mergedItem.titleStyle = this.deepMerge(defaultConfig.titleStyle, mergedItem.titleStyle)
          }
          
          if('placeholderStyle' in defaultConfig && mergedItem.placeholderStyle) {
            mergedItem.placeholderStyle = this.deepMerge(defaultConfig.placeholderStyle, mergedItem.placeholderStyle)
          }
          
          if('inputStyle' in defaultConfig && mergedItem.inputStyle) {
            mergedItem.inputStyle = this.deepMerge(defaultConfig.inputStyle, mergedItem.inputStyle)
          }

          //container组件的style不允许设置内外边距，不可使用padding、margin等属性
          if(mergedItem.type === 'container') {
            delete mergedItem.style.padding
            delete mergedItem.style.margin
            delete mergedItem.style.paddingLeft
            delete mergedItem.style.paddingRight
            delete mergedItem.style.paddingTop
            delete mergedItem.style.paddingBottom
          }

          // 递归处理子组件
          if(mergedItem.components) {
            mergedItem.components = this.mergeComponents(mergedItem.components)
          }

          return mergedItem
        }
        return item
      })
    },

    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} - 合并后的对象
     */
    deepMerge(target, source) {
      const result = { ...target }
      
      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            // 如果是对象，递归合并
            result[key] = this.deepMerge(result[key] || {}, source[key])
          } else if (Array.isArray(source[key])) {
            // 如果是数组，直接替换
            result[key] = [...source[key]]
          } else if (source[key] !== undefined) {
            // 如果是基本类型且不是undefined，则使用source的值
            result[key] = source[key]
          }
        }
      }
      
      return result
    },
    _handleAgentMessage(data) {
      console.log('handleAgentMessage - 原始数据:', data);
      
      // 深度克隆数据以避免修改原始对象
      const processedData = JSON.parse(JSON.stringify(data.result))
      
      // 处理组件数据
      if(processedData.components) {
        processedData.components = this.mergeComponents(processedData.components)
      }
      
      console.log('handleAgentMessage - 处理后数据:', processedData);
      
      //llm返回的页面组件数据结构，有些是经过摘要的，需要递归所有组件，与组件默认数据进行一次merge合并操作，保证组件数据完整性
      this.$emit('onAgentMessage', {
        type: data.type,
        result: processedData,
      });
    },
    /**
     * 初始化系统提示词
     */
    initSystemPrompt() {
      this.defaultSystemPrompt = `你是一个专业的网页设计助手，擅长创建微页面。你的任务是根据用户需求创建微页面。

工作流程分为两个主要阶段：

第一阶段：需求分析与补充
- 详细了解用户对微页面的需求，包括页面目的、主要内容、风格偏好等
- 如果用户提供的信息不足，引导用户补充必要的细节
- 确保获取足够的信息后，请明确告知用户当前步骤已完成，并等待用户确认继续

第二阶段：微页面制作流程（自动执行）
1. 页面规划：确定页面的主要板块、内容组织方式和信息层次
2. UI设计：确定页面的整体色调、排版风格、视觉元素等
3. 组件布局：选择合适的组件类型，确定组件的位置和大小
4. 内容完善：为每个组件填充具体的内容，包括文字、图片、按钮文案等
5. 样式优化：调整组件的颜色、字体、间距、边框等样式属性
6. 输出结果：生成最终页面数据结构，确保JSON格式正确

在第一阶段结束时，请明确告知用户需求分析已完成，并等待用户回复确认后再自动执行后续步骤。用户只需要回复"确认"、"继续"等类似词语即可触发后续自动执行流程。`
    },

    /**
     * 处理工作流启动事件
     * @param {Object} data - 事件数据
     */
    handleWorkflowStart(data) {
      console.log('Workflow started:', data)
      // 更新系统提示词，加入活动信息和图片库
      this.updateSystemPrompt()

      // 触发工作流启动事件
      this.$emit('onWorkflowStart', data)
    },

    /**
     * 处理工作流完成事件
     * @param {Object} data - 事件数据
     */
    handleWorkflowComplete(data) {
      console.log('Workflow completed:', data)

      // 触发页面生成完成事件
      this.$emit('onPageGenerationComplete', this.pageData)
    },

    /**
     * 处理工作流错误事件
     * @param {Object} data - 事件数据
     */
    handleWorkflowError(data) {
      console.error('Workflow error:', data)

      // 触发错误事件
      this.$emit('onError', data)
    },

    /**
     * 处理输出数据事件
     * @param {Object} data - 事件数据
     */
    handleOutputData(data) {
      console.log('Output data:', data)

      // 存储页面数据
      this.pageData = data.data

      // 触发页面数据生成事件
      this.$emit('onPageDataGenerated', this.pageData)
    },

    /**
     * 解析页面数据
     * 这是SiteAgent特有的数据解析逻辑
     */
    parsePageData(content) {
      // 尝试从内容中提取JSON
      const jsonRegex = /```json\s*([\s\S]*?)\s*```|(\{[\s\S]*"type"\s*:\s*"page"[\s\S]*\})/g
      const matches = [...content.matchAll(jsonRegex)]

      if (matches.length > 0) {
        const jsonStr = matches[0][1] || matches[0][2]
        try {
          const pageData = JSON.parse(jsonStr.trim())
          return pageData
        } catch (error) {
          console.error('解析JSON失败:', error)
          return null
        }
      }

      return null
    },

    /**
     * 更新系统提示词，加入活动信息和图片库
     */
    updateSystemPrompt() {
      if (!this.$refs.baseChat) return

      let customPrompt = this.defaultSystemPrompt

      // 添加活动信息
      if (Object.keys(this.activityInfo).length > 0) {
        customPrompt += `\n\n当前相关活动信息，请在生成页面时参考使用：\n${JSON.stringify(this.activityInfo, null, 2)}`
      }

      // 添加图片库信息
      if (this.marketingImages.length > 0) {
        customPrompt += `\n\n可用的营销图片库，请在生成页面时参考使用：\n${this.marketingImages.map(item => `图片地址：${item.url}，图片关键词：${item.keyword}`).join('\n')}`
      }

      // 添加品牌色信息
      const brandColor = this.getBrandColors()
      if (brandColor) {
        customPrompt += `\n\n品牌主色调：${brandColor}，请在设计中适当使用此颜色。`
      }

      // 更新系统提示词
      this.defaultSystemPrompt = customPrompt

      // 更新API配置
      if (this.$refs.baseChat) {
        this.$refs.baseChat.systemPrompt = customPrompt
      }
    },

    /**
     * 设置变量到Agent
     */
    setVariables() {
      if (this.$context.agent) {
        this.$context.agent.sendConfigData({
          variables: [
            {
              name: 'activityInfo',
              value: JSON.stringify(this.activityInfo)
            },
            {
              name: 'imageGallery',
              value: JSON.stringify(this.marketingImages)
            },
            {
              name: 'pageData',
              value: JSON.stringify(this.pageData || {})
            },
            {
              name: 'brandColor',
              value: this.getBrandColors()
            },
          ]
        })
      }
    },

    /**
     * 获取品牌色
     */
    getBrandColors() {
      // 获取公司品牌色
      const style = $('html').attr('style');
      const brandColors = {
        primary01: style.match(/--color-primary01: (#[0-9a-fA-F]{6});/)?.[1] || '#fffaf0',
        primary02: style.match(/--color-primary02: (#[0-9a-fA-F]{6});/)?.[1] || '#ffeecc',
        primary03: style.match(/--color-primary03: (#[0-9a-fA-F]{6});/)?.[1] || '#ffdda3',
        primary04: style.match(/--color-primary04: (#[0-9a-fA-F]{6});/)?.[1] || '#ffca7a',
        primary05: style.match(/--color-primary05: (#[0-9a-fA-F]{6});/)?.[1] || '#ffb452',
        primary06: style.match(/--color-primary06: (#[0-9a-fA-F]{6});/)?.[1] || '#ff9b29',
        primary07: style.match(/--color-primary07: (#[0-9a-fA-F]{6});/)?.[1] || '#d97818',
        primary08: style.match(/--color-primary08: (#[0-9a-fA-F]{6});/)?.[1] || '#b3590b',
        primary09: style.match(/--color-primary09: (#[0-9a-fA-F]{6});/)?.[1] || '#8c3e01',
        primary10: style.match(/--color-primary10: (#[0-9a-fA-F]{6});/)?.[1] || '#662900'
      };
      return brandColors['primary06'];
    },

    /**
     * 获取营销图片库最新的图片
     */
    async getMarketingImages() {
      const params = {
        groupId: -1,
        pageNum: 1,
        pageSize: 30,
        time: 0,
      }
      const res = await http.listPhotoByGroup(params);
      if (res.errCode === 0) {
        // 将返回图片的地址和关键词存储，再制作页面时添加到系统提示词中
        this.marketingImages = res.data.result.reduce((acc, item) => {
          if (item.photoName) {
            acc.push({
              url: item.cdnUrl || item.thumbnailUrl || item.url || '',
              keyword: item.photoName || ''
            });
          }
          return acc;
        }, []);
        this.setVariables();
        this.updateSystemPrompt();
      }
    },

    /**
     * 活动下新建微页面场景，将当前活动的有关信息获取
     */
    async getActivityInfo() {
      const { liveId, conferenceId } = this.$route.query;
      if (liveId) {
        http.queryLiveDetail({
          id: liveId
        }).then((res) => {
          if (res && res.errCode === 0 && res.data) {
            const data = res.data;
            this.activityInfo = {
              title: data.title,
              startTime: data.startTime,
              endTime: data.endTime,
              details: data.desc, // 直播描述
              coverImageUrl: data.coverTaPath,
              type: '直播营销',
              viewUrl: data.viewUrl, // 观看链接
              lecturerName: data.lectureUserName, // 讲师名称
              shareImageUrl: data.sharePicOrdinaryUrl, // 分享图片
              marketingEventId: data.marketingEventId, // 营销活动ID
              livePlatform: data.livePlatform, // 直播平台
              otherPlatformLiveUrl: data.otherPlatformLiveUrl // 其他平台直播链接
            };
            this.setVariables();
            this.updateSystemPrompt();
          }
        });
      } else if (conferenceId) {
        http.queryConferenceDetail({
          id: conferenceId
        }).then((res) => {
          if (res && res.errCode === 0 && res.data) {
            const data = res.data;
            this.activityInfo = {
              title: data.title,
              startTime: data.startTime,
              endTime: data.endTime,
              location: data.location,
              details: data.conferenceDetails, // 使用 details 字段存储 HTML 内容
              coverImageUrl: data.coverImageUrl,
              enrollEndTime: data.enrollEndTime,
              formId: data.formId,
              formName: data.formName,
              type: data.marketingEventDetail?.marketingEventType, // 从嵌套对象获取类型
              qrUrl: data.qrUrl,
              shareImageUrl: data.sharePicOrdinaryUrl // 选择一个默认的分享图
            };
            this.setVariables();
            this.updateSystemPrompt();
          }
        });
      }
    },

    /**
     * 重置对话
     */
    resetConversation() {
      if (this.$refs.baseChat) {
        // 重置组件内部状态
        this.$refs.baseChat.messages = [];
        this.$refs.baseChat.conversationHistory = [];
        this.$refs.baseChat.currentStep = 0;
        this.$refs.baseChat.workflowStarted = false;
        this.$refs.baseChat.workflowCompleted = false;
        this.$refs.baseChat.workflowError = false;
        this.$refs.baseChat.outputData = null;

        // 添加初始消息
        if (this.initialMessages.length > 0) {
          this.initialMessages.forEach(msg => {
            this.$refs.baseChat.addMessage(msg);
          });
        }

        // 触发重置事件
        this.$emit('onReset');
      }

      // 重置本地数据
      this.pageData = null;
    },

    /**
     * 获取当前生成的页面数据
     */
    getPageData() {
      // 如果已有本地缓存的页面数据，则直接返回
      if (this.pageData) {
        return this.pageData;
      }

      // 否则尝试从基础组件获取
      if (this.$refs.baseChat && this.$refs.baseChat.outputData) {
        this.pageData = this.$refs.baseChat.outputData;
        return this.pageData;
      }

      return null;
    },

    /**
     * 发送消息前的处理钩子
     * @param {string} message - 用户输入的消息
     * @returns {Promise<string>} - 处理后的消息
     */
    async beforeSendMessage(message) {
      console.log('处理消息前:', message);
      // 这里可以添加消息预处理逻辑，例如添加上下文信息
      return message;
    },

    /**
     * 发送消息后的处理钩子
     * @param {string} message - 用户输入的消息
     * @param {string} response - AI的响应
     */
    async afterSendMessage(message, response) {
      console.log('处理消息后:', { message, responseLength: response.length });
      // 这里可以添加响应后处理逻辑，例如分析响应内容
    },

    /**
     * 响应完成后的处理钩子
     * @param {string} response - 完整的响应内容
     */
    async onResponseComplete(response) {
      // 尝试从响应中提取关键信息
      if (response.includes('页面类型') || response.includes('页面风格')) {
        console.log('检测到页面规划信息');
      }
    },

    /**
     * 步骤变化时的处理钩子
     * @param {number} prevStep - 前一个步骤索引
     * @param {number} currentStep - 当前步骤索引
     */
    async onStepChange(prevStep, currentStep) {
      console.log(`步骤变化: ${prevStep} -> ${currentStep}`);

      // 根据不同步骤执行特定逻辑
      if (currentStep === 1) { // 进入微页面制作流程
        // 可以在这里加载额外的资源或配置
        console.log('开始微页面制作流程');
      }
    },

    /**
     * 重置对话时的处理钩子
     */
    async onReset() {
      console.log('对话已重置');
      this.pageData = null;
    }
  }
}
</script>

<style lang="less" module>
.site-agent-chat {
  height: 100%;
  width: 100%;
}
</style>
