<script>
import BaseAgent from '../conponents/BaseAgent/llm.vue'
import Content from './components/Content.vue'
import Chat from './components/Chat.vue'
export default {
  name: 'SiteAgent',
  components: {
    Content,
    Chat,
  },
  extends: BaseAgent,
  data() {
    return {
      agentApiName: 'Copilot_hexagon_ai__c',
    }
  },
  methods: {
    getCrumbs() {
      return [
        {
          text: 'Vibe Marketing',
          to: { name: 'vibe-marketing-init' },
        },
        {
          text: '策划',
          to: { name: 'vibe-marketing-plan' },
        },
        {
          text: '微页面制作',
          to: false,
        },
      ]
    },
    onAgentMessage(data) {
      console.log('handleAgentMessage', data);
      try {
        // if(!data.message.finish) {
        //   return;
        // }
        if(typeof data === 'object'){
          console.log('pageData object:', data);
          if(data.type === 'update'){ 
            this.$context.$content.setPageData(data.result);
          } else if(data.type === 'create'){
            // 创建页面
            this.$context.$content.createEmptyPage().then(page => {
              setTimeout(() => {
                this.$context.$content.setPageData(data.result);
              }, 500);
            });
          } else {
            console.error('handleAgentMessage error', data);
          }
        } else {
          //移除\n、\t
          const content = data.message.content.replace(/\n/g, '').replace(/\t/g, '');
          const pageData = JSON.parse(content);
          console.log('pageData', pageData);
          this.$context.$content.setPageData(pageData.page ? pageData.page : pageData);
        }
        
      } catch (error) {
        console.error('handleAgentMessage error', error);
      }
    },
    onAgentReady() {
      console.log('onAgentReady', this.$context, this.$context.$content.getPageData());
    }
  }
}
</script>