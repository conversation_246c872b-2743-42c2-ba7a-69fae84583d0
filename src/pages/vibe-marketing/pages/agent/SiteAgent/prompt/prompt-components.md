# 组件定义文档

## 0. 样式定义

### 0.1 基础样式
```json
{
  "baseStyle": {
    "paddingBottom": 6,
    "paddingLeft": 12,
    "paddingRight": 12,
    "paddingTop": 6,
    "background": "rgba(255, 255, 255, 0)",
    "borderWidth": 0,
    "borderRadius": 0,
    "borderStyle": "none",
    "borderColor": "#e9edf5"
  },
  "textStyle": {
    "fontSize": 14,
    "color": "#181C25",
    "lineHeight": 20
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  },
  "formItemStyle": {
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "buttonStyle": {
    "height": 45,
    "width": 345,
    "fontSize": 16,
    "background": "#409EFF",
    "borderRadius": 6,
    "color": "#fff",
    "letterSpacing": 0,
    "lineHeight": 45,
    "textAlign": "center",
    "marginLeft": "auto",
    "marginRight": "auto",
    "marginTop": 15,
    "marginBottom": 15
  },
  "containerStyle": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "backgroundColor": "",
    "backgroundImage": ""
  }
}
```

## 1. 基础组件

### 1.1 文本组件
```json
{
  "id": "",
  "name": "文本",
  "type": "text",
  "value": "请输入文本",
  "style": {
    "fontSize": 14
  }
}
```

### 1.2 图片组件
```json
{
  "name": "图片",
  "type": "image",
  "previewEnable": false,
  "images": [{"url": "", "action": {}, "uploadType": "upload"}],
  "imageGap": 4,
  "style": {
    "display": "flex",
    "width": 375,
    "height": 180,
    "backgroundRepeat": "no-repeat",
    "backgroundSize": "cover",
    "backgroundPosition": "center center"
  },
  "filterConfig": {
    "brightness": 100,
    "grayscale": 0,
    "opacity": 100
  }
}
```

### 1.3 视频组件
```json
{
  "id": "",
  "name": "视频",
  "type": "video",
  "layoutType": "video-and-icon",
  "iconMode": "light",
  "iconSize": 72,
  "url": "",
  "cover": "",
  "autoplay": false,
  "loop": false,
  "style": {
    "width": 375,
    "height": 210,
    "background": "#000"
  },
  "wrapStyle": {
    "position": "relative",
    "zIndex": 10,
    "left": 0,
    "top": 0
  }
}
```

### 1.4 幻灯片组件
```json
{
  "id": "",
  "name": "幻灯片",
  "type": "slider",
  "autoplay": true,
  "interval": 3000,
  "duration": 300,
  "indicators": true,
  "indicatorType": 5,
  "indicatorColor": "#999999",
  "indicatorActiveColor": "#FF8000",
  "circlePointSize": 6,
  "circlePointMargin": 5,
  "dashPointWidth": 12,
  "dashPointHeight": 2,
  "dashPointMargin": 4,
  "slidePointWidth": 180,
  "slidePointHeight": 1,
  "indicatorLeft": 187,
  "indicatorBottom": 24,
  "indicatorTop": 150,
  "active": 0,
  "slider": [],
  "style": {
    "width": 375,
    "height": 180,
    "overflow": "hidden"
  }
}
```

### 1.5 矩形组件
```json
{
  "id": "",
  "name": "矩形",
  "type": "rectangle",
  "radiusMode": 1,
  "borderRadiusStyles": {
    "borderRadius": 0,
    "borderTopLeftRadius": 0,
    "borderTopRightRadius": 0,
    "borderBottomLeftRadius": 0,
    "borderBottomRightRadius": 0
  },
  "rectangle": {
    "style": {
      "width": 150,
      "height": 150,
      "backgroundColor": "#d9d9d9",
      "display": "inline-block"
    }
  },
  "style": {
    "width": 150,
    "height": 150,
    "backgroundColor": "#d9d9d9",
    "fontSize": 14,
    "color": "#181C25"
  }
}
```

### 1.6 分割线组件
```json
{
  "id": "",
  "name": "分割线",
  "type": "line",
  "line": {
    "style": {
      "width": 345,
      "borderBottomWidth": 1,
      "borderBottomColor": "#333",
      "borderBottomStyle": "solid",
      "display": "inline-block"
    }
  },
  "style": {
    "width": 375,
    "textAlign": "center",
    "lineHeight": 0
  }
}
```

### 1.7 辅助留白组件
```json
{
  "id": "",
  "name": "辅助留白",
  "type": "blank",
  "style": {
    "width": 375,
    "height": 50
  }
}
```

## 2. 表单组件

### 2.1 基础表单组件
```json
{
  "id": "",
  "name": "表单",
  "key": "form-container",
  "type": "container",
  "typeValue": "form",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "flow",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative"
  },
  "formLayout": "default"
}
```

### 2.2 分步表单组件
```json
{
  "id": "",
  "name": "分步表单",
  "key": "form-container",
  "type": "container",
  "typeValue": "step-form",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "flow",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative"
  },
  "isStepSubmit": true,
  "formLayout": "default"
}
```

### 2.3 支付表单组件
```json
{
  "id": "",
  "name": "支付表单",
  "key": "form-container",
  "type": "container",
  "typeValue": "order",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "absolute",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "height": 235
  },
  "formLayout": "default"
}
```

### 2.4 输入类组件
#### 2.4.1 姓名组件
```json
{
  "id": "",
  "label": "姓名",
  "name": "姓名",
  "title": "",
  "type": "input",
  "typeValue": "text",
  "fieldName": "name",
  "customFieldName": "",
  "defaultValueOpen": false,
  "defaultValue": "",
  "globalCacheField": "",
  "defaultValueType": "manual",
  "required": true,
  "placeholder": "请输入姓名",
  "isFormComp": true,
  "style": {
    "color": "#181C25",
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.4.2 手机号组件
```json
{
  "id": "",
  "label": "手机号",
  "name": "手机号",
  "title": "",
  "type": "input",
  "typeValue": "number",
  "fieldName": "phone",
  "customFieldName": "",
  "pattern": "^1[0-9]\\d{9}$",
  "defaultValue": "",
  "defaultValueOpen": false,
  "globalCacheField": "",
  "defaultValueType": "manual",
  "required": true,
  "verify": false,
  "enableInternationalCode": false,
  "weChatAuthorizationButton": false,
  "placeholder": "请输入手机号",
  "isFormComp": true,
  "weChatAuthorizationButtonStyle": {
    "color": "#fff",
    "background": "#09BB07",
    "fontSize": 14,
    "borderRadius": 3
  },
  "verifyButtonStyle": {
    "color": "#181C25",
    "background": "#ffffff",
    "fontSize": 14,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5"
  },
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "color": "#181C25",
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.4.3 邮箱组件
```json
{
  "id": "",
  "label": "邮箱",
  "name": "邮箱",
  "title": "",
  "type": "input",
  "typeValue": "text",
  "fieldName": "email",
  "customFieldName": "",
  "pattern": "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$",
  "defaultValue": "",
  "defaultValueOpen": false,
  "globalCacheField": "",
  "defaultValueType": "manual",
  "required": true,
  "placeholder": "请输入邮箱",
  "isFormComp": true,
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "color": "#181C25",
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.4.4 文本域组件
```json
{
  "id": "",
  "label": "文本域",
  "name": "文本域",
  "title": "",
  "type": "textarea",
  "typeValue": "text",
  "fieldName": "textarea",
  "customFieldName": "",
  "defaultValue": "",
  "defaultValueOpen": false,
  "globalCacheField": "",
  "defaultValueType": "manual",
  "required": false,
  "placeholder": "请输入内容",
  "isFormComp": true,
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "color": "#181C25",
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 120,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.4.5 数字组件
```json
{
  "id": "",
  "label": "数字",
  "name": "数字",
  "title": "",
  "type": "input",
  "typeValue": "number",
  "fieldName": "number",
  "customFieldName": "",
  "defaultValue": "",
  "defaultValueOpen": false,
  "globalCacheField": "",
  "defaultValueType": "manual",
  "required": false,
  "placeholder": "请输入数字",
  "isFormComp": true,
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "color": "#181C25",
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.4.6 日期组件
```json
{
  "id": "",
  "label": "日期",
  "name": "日期",
  "title": "",
  "type": "date",
  "typeValue": "date",
  "fieldName": "date",
  "customFieldName": "",
  "defaultValue": "",
  "defaultValueOpen": false,
  "globalCacheField": "",
  "defaultValueType": "manual",
  "required": false,
  "placeholder": "请选择日期",
  "isFormComp": true,
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "color": "#181C25",
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.4.7 时间组件
```json
{
  "id": "",
  "label": "时间",
  "name": "时间",
  "title": "",
  "type": "time",
  "typeValue": "time",
  "fieldName": "time",
  "customFieldName": "",
  "defaultValue": "",
  "defaultValueOpen": false,
  "globalCacheField": "",
  "defaultValueType": "manual",
  "required": false,
  "placeholder": "请选择时间",
  "isFormComp": true,
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "color": "#181C25",
    "width": 345,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingTop": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderRadius": 3,
    "borderColor": "#e9edf5",
    "marginLeft": 15,
    "marginTop": 15
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

### 2.5 选择类组件
#### 2.5.1 单选组件
```json
{
  "id": "",
  "label": "单选",
  "name": "单选",
  "title": "",
  "type": "radio",
  "typeValue": "select_one",
  "layout": "select",
  "fieldName": "text7",
  "customFieldName": "",
  "required": false,
  "placeholder": "请选择",
  "isFormComp": true,
  "otherOption": false,
  "otherOptionValueRequired": false,
  "associatedOpen": false,
  "associatedFieldName": "",
  "associatedFields": [],
  "options": [
    {
      "label": "选项1",
      "value": 1,
      "isDefault": false
    },
    {
      "label": "选项2",
      "value": 2,
      "isDefault": false
    }
  ],
  "components": [],
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "width": 345,
    "color": "#181C25",
    "borderRadius": 3,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "paddingTop": 0,
    "marginLeft": 15,
    "marginTop": 15,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderColor": "#e9edf5"
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.5.2 多选组件
```json
{
  "id": "",
  "label": "多选",
  "name": "多选",
  "title": "多选",
  "type": "checkbox",
  "typeValue": "select_manny",
  "fieldName": "texts1",
  "customFieldName": "",
  "layout": "tile",
  "required": false,
  "placeholder": "请选择",
  "isFormComp": true,
  "otherOption": false,
  "otherOptionValueRequired": false,
  "associatedOpen": false,
  "associatedFieldName": "",
  "associatedFields": [],
  "options": [
    {
      "label": "选项1",
      "value": 1,
      "isDefault": false
    },
    {
      "label": "选项2",
      "value": 2,
      "isDefault": false
    }
  ],
  "components": [],
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "width": 345,
    "borderRadius": 3,
    "fontSize": 14,
    "marginTop": 15,
    "marginLeft": 15,
    "color": "#181C25",
    "lineHeight": 20
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "color": "#181c25",
    "background": "#fff"
  }
}
```

#### 2.5.3 下拉选择组件
```json
{
  "id": "",
  "label": "下拉选择",
  "name": "下拉选择",
  "title": "",
  "type": "select",
  "typeValue": "select",
  "fieldName": "select",
  "customFieldName": "",
  "required": false,
  "placeholder": "请选择",
  "isFormComp": true,
  "options": [
    {
      "label": "选项1",
      "value": 1,
      "isDefault": false
    },
    {
      "label": "选项2",
      "value": 2,
      "isDefault": false
    }
  ],
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "width": 345,
    "color": "#181C25",
    "borderRadius": 3,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "paddingTop": 0,
    "marginLeft": 15,
    "marginTop": 15,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderColor": "#e9edf5"
  },
  "placeholderStyle": {
    "color": "#cbcccf"
  },
  "inputStyle": {
    "height": 45,
    "color": "#181c25",
    "background": "#fff"
  }
}
```

### 2.6 上传类组件
#### 2.6.1 图片上传组件
```json
{
  "id": "",
  "label": "图片上传",
  "name": "图片上传",
  "title": "",
  "type": "upload",
  "typeValue": "image",
  "fieldName": "image",
  "customFieldName": "",
  "required": false,
  "placeholder": "请上传图片",
  "isFormComp": true,
  "maxCount": 9,
  "maxSize": 5,
  "accept": "image/*",
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "width": 345,
    "color": "#181C25",
    "borderRadius": 3,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "paddingTop": 0,
    "marginLeft": 15,
    "marginTop": 15,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderColor": "#e9edf5"
  },
  "uploadStyle": {
    "width": 80,
    "height": 80,
    "borderRadius": 4,
    "borderStyle": "dashed",
    "borderWidth": 1,
    "borderColor": "#d9d9d9",
    "backgroundColor": "#fafafa"
  }
}
```

#### 2.6.2 文件上传组件
```json
{
  "id": "",
  "label": "文件上传",
  "name": "文件上传",
  "title": "",
  "type": "upload",
  "typeValue": "file",
  "fieldName": "file",
  "customFieldName": "",
  "required": false,
  "placeholder": "请上传文件",
  "isFormComp": true,
  "maxCount": 1,
  "maxSize": 10,
  "accept": "*/*",
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "width": 345,
    "color": "#181C25",
    "borderRadius": 3,
    "fontSize": 14,
    "paddingBottom": 0,
    "paddingLeft": 12,
    "paddingRight": 12,
    "paddingTop": 0,
    "marginLeft": 15,
    "marginTop": 15,
    "borderStyle": "solid",
    "borderWidth": 1,
    "borderColor": "#e9edf5"
  },
  "uploadStyle": {
    "width": "100%",
    "height": 45,
    "borderRadius": 4,
    "borderStyle": "dashed",
    "borderWidth": 1,
    "borderColor": "#d9d9d9",
    "backgroundColor": "#fafafa"
  }
}
```

### 2.7 其他表单组件
#### 2.7.1 评分组件
```json
{
  "id": "",
  "label": "评分",
  "name": "评分",
  "title": "",
  "type": "rate",
  "typeValue": "rate",
  "fieldName": "rate",
  "customFieldName": "",
  "required": false,
  "isFormComp": true,
  "count": 5,
  "allowHalf": false,
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "width": 345,
    "color": "#181C25",
    "fontSize": 14,
    "marginLeft": 15,
    "marginTop": 15
  },
  "rateStyle": {
    "color": "#faad14",
    "fontSize": 20
  }
}
```

#### 2.7.2 开关组件
```json
{
  "id": "",
  "label": "开关",
  "name": "开关",
  "title": "",
  "type": "switch",
  "typeValue": "switch",
  "fieldName": "switch",
  "customFieldName": "",
  "required": false,
  "isFormComp": true,
  "titleStyle": {
    "color": "#181C25",
    "fontSize": 14,
    "lineHeight": 16,
    "paddingBottom": 6,
    "paddingTop": 6,
    "whiteSpace": "normal"
  },
  "style": {
    "width": 345,
    "color": "#181C25",
    "fontSize": 14,
    "marginLeft": 15,
    "marginTop": 15
  },
  "switchStyle": {
    "checkedColor": "#409EFF",
    "uncheckedColor": "#dcdfe6"
  }
}
```

## 3. 布局组件

### 3.1 自定义布局组件
```json
{
  "id": "",
  "name": "自定义布局",
  "key": "auto-container",
  "type": "container",
  "typeValue": "auto",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "absolute",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "backgroundColor": "",
    "backgroundImage": "",
    "height": 235
  },
  "isVisual": true,
  "visual": false
}
```

### 3.2 导航布局组件
```json
{
  "id": "",
  "name": "导航布局",
  "key": "tab-container",
  "type": "container",
  "typeValue": "tab-container",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "flow",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "backgroundColor": "",
    "backgroundImage": ""
  },
  "tabLayout": "underline",
  "enableDividingLine": true,
  "dividingLineColor": "#DEE1E8",
  "underlineType": "long-line",
  "capsuleType": "big-round",
  "tabColor": "#181C25",
  "tabCapsuleColor": "#EFEFEF",
  "tabActiveColor": "#F86E30",
  "tabActiveCapsuleColor": "#0C6CFF",
  "tabBackgroundColor": "#FFFFFF",
  "tabHeight": 40,
  "tabFontSize": 16
}
```

### 3.3 栅格布局组件
```json
{
  "id": "",
  "name": "栅格布局",
  "key": "grid",
  "type": "gridcontainer",
  "typeValue": "grid",
  "components": [],
  "fillType": "color",
  "fillMethod": "filling",
  "style": {},
  "typesetting": "grid",
  "gridConfig": {
    "columns": 2,
    "gutter": 10,
    "padding": 15
  }
}
```

### 3.4 列表布局组件
```json
{
  "id": "",
  "name": "列表布局",
  "key": "list-container",
  "type": "container",
  "typeValue": "list",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "flow",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "backgroundColor": "#FFFFFF"
  },
  "listConfig": {
    "itemSpacing": 10,
    "padding": 15,
    "showDivider": true,
    "dividerColor": "#E9EDF5",
    "dividerStyle": "solid",
    "dividerWidth": 1
  }
}
```

### 3.5 卡片布局组件
```json
{
  "id": "",
  "name": "卡片布局",
  "key": "card-container",
  "type": "container",
  "typeValue": "card",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "flow",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "backgroundColor": "#F5F7FA"
  },
  "cardConfig": {
    "itemSpacing": 15,
    "padding": 15,
    "borderRadius": 8,
    "backgroundColor": "#FFFFFF",
    "shadow": "0 2px 8px rgba(0,0,0,0.08)",
    "borderWidth": 0,
    "borderStyle": "solid",
    "borderColor": "#E9EDF5"
  }
}
```

### 3.6 轮播布局组件
```json
{
  "id": "",
  "name": "轮播布局",
  "key": "swiper-container",
  "type": "container",
  "typeValue": "swiper",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "absolute",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "backgroundColor": "#FFFFFF"
  },
  "swiperConfig": {
    "autoplay": true,
    "interval": 3000,
    "duration": 300,
    "circular": true,
    "indicatorDots": true,
    "indicatorColor": "rgba(0, 0, 0, 0.3)",
    "indicatorActiveColor": "#409EFF",
    "indicatorSize": 6,
    "indicatorPosition": "bottom"
  }
}
```

### 3.7 瀑布流布局组件
```json
{
  "id": "",
  "name": "瀑布流布局",
  "key": "waterfall-container",
  "type": "container",
  "typeValue": "waterfall",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "waterfall",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {
    "width": 375,
    "overflow": "hidden",
    "position": "relative",
    "backgroundColor": "#FFFFFF"
  },
  "waterfallConfig": {
    "columns": 2,
    "gutter": 10,
    "padding": 15,
    "loadMore": true,
    "loadMoreText": "加载更多",
    "loadMoreStyle": {
      "color": "#409EFF",
      "fontSize": 14,
      "textAlign": "center",
      "padding": "15px 0"
    }
  }
}
```

## 4. 媒体组件

### 4.1 视频号视频组件
```json
{
  "name": "视频号视频",
  "type": "wechatvideo",
  "coverPageType": "upload",
  "url": "",
  "videoNumId": "",
  "videoId": "",
  "videoName": "视频号",
  "style": {
    "width": 375,
    "height": 360,
    "paddingTop": "10px",
    "paddingBottom": "10px"
  }
}
```

### 4.2 视频号主页组件
```json
{
  "name": "视频号主页",
  "type": "videohomepage",
  "associatedAccountId": "",
  "style": {
    "paddingTop": 15,
    "paddingBottom": 15,
    "paddingLeft": 10,
    "paddingRight": 10
  }
}
```

### 4.3 视频号直播组件
```json
{
  "name": "视频号直播",
  "type": "videolive",
  "associatedAccountId": "",
  "layout": 0,
  "isHide": true,
  "fillType": "color",
  "fillMethod": "filling",
  "wrapStyle": {
    "position": "static",
    "zIndex": 10,
    "left": 265,
    "top": 375
  },
  "style": {
    "paddingTop": 15,
    "paddingBottom": 15,
    "paddingLeft": 10,
    "paddingRight": 10,
    "backgroundSize": "cover",
    "backgroundPosition": "center center",
    "backgroundRepeat": "no-repeat"
  }
}
```

## 5. 营销组件

### 5.1 直播预约组件
```json
{
  "id": "",
  "label": "直播预约按钮",
  "name": "直播预约",
  "tip": "提交成功",
  "type": "signupbutton",
  "position": "none",
  "required": false,
  "isFormComp": false,
  "layoutType": "text-only",
  "textAlign": "center",
  "wrapStyle": {
    "position": "none"
  },
  "style": {
    "height": 45,
    "width": 345,
    "fontSize": 16,
    "background": "#409EFF",
    "borderRadius": 6,
    "color": "#fff",
    "letterSpacing": 0,
    "lineHeight": 45,
    "textAlign": "center",
    "marginLeft": "auto",
    "marginRight": "auto",
    "marginTop": 15,
    "marginBottom": 15
  },
  "typeValue": "live",
  "memberAutoSignup": false,
  "memberAutoSignupButtonStyle": {
    "textAlign": "center",
    "color": "#181c25",
    "fontSize": 16,
    "lineHeight": 45
  },
  "memberAutoSignupButton": true,
  "memberAutoSignupButtonText": "会员登录",
  "member": {},
  "objectId": "",
  "objectTitle": "",
  "objectName": "直播",
  "status": "before",
  "schedule": {
    "before": {
      "yes": {
        "name": "已预约",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      },
      "no": {
        "name": "立即预约",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      }
    },
    "processing": {
      "yes": {
        "name": "观看直播",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      },
      "no": {
        "name": "报名并观看",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      }
    },
    "after": {
      "yes": {
        "name": "观看回放",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      },
      "no": {
        "name": "观看回放",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      }
    }
  },
  "placeholder": "选择直播"
}
```

### 5.2 会议报名组件
```json
{
  "id": "",
  "label": "会议报名按钮",
  "name": "会议报名",
  "tip": "提交成功",
  "type": "signupbutton",
  "position": "none",
  "required": false,
  "isFormComp": false,
  "layoutType": "text-only",
  "textAlign": "center",
  "wrapStyle": {
    "position": "none"
  },
  "style": {
    "height": 45,
    "width": 345,
    "fontSize": 16,
    "background": "#409EFF",
    "borderRadius": 6,
    "color": "#fff",
    "letterSpacing": 0,
    "lineHeight": 45,
    "textAlign": "center",
    "marginLeft": "auto",
    "marginRight": "auto",
    "marginTop": 15,
    "marginBottom": 15
  },
  "typeValue": "meeting",
  "memberAutoSignup": false,
  "memberAutoSignupButtonStyle": {
    "textAlign": "center",
    "color": "#181c25",
    "fontSize": 16,
    "lineHeight": 45
  },
  "memberAutoSignupButton": true,
  "memberAutoSignupButtonText": "会员登录",
  "member": {},
  "objectId": "",
  "objectTitle": "",
  "objectName": "会议",
  "status": "before",
  "schedule": {
    "before": {
      "yes": {
        "name": "已报名",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      },
      "no": {
        "name": "立即报名",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      }
    },
    "processing": {
      "yes": {
        "name": "已报名",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      },
      "no": {
        "name": "立即报名",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      }
    },
    "after": {
      "yes": {
        "name": "精彩回顾",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      },
      "no": {
        "name": "精彩回顾",
        "style": {
          "color": "#fff",
          "fontSize": 16,
          "lineHeight": 45,
          "textAlign": "center",
          "letterSpacing": 0
        },
        "action": {}
      }
    }
  },
  "placeholder": "选择会议"
}
```

## 6. 会员组件

### 6.1 会员信息组件
```json
{
  "id": "",
  "name": "会员信息",
  "key": "",
  "type": "minivipmemberinfo",
  "typeValue": "",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "absolute",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {},
  "styleData": {},
  "settingData": {
    "showIcon": false,
    "showLeague": false,
    "typeOfLeague": 1
  }
}
```

### 6.2 会员等级组件
```json
{
  "id": "",
  "name": "会员等级",
  "key": "",
  "type": "miniviplevel",
  "typeValue": "",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "absolute",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {},
  "styleData": {},
  "settingData": {
    "defaultShow": "join",
    "join": {
      "showOtherPlan": true,
      "switchText": "加入的其他会员计划",
      "LevelText": "成长值",
      "container": {
        "style": {},
        "visualStyle": {
          "visual": false
        }
      }
    },
    "nojoin": {
      "showOtherPlan": true,
      "openText": "开通会员，尽享权益",
      "switchText": "加入的其他会员计划",
      "container": {
        "style": {},
        "visualStyle": {
          "visual": false
        }
      }
    }
  }
}
```

### 6.3 积分明细组件
```json
{
  "id": "",
  "name": "积分明细",
  "key": "",
  "type": "minivippointsdetails",
  "typeValue": "",
  "components": [],
  "current": 0,
  "slideIndex": 0,
  "layout": "single",
  "fillType": "color",
  "fillMethod": "filling",
  "typesetting": "absolute",
  "visualStyle": {
    "overflowX": "hidden",
    "overflowY": "auto",
    "height": 30
  },
  "style": {},
  "styleData": {},
  "typeOfLeague": 1
}
```

## 7. 其他组件

### 7.1 关注公众号组件
```json
{
  "name": "关注公众号",
  "type": "followwechat",
  "url": "",
  "style": {
    "paddingBottom": 10,
    "paddingLeft": 10,
    "paddingRight": 10,
    "paddingTop": 10
  }
}
```

### 7.2 文章组件
```json
{
  "id": "",
  "name": "文章",
  "type": "article",
  "article": {
    "type": "column",
    "title": "文章标题",
    "desc": "文章描述",
    "author": "管理员",
    "time": "2019-11-20",
    "image": "",
    "content": "请输入内容",
    "style": {
      "borderRadius": 0,
      "background": "rgba(0,0,0,0)",
      "borderColor": "rgba(0,0,0,0)",
      "borderWidth": 1,
      "borderStyle": "solid"
    }
  },
  "style": {
    "width": 375,
    "height": "auto"
  }
}
```

### 7.3 文件组件
```json
{
  "name": "文件",
  "type": "document",
  "range": "all",
  "contentObjectType": 8,
  "contentObjectIds": [],
  "items": [],
  "siteGroupId": "-1",
  "hideWhenEmpty": false,
  "enableSearch": false,
  "searchLayout": "small-round",
  "searchPlaceholder": "搜索",
  "searchBackgroundColor": "#F2F3F5",
  "searchBorderColor": "#F2F3F5",
  "searchFontColor": "#181C25",
  "searchPlaceholderFontColor": "#C1C5CE",
  "paginationThemeColor": "#ff9b29",
  "materialTagFilter": {},
  "classification": [],
  "optionLook": true,
  "lookActionConfig": {
    "type": "file",
    "ctaConfig": {}
  },
  "optionDownload": true,
  "downloadActionConfig": {
    "type": "fileDownloadV2",
    "ctaConfig": {}
  },
  "optionSendMail": true,
  "sendMailActionConfig": {
    "type": "sendToEmail",
    "ctaConfig": {}
  },
  "email": {
    "title": "下载文件通知",
    "sender": "",
    "applyUser": "",
    "html": "<div id=\"yxt_tinymce_body_class\"style=\"font-size: 16px; line-height: 1.4; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\"><p style=\"text-align: center;\">您的文件已下发，请点击下方文件名称下载文件</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.name}</a></p><p style=\"text-align: center;\">若无法点击文件名下载，您也可以复制以下链接到浏览器打开下载</p><p style=\"text-align: center;\"><a href=\"$${file.url}\">$${file.url}</a></p></div>"
  },
  "style": {
    "width": 375,
    "height": "auto"
  }
}
```

### 7.4 拨打电话组件
```json
{
  "id": "",
  "label": "电话",
  "name": "拨打电话",
  "type": "tel",
  "value": "",
  "position": "none",
  "typeValue": "mobile",
  "required": false,
  "isFormComp": false,
  "wrapStyle": {
    "position": "none",
    "background": "rgba(255,255,255,.9)",
    "zIndex": 9
  },
  "style": {
    "height": 45,
    "borderRadius": 3,
    "fontSize": 16,
    "background": "#409EFF",
    "color": "#fff",
    "letterSpacing": 0,
    "lineHeight": 45,
    "borderColor": "#333",
    "textAlign": "center"
  }
}
```

### 7.5 微信加粉组件
```json
{
  "id": "",
  "name": "微信加粉",
  "type": "wechat",
  "wechat": {
    "value": "",
    "style": {
      "color": "#fff",
      "background": "rgb(80, 187, 70)"
    }
  },
  "style": {
    "textAlign": "center"
  }
}
```

### 7.6 二维码组件
```json
{
  "name": "二维码",
  "type": "qrcode",
  "typeValue": "",
  "qrcodeType": 0,
  "qrcodeValue": "",
  "url": "",
  "style": {
    "display": "flex",
    "width": 355,
    "height": 180,
    "paddingBottom": 15,
    "paddingLeft": 0,
    "paddingRight": 0,
    "paddingTop": 15
  }
}
```