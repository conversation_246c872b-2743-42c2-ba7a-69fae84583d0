<script>
import BaseAgent from '../conponents/BaseAgent/index.vue'
import Content from './components/Content.vue'
import Chat from './components/Chat.vue'
export default {
  name: 'PosterAgent',
  components: {
    Content,
    Chat,
  },
  extends: BaseAgent,
  data() {
    return {
      agentApiName: 'Copilot_hexagon_ai__c',
    }
  },
  methods: {
    getCrumbs() {
      return [
        {
          text: 'Vibe Marketing',
          to: { name: 'vibe-marketing-init' },
        },
        {
          text: '策划',
          to: { name: 'vibe-marketing-plan' },
        },
        {
          text: '海报制作',
          to: false,
        },
      ]
    },
    onAgentMessage(data) {
      console.log('handleAgentMessage', data);
      try {
        // if(!data.message.finish) {
        //   return;
        // }
        //移除\n、\t
        const content = data.message.content.replace(/\n/g, '').replace(/\t/g, '');
        const pageData = JSON.parse(content);
        console.log('pageData', pageData);
        this.$context.$content.setPageData(pageData.page ? pageData.page : pageData);
      } catch (error) {
        // console.error('handleAgentMessage error', error);
      }
    },
    onAgentReady() {
    }
  }
}
</script>