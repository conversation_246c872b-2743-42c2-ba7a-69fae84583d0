<template>
  <div :class="$style['site-agent-content']" v-loading="loading">
    <div :class="$style['site-agent-content-wrapper']">
      <QrposterCreateContent
       ref="qrposterCreateContent"
       :defaultMarketingEventId="defaultMarketingEventId"
       :defaultMarketingEventName="defaultMarketingEventName"
       :defaultMarketingActivityId="defaultMarketingActivityId"
       :groupId="groupId"
       @created="posterCreated"
       @setLoading="setLoading"
       />
    </div>
    <div :class="$style['site-agent-content-button-wrapper']">
      <fx-button
        :class="$style['site-agent-content-button']"
        size="small"
        @click="handleAiPosterGenerate"
      >
        <img :src="aiLogo" />
        <span>{{ isGenerating ? 'AI生成中...' : 'AI海报生成' }}</span>
      </fx-button>
      <fx-button type="primary" :class="$style['site-agent-content-button']" size="small" @click="handleSave">保存</fx-button>
    </div>
    <AiPosterDialog ref="aiPosterDialog" @select="handleSelectImage" />
  </div>
</template>

<script>
import QrposterCreateContent from '@/components/qrposter-create-dialog/qrposter-create-content.vue'
import AiLogo from '@/assets/images/icons/icon-ai-logo.svg'
import { getTNPath } from '@/utils/files'
import AiPosterDialog from '../../../../components/AiPosterDialog.vue'

export default {
  name: 'SiteAgentContent',
  components: {
    QrposterCreateContent,
    AiPosterDialog,
  },
  data() {
    return {
      defaultMarketingEventId: '',
      defaultMarketingEventName: '',
      defaultMarketingActivityId: '',
      groupId: '',
      aiLogo: AiLogo,
      
      selectedImage: null,
      xhr: null,
      loading: false
    }
  },
  watch: {},
  methods: {
    posterCreated() {
      this.$emit('setLoading', false)
      this.loading = false;
      this.$message({
        type: 'success',
        message: '海报创建成功！'
      });
    },
    setLoading(loading) {
      this.loading = loading
      this.$emit('setLoading', loading)
    },
    handleFormDataUpdate(newFormData) {
      this.formData = { ...newFormData }
    },
    handleSave() {
      if (this.$refs.qrposterCreateContent) {
        this.$refs.qrposterCreateContent.handleSave()
      }
    },
    handleSelectImage(image) {
      if (image && typeof image === 'string' && image.startsWith('data:image')) {
        this.$refs.qrposterCreateContent.handleBackgroundChange(image, {}, false)
      } else {
        console.warn('Invalid image data received:', image)
      }
    },
    handleAiPosterGenerate() {
      this.$refs.aiPosterDialog.show({
        name: 'poster',
        title: 'AI海报生成',
        content: 'AI海报生成',
        _id: 'default',
      })
    }
  },
  computed: {},
  mounted() {
    const { image } = this.$route.params
    
    if (image) {
      this.handleSelectImage(image)
    }
  }
}
</script>

<style lang="less" module>
.site-agent-content {
  height: calc(100vh - 100px);
  min-width: 740px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fff;
}
.site-agent-content-button-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 10px;
  gap: 8px;
  flex: 1;
}
</style>
