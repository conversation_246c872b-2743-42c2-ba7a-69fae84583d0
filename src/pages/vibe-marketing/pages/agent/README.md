# Agent 技术实现文档

## 1. 组件架构概述

Agent 相关页面采用基于 BaseAgent 的继承扩展模式，所有具体 Agent（如 SiteAgent、ArticleAgent）均通过继承 BaseAgent 实现页面结构和核心交互的复用。

目录结构如下：
```
agent/
  conponents/
    BaseAgent/         # 基础 Agent 组件
      index.vue
      mixins/
      components/
  SiteAgent/           # 具体 Agent 示例
    index.vue
    components/
  ArticleAgent/
    index.vue
    components/
```

---

## 2. BaseAgent 组件详解

### 2.1 页面结构
- 顶部 Header 区域：显示面包屑导航和标题。
- 左侧 Chat 区域：与 Agent 聊天交互。
- 右侧 Content 区域：展示业务内容。

### 2.2 关键属性与方法
- `agentApiName`：Agent 唯一标识，子类需指定。
- `crumbs`：面包屑导航，通过 `getCrumbs` 方法获取。
- `onAgentReady(agent)`：Agent 初始化回调。
- `onAgentMessage(data)`：Agent 消息处理回调。
- `onHeaderEvent(args)`：Header 事件处理。
- `getSessionId()`：生成会话唯一 ID。
- `getCrumbs()`：返回面包屑导航数组。

### 2.3 组件扩展方式
- 通过 `mixins` 复用通用逻辑。
- 子组件可覆盖 `data` 和 `methods`，实现自定义。

---

## 3. 定制 Agent 的流程与示例

### 3.1 定制流程
1. 在 agent 目录下新建 Agent 目录及 index.vue 文件。
2. 通过 `extends: BaseAgent` 继承基础能力。
3. 指定 `agentApiName`，区分不同 Agent。
4. 覆盖/扩展 `methods`，实现业务定制。
5. 如有特殊需求，可替换/扩展子组件。

### 3.2 示例：SiteAgent 实现
```js
import BaseAgent from '../conponents/BaseAgent/index.vue'
import Content from './components/Content.vue'
import Chat from './components/Chat.vue'

export default {
  name: 'SiteAgent',
  components: { Content, Chat },
  extends: BaseAgent,
  data() {
    return {
      agentApiName: 'Copilot_hexagon_ai__c',
    }
  },
  methods: {
    getCrumbs() {
      return [
        { text: 'Vibe Marketing', to: { name: 'vibe-marketing-init' } },
        { text: '策划', to: { name: 'vibe-marketing-plan' } },
        { text: '微页面制作', to: false },
      ]
    },
    onAgentMessage(data) {
      try {
        const content = data.message.content.replace(/\n/g, '').replace(/\t/g, '');
        const pageData = JSON.parse(content);
        this.$context.$content.setPageData(pageData.page);
      } catch (error) {
        // 错误处理
      }
    },
    onAgentReady() {
      console.log('onAgentReady', this.$context.$content.getPageData());
    }
  }
}
```

#### $context 说明
- `$context` 是在 BaseAgent 及其派生组件中注入的上下文对象，通常用于在组件间共享业务数据和方法。
- $context 可能包含多个业务模块（如 $content、$user 等），具体内容可根据项目实际扩展。
- 在定制 Agent 时，建议通过 $context 进行数据流转和业务调用，避免直接操作子组件实例。

---

## 4. 注意事项
- BaseAgent 负责页面结构和通用交互，定制 Agent 只需关注业务差异。
- agentApiName 必须唯一，便于区分不同 Agent。
- 如需自定义导航、消息处理等，直接覆盖对应方法。
- 可按需扩展/替换 Chat、Content 等子组件。

---

## 5. 参考
- BaseAgent 组件源码：`conponents/BaseAgent/index.vue`
- SiteAgent 示例：`SiteAgent/index.vue`
- ArticleAgent 示例：`ArticleAgent/index.vue` 