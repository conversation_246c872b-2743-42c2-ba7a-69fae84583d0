<template>
  <div>
    <ContentHeader
        v-bind="$attrs"
      >
        <div
          slot="right"
          :class="$style['apply-tmp__page__options']"
        >
          <fx-button
            size="small"
            type="primary"
            @click="handleOpenApplyTmpDialog"
          >
            发送
          </fx-button>
        </div>
      </ContentHeader>
      <ApplyTmpDialog
      v-if="applyTmpDialogVisible"
      :visible="applyTmpDialogVisible"
      :prompt-detail="promptDetail"
      @onClose="handleCloseApplyTmpDialog"
      @onSend="handleSend"
    />
  </div>
</template>

<script>
  import BaseAgentHeader from '../../conponents/BaseAgent/components/Header.vue'
  import ApplyTmpDialog from '@/pages/vibe-marketing/components/apply-tmp-dialog.vue'
  export default {
    extends: BaseAgentHeader,
    components: {
      ApplyTmpDialog,
    },
    data() {
      return {
        applyTmpDialogVisible: false,
      }
    },
    computed: {
      promptDetail() {
        return this.$context.$content.promptDetail
      },
    },
    methods: {
      handleCloseApplyTmpDialog() {
        this.applyTmpDialogVisible = false
      },
      handleOpenApplyTmpDialog() {
        this.applyTmpDialogVisible = true
      },
      handleSend(payload) {
      if (!this.$context.agent) return
      const {
        activeAgentId = '',
        defaultHelperName = '',
        objects = [],
        prompt = '',
        templateId = '',
      } = payload

      this.$context.agent.sendConfigData({
        activeAgentId,
        defaultHelperName,
      })

      const message = {
        msg: prompt,
        templateId,
        objects,
      }
      this.$context.agent.sendMessageToAI(message, res => {
        console.log('sendMessageToAI', res)
        const { errMsg, errCode } = res
        if (errCode === 0 && errMsg === 'receivePeriod:resolve') {
          this.$context.$content.message = res.message;
        }
      })
    },
    },
  }
</script>

<style lang="less" module>
  .apply-tmp__page__options {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
</style>