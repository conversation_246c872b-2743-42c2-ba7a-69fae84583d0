<template>
  <ArticleTinymceEditor
    :height="editorHeight"
    :content="message && message.previewContent"
    @setEditorContent="handleSetEditorContent"
  />
</template>

<script>
import BaseAgentContent from '../../conponents/BaseAgent/components/Content.vue'
import ArticleTinymceEditor from '@/pages/article/edit/ArticleTinymceEditor.vue'
export default {
  name: 'ArticleAgentContent',
  extends: BaseAgentContent,
  components: {
    ArticleTinymceEditor,
  },
  data() {
    return {
      editorHeight: 500,
      message: {},
      promptDetail: {},
    }
  },
  created() {
    this.fetchTemplateDetail()
  },
  mounted() {
    // 这里需要延迟等编辑器初始化好
    setTimeout(() => {
      this.initEditorHeight()
    }, 1000)
    window.addEventListener('resize', this.initEditorHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initEditorHeight)
  },
  methods: {
    initEditorHeight() {
      const { height: applyTmpPageHeight } = this.$context.$agentPage.getBoundingClientRect()
      const { height: applyTmpPageHeaderHeight } = this.$context.$agentPageHeader.getBoundingClientRect()

      const height = applyTmpPageHeight - applyTmpPageHeaderHeight - 16 - 16
      this.editorHeight = height
    },
    fetchTemplateDetail() {
      const { templateId } = this.$route.query
      if (!templateId) return

      http.queryObjDataById({
        objectAPIName: 'PromptObj',
        selectFields: [
          'name',
          'category',
          'title',
          'content',
          'status',
          'last_modified_time',
          'aihelper_id',
          'step_split',
          'created_by',
          'number_of_collections',
          'scope_type',
        ],
        id: templateId,
      }).then(res => {
        if (res && res.errCode === 0) {
          this.promptDetail = {
            objectData: res.data,
          }
          this.$context.setAgentApiName(res.data?.aihelper_id)
          if (res.data.title) {
            this.$context.setCrumbs([
              {
                text: 'Vibe Marketing',
                to: { name: 'vibe-marketing-init' },
              },
              {
                text: '创意',
                to: { name: 'vibe-marketing-idea' },
              },
              {
                text: res.data.title,
                to: false,
              },
            ])
          }

          this.$context.$header.handleOpenApplyTmpDialog()
        }
      })
    },
    handleSetEditorContent(content) {
      this.message.previewContent = content
    },
  },
}
</script>

