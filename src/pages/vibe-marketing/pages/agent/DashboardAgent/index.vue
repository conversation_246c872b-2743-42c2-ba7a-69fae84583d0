<script>
import BaseAgent from '../conponents/BaseAgent/index.vue'
import Content from './components/Content.vue'
import Chat from './components/Chat.vue'
import http from '@/services/http/index.js'

export default {
  name: 'SiteAgent',
  components: {
    Content,
    Chat,
  },
  extends: BaseAgent,
  data() {
    return {
      borderId: this.$route.query.id,
      agentApiName: 'Copilot_dashboard__c',
    }
  },
  created() {
    this.$context.borderId = this.borderId
  },
  mounted() {
    this.replaceData()
  },
  methods: {
    getCrumbs() {
      return [
        {
          text: 'Vibe Marketing',
          to: { name: 'vibe-marketing-init' },
        },
        {
          text: '策划',
          to: { name: 'vibe-marketing-plan' },
        },
        {
          text: '运营任务制作2',
          to: false,
        },
      ]
    },

    onAgentMessage(data) {
      let jsonData = null
      try {
        if (!data.message.finish) {
          return
        }
        let jsonString = null

        // 移除\n、\t
        const content = data.message.content.replace(/\n/g, '').replace(/\t/g, '')
        if (/```json/.test(content)) {
          const jsonMatch = content.match(/```json([\s\S]*?)```/)
          if (jsonMatch && jsonMatch[1]) {
            jsonString = jsonMatch[1].trim()
          }
        } else {
          jsonString = content
        }
        jsonData = JSON.parse(jsonString)
        console.log('jsonData', jsonData)
      } catch (error) {
        console.error('handleAgentMessage error', error)
      }

      if (jsonData) {
        this.replaceData(jsonData)
      }
    },
    onAgentReady() {
      // console.log('onAgentReady', this.$context, this.$context.$content.getPageData())
    },
    async replaceData(jsonData) {
      if (jsonData && Array.isArray(jsonData)) {
        // 初始化数据
        try {
          await jsonData?.reduce(async (promiseChain, item) => {
            await promiseChain
            const boardListId = await this.$context.$content.addBoardCardList(item.name)
            if (boardListId) {
              await item?.boardCards?.reduce(async (cardPromiseChain, card) => {
                await cardPromiseChain
                return this.$context.$content.addBoardCard(boardListId, card.name)
              }, Promise.resolve())
            }
            await this.$context.$content.getBoardDetail()
            return Promise.resolve()
          }, Promise.resolve())
        } catch (error) {
          console.error('replaceData error:', error)
        }
      } else if (jsonData && typeof jsonData === 'object') {
        let boardCardLists = []
        const res = await http.getBoardDetail({
          boardId: this.borderId,
        })
        if (res.errCode === 0 && res.data) {
          boardCardLists = res.data.boardCardLists || []
        }
        // 更新数据
        try {
          Object.keys(jsonData).forEach(async key => {
            if (jsonData[key]?.name) {
              await this.$context.$content.updateBoardCardList(boardCardLists?.[key]?.id, jsonData[key].name)
            }
            const boardCardsKeys = Object.keys(jsonData[key]?.boardCards) || []
            await boardCardsKeys?.reduce(async (cardPromiseChain, idx) => {
              await cardPromiseChain
              const card = boardCardLists?.[key]?.boardCards?.[idx]
              if (card) {
                if (jsonData[key]?.boardCards?.[idx]) {
                  await this.$context.$content.updateBoardCard(card, jsonData[key]?.boardCards?.[idx])
                }
                // else {
                //   await this.$context.$content.deleteBoardCard(card.id)
                // }
              } else {
                await this.$context.$content.addBoardCard(boardCardLists?.[key]?.id, jsonData[key]?.boardCards?.[idx])
              }
              return Promise.resolve()
            }, Promise.resolve())
            await this.$context.$content.getBoardDetail()
          })
        } catch (error) {
          console.error('replaceData error:', error)
        }
      }
    },
  },
}
</script>
