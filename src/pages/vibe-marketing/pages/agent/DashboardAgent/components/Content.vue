<template>
  <div :class="$style['site-agent-content']">
    <div :class="$style['site-agent-content-wrapper']">
      <KanbanDetail
        v-if="borderId"
        :id="borderId"
        ref="kanbanDetail"
        :is-agent="true"
      />
    </div>
  </div>
</template>

<script>
import KanbanDetail from '@/pages/kanban/detail/index.vue'
import http from '@/services/http/index.js'

export default {
  name: 'DashboardAgentContent',
  components: {
    KanbanDetail,
  },
  data() {
    return {
    }
  },
  computed: {
    borderId() {
      return this.$context.borderId
    },
  },
  mounted() {
  },
  methods: {
    async addBoardCardList(name) {
      return new Promise(resolve => {
        http
          .addBoardCardList({
            boardId: this.$context.borderId,
            name,
          })
          .then(async res => {
            if (res.errCode === 0) {
              resolve(res.data)
              return
            }
            resolve(null)
          })
      })
    },
    async updateBoardCardList(boardCardListId, name) {
      return new Promise(resolve => {
        http
          .updateBoardCardList({
            boardCardListId,
            name,
          })
          .then(async res => {
            if (res.errCode === 0) {
              resolve(res.data)
              return
            }
            resolve(null)
          })
      })
    },
    async addBoardCard(boardCardListId, name) {
      return new Promise(resolve => {
        http
          .addBoardCard({
            boardCardListId,
            type: 'common',
            name,
          })
          .then(async res => {
            if (res.errCode === 0) {
              resolve(res.data)
              return
            }
            resolve(null)
          })
      })
    },
    async getBoardCardDetail(boardCardId) {
      return new Promise(resolve => {
        http
          .getBoardCardDetail({
            boardCardId,
          })
          .then(async res => {
            if (res.errCode === 0) {
              resolve(res.data)
              return
            }
            resolve(null)
          })
      })
    },
    async updateBoardCard(boardCard, name) {
      return new Promise(resolve => {
        http
          .updateBoardCard({
            ...boardCard,
            boardCardId: boardCard.id,
            name,
          })
          .then(async res => {
            if (res.errCode === 0) {
              resolve(res.data)
              return
            }
            resolve(null)
          })
      })
    },
    async deleteBoardCard(boardCardId) {
      return new Promise(resolve => {
        http.deleteBoardCard({ boardCardId }).then(res => {
          if (res.errCode === 0) {
            resolve(res.data)
            return
          }
          resolve(null)
        })
      })
    },
    // 获取看板详情
    getBoardDetail() {
      this.$refs.kanbanDetail.getBoardDetail({ id: this.borderId, withoutLoading: true })
    },
  },
}
</script>

<style lang="less" module>
.site-agent-content {
  height: calc(100vh - 100px);
  overflow: hidden;
  .site-agent-content-wrapper {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
</style>
