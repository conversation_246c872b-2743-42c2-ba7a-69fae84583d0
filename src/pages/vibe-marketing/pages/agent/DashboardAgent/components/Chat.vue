<script>
import BaseChat from '../../conponents/BaseAgent/components/Chat.vue'
import http from '@/services/http/index.js'

export default {
  extends: BaseChat,
  data() {
    return {
      currentData: [],
    }
  },
  created() {
    this.$context.addAgentReadyListener(() => {
      this.getBoardDetail()
    })
  },
  methods: {
    setVariables() {
      if (this.$context.agent) {
        this.$context.agent.sendConfigData({
          variables: [
            {
              name: 'currentData',
              value: JSON.stringify(this.currentData),
            },
          ],
        })
      }
    },
    async getBoardDetail() {
      const res = await http.getBoardDetail({
        boardId: this.$context.borderId,
      })
      if (res.errCode === 0 && res.data) {
        // 处理 boardCardLists 数据
        const formattedData = (res.data.boardCardLists || []).map(list => ({
          name: list.name, // 运营任务列表标题
          boardCards: (list.boardCards || []).map(card => ({
            name: card.name, // 具体可执行的运营任务描述
          })),
        }))
        this.currentData = formattedData
        this.setVariables()
      }
    },
  },
}
</script>
