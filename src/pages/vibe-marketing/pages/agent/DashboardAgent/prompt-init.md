- 请根据用户输入「${sence_variables.custom_sence.userinput}」 ，深入分析其业务目标与核心需求，生成一份系统、详细且具有针对性的运营任务清单。输出格式必须采用如下 JSON 结构：

[

  {

    "name": "运营任务列表标题（任务分类，应根据具体活动/项目自动变化，如：拉新推广、转化优化、活动执行、品牌建设、留存激活等）",

    "boardCards": [

      {

        "name": "具体可执行的运营任务描述（如：设计新用户拉新裂变活动方案）"

      },

      {

        "name": "另一条具体运营任务"

      }

    ]

  }

]

- 生成要求：

**任务分类名称（name）**应依据用户输入的主题或活动类型动态生成，避免使用固定模板化分类。

每个任务描述（boardCards）必须具体、明确、可执行，具有落地操作性。

**运营任务内容应完整覆盖实现用户目标所需的关键环节，**包括但不限于策划、执行、推广、转化、优化、评估等步骤。

任务列表应体现阶段性逻辑或目标导向结构，例如围绕一次活动推广，可能涵盖预热期、爆发期、冷却期等维度。

- 示例主题参考（触发不同任务分类）：

用户输入：“我要做一次新品预热推广活动”

分类可能包括：活动预热策划、用户互动设计、内容分发执行、渠道传播布局、效果数据监控

用户输入：“我们要提升私域用户活跃度”

分类可能包括：用户画像分析、私域运营策略优化、社群活跃机制搭建、个性化内容触达

用户输入：“希望提升电商转化率”

分类可能包括：转化路径优化、用户行为分析、落地页改版、AB 测试、促销机制设计