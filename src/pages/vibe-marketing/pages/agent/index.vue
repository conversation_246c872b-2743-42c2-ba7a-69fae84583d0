<template>
  <div :class="$style['mark-agent-page']">
    <div v-if="errorMsg" style="color: red; text-align: center; margin-top: 40px;">{{ errorMsg }}</div>
    <component v-else :is="component" />
  </div>
</template>
<script>

export default {
  name: 'MarkAgentPage',
  data() {
    return {
      component: null,
      errorMsg: '',
    }
  },
  created() {
    const { name } = this.$route.query
    const loader = this.getComponentLoader(name)
    if (loader) {
      this.component = loader
    } else {
      this.errorMsg = '未找到对应的 Agent：' + (name || '未知')
    }
  },
  methods: {
    getComponentLoader(name) {
      if (name === 'article') {
        return () => import('./ArticleAgent/index.vue')
      } else if (name === 'site') {
        return () => import('./SiteAgent/index.vue')
      } else if (name === 'poster') {
        return () => import('./PosterAgent/index.vue')
      } else if (name === 'dashboard') {
        return () => import('./DashboardAgent/index.vue')
      } else {
        return null
      }
    }
  }
}
</script>

<style lang="less" module>
.mark-agent-page {
  height: 100%;
}
</style>
