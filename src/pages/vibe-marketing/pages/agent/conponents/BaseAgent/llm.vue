<template>
  <div
    :class="$style['agent__page']"
    ref="agentPage"
  >
    <div
      :class="$style['agent__page__header']"
      ref="agentPageHeader"
    >
      <Header ref="header" :is-chat-open="isChatOpen" :title="crumbs" :border="true" @onHeaderEvent="_handleHeaderEvent" />
    </div>
    <div :class="{
      [$style['agent__page__content']]: true,
      [$style['chat-open']]: isChatOpen,
    }">
      <div
        :class="$style['agent__page__content-wrapper']"
      >
        <div :class="$style['agent__page__content__left']">
          <Chat :default-helper-name="agentApiName" @onReady="_handleShareGPTReady" @onAgentMessage="_handleAgentMessage" />
        </div>
        <div :class="$style.chat_toggle">
          <img :src="iconArrowLeft" alt="arrow left" @click="handleChatToggle" />
        </div>
        <div :class="$style['agent__page__content__right']">
          <Content ref="content" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Chat from './components/ChatLLM.vue'
import Content from './components/Content.vue'
import Header from './components/NewHeader.vue'
import mixins from './mixins/mixins'
import iconArrowLeft from '@/assets/images/icon-arrow-left.svg'

export default {
  name: 'BaseAgent',
  mixins: [mixins],
  components: {
    Chat,
    Content,
    Header,
  },
  data() {
    return {
      agentApiName: '',
      iconArrowLeft,
      isChatOpen: true,
    }
  },
  methods: {
    /**
     * agent初始化
     */
    onAgentReady(agent) {
      console.log('onAgentReady', agent)
    },
    /**
     * 处理agent消息
     */
    onAgentMessage(data) {
      console.log('onAgentMessage', data)
    },
    /**
     * 处理header事件
     */
    onHeaderEvent(args) {
      console.log('onHeaderEvent', args)
    },
    /**
     * 设置会话ID
     */
     getSessionId() {
      const { id, enterpriseAccount } = FS.contacts.getCurrentEmployee();
      return `${enterpriseAccount}-${id}-${this.agentApiName}`
    },
    /**
     * 获取crumbs
     */
     getCrumbs() {
      return [
        {
          text: 'Vibe Marketing',
          to: { name: 'vibe-marketing-init' },
        },
        {
          text: '创意',
          to: { name: 'vibe-marketing-idea' },
        },
        {
          text: '',
          to: false,
        },
      ]
    },
    handleChatToggle() {
      this.isChatOpen = !this.isChatOpen
    },
  },
}
</script>

<style lang="less" module>
.agent__page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #F7F9FF;
  background-position: left top;
  background-repeat: no-repeat;

  .agent__page__options {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .agent__page__content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    flex: 1;
    padding: 0 12px 12px 0;
    box-sizing: border-box;

    &.chat-open {
      padding-left: 12px;

      .agent__page__content__left {
        width: 375px;
      }

      .chat_toggle img {
        transform: rotate(0deg);
      }
    }

    .agent__page__content-wrapper {
      display: flex;
      height: 100%;
      align-items: stretch;
      flex: 1;
    }

    .agent__page__content__left {
      width: 0;
      transition: width 0.2s ease-in-out;
      overflow: hidden;
      margin-right: 0;
      border: 0;
      box-shadow: 0px 2px 6px 0px #00000026;
      border-radius: 8px;
    }

    .chat_toggle {
      width: 30px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 10px;
        cursor: pointer;
        transition: transform 0.2s ease-out;
        transform: rotate(180deg);
      }
      
    }

    .agent__page__content__right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      box-shadow: 0px 2px 6px 0px #00000026;
      border-radius: 6px;
      overflow: hidden;
    }
  }
}
</style>
