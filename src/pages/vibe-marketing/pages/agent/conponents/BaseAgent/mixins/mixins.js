import Vue from 'vue'

/**
 * 创建初始 context 对象
 * @param {object} vm - 组件实例
 * @returns {object} context 对象
 */
const createInitialContext = (vm) => ({
  // 状态属性
  agent: null,
  agentApiName: null,
  
  // refs 相关（mounted 后赋值）
  $content: null,
  $agentPageHeader: null,
  $agentPage: null,
  $header: null,
  
  // 方法绑定
  setCrumbs: vm.setCrumbs?.bind(vm),
  setAgentApiName: vm.setAgentApiName?.bind(vm),
  addAgentReadyListener: vm.addAgentReadyListener?.bind(vm),
  removeAgentReadyListener: vm.removeAgentReadyListener?.bind(vm),
})

/**
 * 基础agent mixins
 * 提供分享GPT初始化、处理agent消息、设置会话ID等功能
 * 组件内部实现
 */
export default {
  data() {
    // 使用 Vue.observable 确保响应性
    return {
      crumbs: [],
      readyEvents: [],
      hasSessionId: true, // 当前的ShareGPT是否已经创建了SessionId
      context: Vue.observable(createInitialContext(this))
    }
  },
  computed: {
    $context() {
      return this.context
    }
  },
  provide() {
    return {
      $context: this.context
    }
  },
  created() {
    this._initContextMethods()
    this.crumbs = this.getCrumbs()
  },
  mounted() {
    this._initContextRefs()
  },
  methods: {
    /**
     * 初始化 context 中的 refs
     * @private
     */
    _initContextRefs() {
      const refsMapping = {
        $agentPage: 'agentPage',
        $content: 'content',
        $agentPageHeader: 'agentPageHeader',
        $header: 'header'
      }

      // 批量更新 refs
      Object.entries(refsMapping).forEach(([contextKey, refKey]) => {
        if (this.$refs[refKey]) {
          this.context[contextKey] = this.$refs[refKey]
        }
      })
    },

    /**
     * 初始化 context 中的方法和基础属性
     * @private
     */
    _initContextMethods() {
      const methodsMapping = {
        agentApiName: this.agentApiName,
        setCrumbs: this.setCrumbs,
        setAgentApiName: this.setAgentApiName,
        addAgentReadyListener: this.addAgentReadyListener,
        removeAgentReadyListener: this.removeAgentReadyListener
      }

      // 批量更新方法
      Object.entries(methodsMapping).forEach(([key, value]) => {
        if (value) {
          this.context[key] = value
        }
      })
    },
    /**
     * 分享GPT初始化
     * @param {object} agent - GPT agent 实例
     */
    _handleShareGPTReady(agent) {
      this.context.agent = agent
      this.onAgentReady(agent)
      
      agent.sendConfigData({
        bizSessionId: this.getSessionId(),
        enableAgentMessageListener: true,
        debug: true,
      })
      
      // 触发ready事件
      this.readyEvents.forEach(event => event(agent))
    },
    /**
     * 处理agent消息
     */
    _handleAgentMessage(data) {
      const { message } = data
      if (message && message.finish && message.sessionId) {
        this.hasSessionId = true
      }
      this.onAgentMessage(data);
    },
    /**
     * 处理header事件
     */
    _handleHeaderEvent(args) {
      this.onHeaderEvent(args);
    },
    /**
     * 设置crumbs
     */
    setCrumbs(crumbs) {
      this.crumbs = crumbs
    },
    /**
     * 设置agentApiName
     */
    setAgentApiName(agentApiName) {
      this.agentApiName = agentApiName
    },
    /**
     * 添加事件监听
     */
    addAgentReadyListener(event) {
      this.readyEvents.push(event)
    },
    /**
     * 移除事件监听
     */
    removeAgentReadyListener(event) {
      this.readyEvents = this.readyEvents.filter(e => e !== event)
    },
  },
}