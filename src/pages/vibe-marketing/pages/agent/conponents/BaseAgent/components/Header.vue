<template>
  <content-header
    v-bind="$attrs"
  >
  </content-header>
</template>

<script>
import ContentHeader from '@/components/content-header/index.vue'   
import inject from '../mixins/inject'
export default {
  name: 'BaseAgentHeader',
  mixins: [inject],
  components: {
    ContentHeader,
  },
  methods: {
    /**
     * 统一事件回调方法
     */
    triggerEvent(eventType, args) {
      this.$emit('onHeaderEvent', {
        eventType,
        data: args,
      })
    },
  },
}
</script>


