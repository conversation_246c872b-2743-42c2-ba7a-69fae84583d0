<template>
  <div :class="$style['base-agent-chat']">
    <BaseShareGPT
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>
<script>
import BaseShareGPT from '../../../../../components/baseShareGPT.vue'
import inject from '../mixins/inject'
export default {
  name: 'BaseAgentChat',
  mixins: [inject],
  components: {
    BaseShareGPT,
  },
}
</script>

<style lang="less" module>
.base-agent-chat {
  height: 100%;
}
</style>
