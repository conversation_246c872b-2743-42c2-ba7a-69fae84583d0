<!-- 基础Agent聊天组件 - 实现LLM对话能力 -->
<template>
  <div :class="$style['base-agent-chat']">
    <div :class="$style.chat_container" ref="chatContainer">
      <!-- 聊天消息列表 -->
      <div :class="$style.message_list" ref="messageList">
        <div :class="$style.message_list_inner">
          <div v-for="(message, index) in messages" :key="index" :class="[
            $style.message_item,
            message.role === 'user' ? $style.user_message : $style.ai_message
          ]">
            <!-- 头像 -->
            <div :class="$style.avatar">
              <img :src="message.role === 'user' ? userAvatar : aiAvatar" :alt="message.role">
            </div>
            <!-- 消息内容 -->
            <div :class="$style.message_content">
              <!-- 根据消息类型渲染不同内容 -->
              <template v-if="message.type === 'flow'">
                <!-- 流程类型消息 -->
                <div :class="$style.flow_message">
                  <!-- <div :class="$style.message_text" v-html="formatMessage(message.content)"></div> -->
                  <!-- 流程卡片 -->
                  <div :class="$style.task_cards_container">
                    <!-- 工作流程标题 -->
                    <div :class="$style.workflow_header">
                      <i v-if="message.inProgress" :class="[$style.loading_icon, 'fx-icon-loading-ring']"></i>
                      <i v-else-if="message.completed" :class="['fx-icon-chenggong']"></i>
                      <i v-else-if="message.error" :class="['fx-icon-warning']"></i>
                      <span>
                        {{ message.flowTitle || '正在执行流程...' }}
                      </span>
                    </div>
                    <!-- 工作流步骤列表 -->
                    <fx-collapse accordion isCard :isArrowRight="false" style="margin: 10px;">
                      <fx-collapse-item v-for="(step, stepIndex) in message.steps" :key="`${stepIndex}`">
                        <template slot="title">
                          {{ step.name }}
                        </template>
                        <template slot="header-action">
                          <span :class="$style.execution_time" v-if="step.completed">{{ formatTime(step.executionTime) }}</span>
                          <span :class="$style.task_icon">
                            <i v-if="step.error" class="fx-icon-warning" :class="$style.error_icon"></i>
                            <i v-else-if="step.completed" class="fx-icon-chenggong" :class="$style.success_icon"></i>
                            <i v-else-if="step.inProgress" class="fx-icon-loading-ring"
                              :class="[$style.progress_icon, $style.spinning]"></i>
                            <i v-else class="fx-icon-huantu" :class="$style.waiting_icon"></i>
                          </span>
                        </template>
                        <div :class="$style.result_container" v-html="formatMessage(step.response)"></div>
                      </fx-collapse-item>
                    </fx-collapse>

                    <!-- 错误消息 -->
                    <div v-if="message.error" :class="$style.workflow_message">
                      <i class="fx-icon-warning" :class="$style.error_icon"></i>
                      <span>{{ message.error }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <!-- 默认消息类型 -->
                <div :class="$style.message_text" v-html="formatMessage(message.content)"></div>
              </template>
            </div>
          </div>

          <!-- 正在输入的消息 -->
          <div v-if="typingMessage && !isFlowTyping" :class="[$style.message_item, $style.ai_message]">
            <div :class="$style.avatar">
              <img :src="aiAvatar" alt="assistant">
            </div>
            <div :class="$style.message_content">
              <div :class="$style.message_text" v-html="formatMessage(typingMessage)"></div>
            </div>
          </div>

          <!-- 加载中提示 -->
          <div v-if="loading" :class="$style.loading_message">
            <div :class="$style.avatar">
              <img :src="aiAvatar" alt="assistant">
            </div>
            <div :class="$style.loading_dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div :class="$style.input_area">
        <fx-input :class="$style.input" v-model="inputMessage" type="textarea" :rows="3"
          :placeholder="loading ? '正在自动执行任务...' : '请输入入 回车发送 Ctrl+回车换行'" :disabled="loading"
          @keydown.enter.native.prevent="handleEnterPress" />
        <fx-button size="small" type="primary"
          :class="[$style.send_button, inputMessage.trim() && !loading ? $style.send_button_active : '']" square
          :loading="loading" :disabled="loading" icon="fx-icon-obj-app430" @click="handleSend"></fx-button>
      </div>
    </div>
  </div>
</template>

<script>
import inject from '../mixins/inject'
import { AI_CONFIG } from '@/components/AiGenerator/config'
import { generateImage } from '@/pages/vibe-marketing/utils/generateImage'
import { b64toFile } from '@/utils/index'
import { getTNPath } from '@/utils/files'

const defaultAiAvatar = 'https://a2.fspage.com/FSR/weex/avatar/marketing_app/images/ai-helper-avatar5.svg'
const defaultUserAvatar = FS.contacts.getCurrentEmployee().profileImage

export default {
  name: 'BaseAgentChatLLM',
  mixins: [inject],
  props: {
    /**
     * 用户头像URL
     * @type {String}
     */
    userAvatar: {
      type: String,
      default: defaultUserAvatar
    },
    /**
     * AI头像URL
     * @type {String}
     */
    aiAvatar: {
      type: String,
      default: defaultAiAvatar
    },
    /**
     * 输入框占位文本
     * @type {String}
     */
    placeholder: {
      type: String,
      default: '请输入消息...'
    },
    /**
     * 初始消息列表
     * @type {Array}
     */
    initialMessages: {
      type: Array,
      default: () => []
    },
    /**
     * 打字效果速度(ms/字符)
     * @type {Number}
     */
    typingSpeed: {
      type: Number,
      default: 50
    },
    /**
     * 是否自动滚动到底部
     * @type {Boolean}
     */
    autoScroll: {
      type: Boolean,
      default: true
    },
    /**
     * 是否启用打字效果
     * @type {Boolean}
     */
    enableTypingEffect: {
      type: Boolean,
      default: true
    },
    /**
     * LLM API配置
     * @type {Object}
     */
    apiConfig: {
      type: Object,
      default: () => ({})
    },
    /**
     * 工作流步骤配置
     * @type {Array}
     */
    workflowSteps: {
      type: Array,
      default: () => []
    },
    /**
     * 系统提示词
     * @type {String}
     */
    systemPrompt: {
      type: String,
      default: ''
    },
    /**
     * 确认短语列表（用于判断用户确认）
     * @type {Array}
     */
    confirmPhrases: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      messages: [],
      conversationHistory: [],
      inputMessage: '',
      loading: false,
      typingMessage: '',
      typingBuffer: '',
      typingTimeout: null,

      // 工作流相关
      currentStep: 0,                // 当前流程步骤
      currentSubStep: 0,             // 当前子步骤（用于flow类型）
      lastExecutionResult: null,     // 保存上一次执行结果
      needConfirmation: true,       // 默认第一步都需要确认
      lastIntent: null,              // 上一次的意图
      maxRetries: 3,                // 最大重试次数
      retryCount: 0,                // 当前重试次数

      // API相关
      apiEndpoint: '',               // API端点
      model: '',                     // 模型名称
      temperature: 0.7,              // 温度参数
      maxTokens: 4096,               // 最大token数

      // 状态标志
      isFlowTyping: false,            // 工作流是否正在输入

      //全局变量
      globalVariables: {},
      //默认第一次创建首页
      isCreateHomePage: true,
    }
  },
  created() {
    // 初始化消息列表
    this.messages = this.initialMessages.map(msg => ({
      ...msg,
      timestamp: msg.timestamp || Date.now()
    }))

    // 初始化API配置
    this.initApiConfig()
  },
  mounted() {
    this.$emit('onReady', {});
    // 初始化后自动滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom()
    })
  },
  beforeDestroy() {
    // 清理定时器
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout)
    }
  },
  methods: {
    /**
     * 初始化API配置
     */
    initApiConfig() {
      this.apiEndpoint = this.apiConfig.endpoint || AI_CONFIG.api.endpoint
      this.model = this.apiConfig.model || AI_CONFIG.api.defaultModel
      this.temperature = this.apiConfig.temperature || AI_CONFIG.api.temperature
      this.maxTokens = this.apiConfig.maxTokens || AI_CONFIG.api.maxTokens
    },

    /**
     * 格式化消息内容
     * 支持换行和链接
     */
    formatMessage(content) {
      if (!content) return ''
      return content
        .replace(/\n/g, '<br>')
        .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>')
    },

    /**
     * 处理回车按键
     */
    handleEnterPress(e) {
      // Shift + Enter 换行
      if (e.shiftKey) return
      this.handleSend()
    },

    /**
     * 处理发送消息
     */
    async handleSend() {
      const message = this.inputMessage.trim()
      if (!message || this.loading) return

      // 添加用户消息
      this.addMessage({
        role: 'user',
        content: message
      })

      this.setLoading(true)

      try {


        if (this.messages.length > 1) {
          // 检测用户意图
          const intentResult = await this.detectUserIntent(this.inputMessage)
          console.log('用户意图检测结果:', intentResult)
          this.lastIntent = intentResult

          // 根据意图处理不同的场景
          switch (intentResult.intent) {
            case 'create':
              // 重置步骤，开始新的页面创建流程
              this.currentStep = 0
              this.lastExecutionResult = null
              this.needConfirmation = true
              // 重置重试计数
              this.retryCount = 0
              break
            case 'confirm':
              if (this.needConfirmation) {
                this.currentStep++
                this.needConfirmation = false
              }
              break
            case 'supplement':
              // 补充信息时不重置步骤，继续当前流程
              break
            case 'modify':
              // 修改意图会在 startWorkflow 中处理
              break
            case 'reject':
            // 可以添加一些拒绝后的处理逻辑
            // this.addAiMessage('好的，我明白了。如果您有新的需求，随时告诉我。')
            // this.setLoading(false)
            // return
          }
        }

        // 清空输入框
        this.inputMessage = ''

        await this.startWorkflow(message)

      } catch (error) {
        this.setLoading(false)
        console.error('发送消息失败:', error)
        this.addAiMessage(`抱歉，处理您的请求时出现了问题: ${error.message || '未知错误'}`)
      }
    },

    /**
     * 添加消息到列表
     */
    addMessage(message) {
      // 确保消息有type字段
      if (!message.type) {
        message.type = 'default'
      }

      this.messages.push({
        ...message,
        timestamp: Date.now()
      })

      // 使用 nextTick 确保 DOM 更新后再滚动
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      console.log(this.messages, 999)
      //返回当前消息在列表中的索引值
      return this.messages.length - 1
    },

    /**
     * 添加AI回复消息
     */
    addAiMessage(content) {
      const message = {
        role: 'assistant',
        content: content,
        timestamp: Date.now(),
      }

      return this.addMessage(message)
    },

    /**
     * 设置加载状态
     */
    setLoading(status) {
      this.loading = status
      if (!status && this.typingMessage) {
        if (!this.isFlowTyping)
          this.addAiMessage(this.typingMessage)
        this.typingMessage = ''
      }
    },

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      if (!this.autoScroll) return

      const messageList = this.$refs.messageList.querySelector('.' + this.$style.message_list_inner)
      if (messageList) {
        messageList.scrollTop = messageList.scrollHeight
      }
    },

    /**
     * 处理流式响应的内容
     */
    handleStreamContent(content) {
      if (this.enableTypingEffect) {
        this.typingMessage += content
      } else {
        this.typingBuffer += content
      }

      // 使用 nextTick 确保 DOM 更新后再滚动
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    /**
     * 启动工作流
     */
    async startWorkflow(message) {
      try {
        const stepInfo = this.workflowSteps[this.currentStep] || {}
        console.log('startWorkflow:', stepInfo);
        this.isFlowTyping = stepInfo.actionType === 'flow'

        // 获取当前用户输入
        // const userMessage = message || this.messages[this.messages.length - 1]?.content || ''

        if (stepInfo.actionType === 'flow') {
          const content = message

          // 如果是修改意图，且有上一次执行结果
          if (this.lastIntent.intent === 'modify' && this.lastExecutionResult) {
            const flowMessageIndex = this.addFlowMessage('微页面修改流程', [
              {
                name: '修改页面内容',
                description: '根据用户需求修改已创建的页面'
              }
            ])

            const stepInfo = this.workflowSteps[this.workflowSteps.length - 1] || {}
            // 生成子步骤提示词
            const prompt = this.generateSubStepPrompt(stepInfo, this.workflowSteps.length - 1, stepInfo.actionList.length)

            // 将上一次执行结果和当前修改需求传递给LLM
            const modifyPrompt = `请根据以下原始页面数据和用户的修改需求，生成修改后的页面数据：

            ## 原始页面数据：
            ${JSON.stringify(this.lastExecutionResult, null, 2)}

            ## 用户修改需求：
            ${content}

            要求仅修改相应的内容。`

            // 更新当前步骤状态为进行中
            this.updateFlowSubStep(flowMessageIndex, 0, {
              inProgress: true,
              name: '修改页面内容'
            })

            try {
              const modifyResponse = await this.callLLMApi([
                { role: 'system', content: prompt },
                { role: 'user', content: modifyPrompt }
              ])

              if (stepInfo.output) {
                this.globalVariables[stepInfo.output.fieldName] = modifyResponse
              }

              console.log('修改后的页面数据:', modifyResponse)

              const modifiedData = this.parseOutputData(modifyResponse)

              // 更新执行结果
              this.lastExecutionResult = modifiedData

              // 更新步骤状态为完成
              this.updateFlowSubStep(flowMessageIndex, 0, {
                completed: true,
                inProgress: false,
                executionTime: '1.0',
                response: '页面修改完成',
                name: '修改页面内容'
              })

              // 更新整个流程状态
              this.updateFlowStep(flowMessageIndex, {
                flowTitle: '页面修改完成',
                completed: true,
                inProgress: false
              })

              // 触发结果事件
              this.$emit('onAgentMessage', {
                type: 'update',
                result: modifiedData,
              })
            } catch (error) {
              this.updateFlowSubStep(flowMessageIndex, 0, {
                error: error.message || '修改失败',
                inProgress: false,
                name: '修改页面内容'
              })
            }
          } else {
            // 原有的页面创建流程
            const flowMessageIndex = this.addFlowMessage('执行微页面制作流程', stepInfo.actionList)
            // 执行所有子步骤
            await this.executeFlowSteps(stepInfo, flowMessageIndex)
          }

          this.setLoading(false)
        } else if (stepInfo.actionType === 'guide') {
          this.conversationHistory.push({
            role: 'user',
            content: message
          })
          //引导节点需要存储会话记录
          const messages = [
            { role: 'system', content: this.getSystemPrompt() },
            ...this.conversationHistory
          ]
          console.log('guide messages:', messages);
          const fullContent = await this.callLLMApi(messages)

          if (stepInfo.output) {
            this.globalVariables[stepInfo.output.fieldName] = fullContent
          }

          this.conversationHistory.push({
            role: 'assistant',
            content: fullContent
          })
          //如果不含有需要补充内容信息则往下执行
          if (!fullContent.includes('麻烦您提供一下这些信息')) {
            this.currentStep++
            this.startWorkflow(fullContent)
            return
          }
          this.setLoading(false)
        } else {
          // 调用LLM API获取初始响应
          await this.callLLMApi([
            { role: 'system', content: this.getSystemPrompt() },
            { role: 'user', content: message }
          ])
          this.setLoading(false)
        }

      } catch (error) {
        console.error('startWorkflow error:', error);
        this.addAiMessage(`启动工作流失败: ${error.message || '未知错误'}`)
        this.setLoading(false)
        this.workflowError = true
      }
    },

    /**
     * 执行flow类型步骤的子步骤
     */
    async executeFlowSteps(flowStep, flowMessageIndex) {
      if (!flowStep.actionList?.length) return;

      const stepStartTime = performance.now();
      let outputData = '';
      try {
        // 初始化所有子步骤的状态
        flowStep.actionList.forEach((subStep, index) => {
          this.updateFlowSubStep(flowMessageIndex, index, {
            inProgress: false,
            completed: false,
            error: null,
            name: `${subStep.name}`,
            response: null,
            executionTime: 0,
            retryCount: 0 // 添加重试次数记录
          })
        })

        // 创建任务队列
        const taskQueue = flowStep.actionList.map((subStep, index) => ({
          subStep,
          index,
          executed: false,
          retryCount: 0 // 添加重试次数记录
        }))

        // 执行队列中的任务
        while (taskQueue.some(task => !task.executed)) {
          // 获取下一个未执行的任务
          const currentTask = taskQueue.find(task => !task.executed)
          if (!currentTask) break

          const { subStep, index } = currentTask

          // 更新当前步骤状态为进行中
          this.updateFlowSubStep(flowMessageIndex, index, {
            inProgress: true,
            error: '',
            name: `${subStep.name}${currentTask.retryCount > 0 ? ` (${currentTask.retryCount}/${this.maxRetries})` : ''}`
          })

          try {
            const subStepStartTime = performance.now()

            // 生成子步骤提示词
            const prompt = this.generateSubStepPrompt(subStep, index, flowStep.actionList.length)

            if (subStep.actionType !== 'done') {
              // 调用LLM API
              const response = await this.callLLMApi([
                { role: 'system', content: prompt },
                ...this.conversationHistory
              ])

              if (subStep.output) {
                this.globalVariables[subStep.output.fieldName] = response
              }

              if (subStep.validator) {
                try {
                  const res = this.parseOutputData(response)
                  if (!subStep.validator(res)) {
                    // 更新重试状态
                    currentTask.retryCount++

                    // 检查是否超过最大重试次数
                    if (currentTask.retryCount >= this.maxRetries) {
                      throw new Error(`验证失败，已达到最大重试次数(${this.maxRetries}次)`)
                    }

                    // 更新子步骤状态
                    this.updateFlowSubStep(flowMessageIndex, index, {
                      inProgress: false,
                      error: '验证未通过，准备重试...',
                      name: `${subStep.name}`
                    })

                    // 等待一段时间后重试
                    await new Promise(resolve => setTimeout(resolve, 1000))
                    continue // 重新执行当前任务
                  }
                } catch (validationError) {
                  // 更新重试状态
                  currentTask.retryCount++

                  // 检查是否超过最大重试次数
                  if (currentTask.retryCount >= this.maxRetries) {
                    throw new Error(`验证过程多次失败: ${validationError.message}`)
                  }

                  // 更新子步骤状态
                  this.updateFlowSubStep(flowMessageIndex, index, {
                    inProgress: false,
                    error: `验证未通过，准备重试...`,
                    name: `${subStep.name}`
                  })

                  // 等待一段时间后重试
                  await new Promise(resolve => setTimeout(resolve, 1000))
                  continue // 重新执行当前任务
                }
              }

              console.log(response, 1111, flowMessageIndex, index);
              if (subStep.actionType === 'imageGen') {
                let imageGenResponse = []
                try {
                  imageGenResponse = this.parseOutputData(response)
                } catch (error) {
                  console.error('解析图片生成响应失败:', error)
                }
                console.log(imageGenResponse, 2222);
                if (imageGenResponse instanceof Array && imageGenResponse.length > 0) {
                  //使用promise.all通过图片生成接口生成图片
                  const imagePromises = imageGenResponse.map(async item => {
                    try {
                      console.log(`开始生成图片 - 提示词: ${item.prompt}`)
                      
                      const xhr = new AbortController()
                      // 生成图片
                      const images = await generateImage({
                        prompt: item.prompt,
                        size: `${item.width}x${item.height}`,
                        quality: 'low',
                        n: 1,
                      }, { signal: xhr.signal })

                      console.log(`图片生成成功 - 数量: ${images.length}`)

                      // 将base64图片转换为文件并上传
                      const uploadedImages = []
                      for (let i = 0; i < images.length; i++) {
                        const base64Image = images[i]
                        try {
                          console.log(`开始处理图片 ${i + 1}/${images.length}`)
                          
                          // 转换为文件
                          const fileName = `generated_image_${Date.now()}_${i}.png`
                          const file = b64toFile(base64Image, fileName)
                          console.log(`文件转换成功 - 文件名: ${fileName}, 大小: ${file.size}`)
                          
                          // 上传到文件服务
                          const uploadResults = await getTNPath([file], { cdn: true })
                          if (uploadResults && uploadResults.length > 0) {
                            const uploadResult = uploadResults[0]
                            console.log(`文件上传成功 - Path: ${uploadResult.path}`)
                            
                            uploadedImages.push({
                              id: item.id,
                              path: uploadResult.path,
                              name: uploadResult.name,
                              ext: uploadResult.ext,
                              size: uploadResult.size,
                              width: item.width,
                              height: item.height,
                              base64: base64Image, // 保留base64用于预览
                              originalPrompt: item.prompt // 保留原始提示词
                            })
                          } else {
                            console.error(`文件上传失败 - 未获取到上传结果`)
                          }
                        } catch (uploadError) {
                          console.error(`处理图片失败 - 图片 ${i + 1}:`, uploadError)
                        }
                      }

                      console.log(`图片处理完成 - 成功上传: ${uploadedImages.length}/${images.length}`)

                      return {
                        id: item.id,
                        images: uploadedImages,
                        success: true,
                        totalGenerated: images.length,
                        totalUploaded: uploadedImages.length
                      }
                    } catch (error) {
                      console.error(`图片生成失败:`, error)
                      // 返回错误信息而不是抛出，这样其他图片的生成不会被中断
                      return {
                        error: true,
                        id: item.id,
                        message: error.message || '图片生成失败'
                      }
                    }
                  })

                  const imageResults = await Promise.all(imagePromises)
                  
                  // 处理生成结果
                  const successImages = imageResults.filter(result => !result.error)
                  const failedImages = imageResults.filter(result => result.error)
                  
                  // 统计信息
                  const totalRequested = imageGenResponse.length
                  const totalSuccessful = successImages.length
                  const totalGenerated = successImages.reduce((sum, result) => sum + (result.totalGenerated || 0), 0)
                  const totalUploaded = successImages.reduce((sum, result) => sum + (result.totalUploaded || 0), 0)
                  
                  console.log(`图片生成统计:`, {
                    totalRequested,
                    totalSuccessful,
                    totalGenerated,
                    totalUploaded,
                    failedCount: failedImages.length
                  })
                  
                  // 如果有失败的图片，记录日志
                  if (failedImages.length > 0) {
                    console.warn('部分图片生成失败:', failedImages)
                  }

                  // 将成功生成的图片展平为数组，返回包含path的完整信息
                  outputData = successImages.reduce((acc, result) => {
                    if (result.images && Array.isArray(result.images)) {
                      acc.push(...result.images)
                    }
                    return acc
                  }, [])
                  const outputDataStr = JSON.stringify(outputData.map(img => ({
                    id: img.id,
                    width: img.width,
                    height: img.height,
                    path: img.path,
                    name: img.name,
                    size: img.size,
                    title: img.originalPrompt,
                    url: FS.util.getFscLink(img.path)
                  })))

                  console.log(`最终输出数据:`, {
                    imageCount: outputData.length,
                    images: outputData.map(img => ({
                      id: img.id,
                      path: img.path,
                      name: img.name,
                      size: img.size,
                      url: FS.util.getFscLink(img.path)
                    }))
                  })
                  outputData = outputDataStr
                  
                  console.log(outputDataStr, 3333);
                  if (subStep.output) {
                    //将outputData转换为json字符串，放到全局变量方便后续提示词中引用
                    this.globalVariables[subStep.output.fieldName] = outputDataStr
                  }
                }
                
              } else {
                outputData = response;
              }
            }

            // 更新步骤状态为完成
            const executionTime = ((performance.now() - subStepStartTime) / 1000).toFixed(1)

            this.updateFlowSubStep(flowMessageIndex, index, {
              completed: true,
              inProgress: false,
              executionTime,
              response: outputData,
              name: `${subStep.name}`
            })

            // 标记任务为已执行
            currentTask.executed = true

            // 等待一段时间再执行下一个任务
            await new Promise(resolve => setTimeout(resolve, 1000))

          } catch (error) {
            console.error(`子步骤 ${index + 1} 执行失败:`, error)

            // 更新步骤错误状态
            this.updateFlowSubStep(flowMessageIndex, index, {
              error: error.message || '子步骤执行失败',
              inProgress: false,
              name: `${subStep.name}`
            })

            throw new Error(`${subStep.name} 执行失败:${error.message}`);
            return
          }
        }

        // 所有步骤执行完成
        const totalExecutionTime = ((performance.now() - stepStartTime) / 1000).toFixed(1);
        this.updateFlowStep(flowMessageIndex, {
          flowTitle: '微页面制作完成',
          completed: true,
          inProgress: false,
          executionTime: totalExecutionTime
        });
        const result = this.parseOutputData(outputData);
        // 保存执行结果
        this.lastExecutionResult = result;
        console.log('执行结果已保存:', this.lastExecutionResult);

        if(this.isCreateHomePage){
          this.$emit('onAgentMessage', {
            type: 'update',
            result,
          });
          this.isCreateHomePage = false;
        } else {
          this.$emit('onAgentMessage', {
            type: 'create',
            result,
          });
        }

      } catch (error) {
        console.error('执行流程失败:', error)

        // 更新步骤错误状态
        this.updateFlowStep(flowMessageIndex, {
          error: error.message || '步骤执行失败',
          inProgress: false
        })
      }
    },

    /**
     * 获取系统提示词
     */
    getSystemPrompt() {
      const basePrompt = this.systemPrompt

      // 如果没有工作流步骤或当前步骤超出范围，则只返回基础提示词
      if (!this.workflowSteps.length || this.currentStep >= this.workflowSteps.length) {
        return basePrompt
      }

      const currentStepInfo = this.workflowSteps[this.currentStep]
      let prompt = currentStepInfo.prompt
      if (!prompt) {
        return basePrompt
      }
      //默认对全局变量进行替换
      Object.keys(this.globalVariables).forEach(key => {
        prompt = prompt.replace(`{{${key}}}`, typeof this.globalVariables[key] === 'object' ? JSON.stringify(this.globalVariables[key]) : this.globalVariables[key])
      })
      return prompt
    },

    /**
     * 生成子步骤提示词
     */
    generateSubStepPrompt(subStep, index, total) {
      let prompt = subStep.prompt
      if (!prompt) {
        return ''
      }
      //默认对全局变量进行替换
      Object.keys(this.globalVariables).forEach(key => {
        prompt = prompt.replace(`{{${key}}}`, typeof this.globalVariables[key] === 'object' ? JSON.stringify(this.globalVariables[key]) : this.globalVariables[key])
      })
      return `${prompt || ''}`
    },


    /**
     * 格式化执行时间
     */
    formatTime(time) {
      if (!time) return '0.0s';
      // 确保时间格式为X.Xs
      return `${parseFloat(time).toFixed(1)}s`;
    },

    /**
     * 解析输出数据
     */
    parseOutputData(content) {
      try {
        // 如果content本身就是对象，直接返回
        if (typeof content === 'object' && content !== null) {
          return content;
        }

        // 确保content是字符串
        const contentStr = String(content).trim();
        // try {
        //   return JSON.parse(contentStr);
        // } catch (error) {
        //   console.log(error, 1111);
        // }

        // 提取代码块中的JSON内容
        const codeBlockMatch = contentStr.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
        if (codeBlockMatch) {
          const jsonStr = codeBlockMatch[1].trim();
          return JSON.parse(jsonStr);
        }

        // 如果没有代码块，尝试直接解析
        return JSON.parse(contentStr);
      } catch (error) {
        console.error('解析输出数据失败:', error, content);
        throw new Error('解析JSON数据失败，请检查格式');
      }
    },


    // 通用意图识别方法，支持多种场景的意图判断
    async detectUserIntent(message, scene = 'general') {
      const systemPrompts = {
        general: `你是一个意图判断助手。你的任务是判断用户的意图类型。
        请根据用户输入和历史对话记录，判断用户想要做什么，并返回对应的意图。
严格要求：
1. 只能回复以下JSON格式之一：
   {"intent": "create", "action": "create_page"} - 表示用户想创建新页面
   {"intent": "modify", "action": "update_page"} - 表示用户想修改现有页面
   {"intent": "confirm", "action": "proceed"} - 表示用户确认继续当前流程
   {"intent": "supplement", "action": "add_info"} - 表示用户在补充信息
   {"intent": "reject", "action": "stop"} - 表示用户拒绝或要停止
2. 判断依据：
   - 当用户描述要制作、创建、生成新页面或者说一个新页面名字时，返回 create 意图
   - 当用户描述要修改、更新、调整现有页面时，返回 modify 意图
   - 当用户表达同意、确认、继续、下一步、开始创建的意思时，返回 confirm 意图
   - 当用户在补充、添加、完善信息时，返回 supplement 意图
   - 其他情况（包括拒绝、否定）返回 reject 意图`,
      }

      const messages = [
        { role: 'system', content: systemPrompts[scene] },
        ...this.conversationHistory,
        { role: 'user', content: message }
      ]

      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.api.key}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: messages,
          temperature: this.temperature,
          max_tokens: 4096,
          stream: false
        })
      })

      const data = await response.json()
      try {
        return JSON.parse(data.choices[0].message.content)
      } catch (error) {
        console.error('Intent parsing error:', error)
        return { intent: 'unknown', action: 'none' }
      }
    },

    /**
     * 调用LLM API
     */
    async callLLMApi(messages) {
      try {
        // 调用API
        const response = await fetch(this.apiEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${AI_CONFIG.api.key}`
          },
          body: JSON.stringify({
            model: this.model,
            messages: messages,
            temperature: this.temperature,
            max_tokens: 4096,
            stream: true
          })
        })

        if (!response.ok) {
          const errorBody = await response.text()
          console.error('API Error Response:', errorBody)
          throw new Error(`API 调用失败: ${response.status} ${response.statusText}`)
        }

        // 处理流式响应
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let streamBuffer = '' // 用于累积解码后的数据块
        let fullContent = '' // 用于存储完整的响应内容

        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          streamBuffer += decoder.decode(value, { stream: true }) // 解码并累积

          // 查找最后一个换行符，处理所有完整的行
          let lastNewlineIndex = streamBuffer.lastIndexOf('\n')
          if (lastNewlineIndex !== -1) {
            const linesToProcess = streamBuffer.substring(0, lastNewlineIndex)
            streamBuffer = streamBuffer.substring(lastNewlineIndex + 1) // 保留不完整的行部分

            const lines = linesToProcess.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonData = line.slice(6).trim() // 去除可能的空白字符
                if (jsonData === '[DONE]') continue
                if (!jsonData) continue // 跳过空的 data 行

                try {
                  const parsedData = JSON.parse(jsonData)
                  const content = parsedData.choices[0]?.delta?.content || ''

                  // 发送文本进行打字效果显示
                  if (content) {
                    this.handleStreamContent(content)
                  }

                  // 累积完整内容
                  fullContent += content
                } catch (e) {
                  console.warn('解析流数据中的 JSON 出错:', e, '原始数据:', jsonData)
                }
              }
            }
          }
        }

        // 处理可能残留在缓冲区中的最后一部分
        if (streamBuffer.startsWith('data: ')) {
          const jsonData = streamBuffer.slice(6).trim()
          if (jsonData && jsonData !== '[DONE]') {
            try {
              const parsedData = JSON.parse(jsonData)
              const content = parsedData.choices[0]?.delta?.content || ''
              if (content) {
                this.handleStreamContent(content)
              }
              fullContent += content
            } catch (e) {
              console.warn('解析流末尾数据出错:', e, '原始数据:', jsonData)
            }
          }
        }

        return fullContent
      } catch (error) {
        console.error('API调用失败:', error)
        throw new Error(`API调用失败: ${error.message || '未知错误'}`)
      }
    },

    /**
     * 更新流程消息的步骤状态
     */
    updateFlowStep(stepIndex, updates) {
      // 获取当前消息的完整副本
      const message = { ...this.messages[stepIndex] }

      // 合并更新
      Object.keys(updates).forEach(key => {
        this.$set(message, key, updates[key])
      })

      // 更新整个消息对象
      this.$set(this.messages, stepIndex, message)

      // 确保视图更新
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    /**
     * 更新流程消息的子步骤状态
     */
    updateFlowSubStep(stepIndex, subStepIndex, updates) {
      // 获取当前消息的完整副本
      const message = { ...this.messages[stepIndex] }

      // 确保steps数组存在
      if (!message.steps) {
        message.steps = []
      }

      // 获取当前步骤的完整副本
      const step = { ...message.steps[subStepIndex] }

      // 合并更新
      Object.keys(updates).forEach(key => {
        this.$set(step, key, updates[key])
      })

      // 更新步骤
      this.$set(message.steps, subStepIndex, step)

      // 更新整个消息对象
      this.$set(this.messages, stepIndex, message)

      // 确保视图更新
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    /**
     * 添加流程类型消息
     */
    addFlowMessage(flowTitle, steps = []) {
      const message = {
        role: 'assistant',
        type: 'flow',
        flowTitle: flowTitle,
        steps: steps.map(step => ({
          ...step,
          expanded: false,
          response: null,
          executionTime: 0,
          completed: false,
          inProgress: false,
          error: null
        })),
        error: null,
        inProgress: true,
        completed: false,
        executionTime: 0,
        timestamp: Date.now()
      }

      return this.addMessage(message)
    },
  }
}

</script>

<style lang="less" module>
.base-agent-chat {
  height: 100%;
}

.chat_container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  background-image: url(https://a2.fspage.com/FSR/weex/avatar/marketing_app/images/chat-bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top right;
  overflow: hidden;
}

.message_list {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.message_list_inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    border-radius: 2px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(144, 147, 153, 0.5);
    }
  }

  /* Firefox滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(144, 147, 153, 0.3) transparent;
}

.message_item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.avatar {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: none;
  }
}

.message_content {
  flex: 1;
  max-width: calc(100% - 64px);
}

.message_text {
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  word-break: break-word;
  line-height: 1.5;
  user-select: text;

  a {
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.user_message {
  flex-direction: row-reverse;

  .avatar {
    margin-right: 0;
    margin-left: 12px;
  }

  .message_text {
    background: #e6f7ff;
  }
}

.input_area {
  position: relative;
  /* 改为相对定位 */
  margin: 0 20px 25px;
  display: flex;
  align-items: flex-start;
  padding: 1px;
  border-top: 1px solid #e8e8e8;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
  border-radius: 8px;
  /* 毛玻璃背景 */
  backdrop-filter: blur(10px);
  background: linear-gradient(100deg, #09f 0, #a033ff 50%, #ff5280 75%, #ff7061 100%);

  /* 全局样式 */
  :global(.fx-input) textarea {
    border-radius: 8px;
    min-height: 50px !important;
  }

  :global(.fx-input) textarea::placeholder {
    color: #666;
  }

  :global {
    .el-textarea__inner {
      border: 0;
    }
  }
}

.input {
  flex: 1;
  width: 100%;
}

.send_button {
  margin-left: 12px;
  padding: 0;
  flex-shrink: 0;
  font-size: 20px;
  align-self: end;
  position: absolute !important;
  right: 10px;
  bottom: 10px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);

  &_active {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading_message {
  display: flex;
  align-items: flex-start;
  margin-top: 20px;
}

.loading_dots {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  span {
    width: 6px;
    height: 6px;
    margin: 0 2px;
    background: #1890ff;
    border-radius: 50%;
    animation: dot-flashing 1s infinite linear alternate;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

@keyframes dot-flashing {
  0% {
    opacity: 0.2;
  }

  100% {
    opacity: 1;
  }
}

/* 任务卡片样式 - 重新设计与截图一致 */
.task_cards_container {
  margin-top: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-size: 14px;
}

.workflow_header {
  padding: 12px 16px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  display: flex;
  align-items: center;

  span {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-left: 6px;
  }

  .loading_icon {
    margin-right: 8px;
    color: #409EFF;
    animation: spinning 1.2s linear infinite;
  }
}

.task_list {
  padding: 8px 0;
}

.task_row {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  position: relative;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 28px;
    bottom: -8px;
    width: 1px;
    height: 16px;
    background-color: #EBEEF5;
  }
}

.execution_time {
  color: #67C23A;
  font-size: 12px;
  text-align: right;
}

.task_icon {
  width: 24px;
  text-align: center;
  margin-left: 5px;
  margin-right: -5px;

  i {
    font-size: 12px;
  }
}

.task_name {
  flex: 1;
  color: #303133;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task_time {
  color: #909399;
  font-size: 12px;
  margin-left: 12px;
  min-width: 40px;
  text-align: right;
}

.workflow_message {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  color: #F56C6C;
  background-color: #FEF0F0;
  font-size: 13px;
  border-top: 1px solid #FCDEE0;

  .error_icon {
    margin-right: 8px;
  }
}

/* 展开的子步骤容器 */
.sub_task_container {
  padding-left: 24px;
  margin: 4px 0 8px;
}

.sub_task_row {
  display: flex;
  align-items: center;
  padding: 8px 16px 8px 24px;
  position: relative;
  font-size: 13px;

  &::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 15px;
    width: 6px;
    height: 1px;
    background-color: #DCDFE6;
  }
}

/* 图标状态样式 */
.success_icon {
  color: #67C23A;
}

.error_icon {
  color: #F56C6C;
}

.progress_icon {
  color: #409EFF;
}

.waiting_icon {
  color: #909399;
}

.spinning {
  animation: spinning 1.2s linear infinite;
}

.expand_toggle {
  margin-right: 8px;
  cursor: pointer;
  color: #909399;
  font-size: 12px;
  transition: all 0.3s;

  &:hover {
    color: #409EFF;
  }
}

.expand_icon {
  transform: rotate(0deg);
}

.collapse_icon {
  transform: rotate(90deg);
}

@keyframes spinning {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
