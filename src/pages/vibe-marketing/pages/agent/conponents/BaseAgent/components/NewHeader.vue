<template>
  <div :class="{ [$style.vibeMarketingHeader]: true, [$style.isChatOpen]: isChatOpen }">
    <div :class="$style.vibeMarketingHeader__left">
      <div :class="$style.vibeMarketingHeader__title">
        AI创意工坊
      </div>
    </div>
    <div :class="$style.vibeMarketingHeader__right">
      <div :class="$style.vibeMarketingHeader__assets">
        <fx-popover
          placement="bottom"
          :arrowOffset="50"
          width="400"
          trigger="click"
        >
          <div :class="$style.vibeMarketingHeader__assets__popover">
            <div :class="$style.vibeMarketingHeader__assets__popover__item">assets</div>
          </div>
          <div slot="reference" :class="$style.vibeMarketingHeader__assets_content">
            <img :class="$style.icon" :src="assetsIconMap.micropage" alt="assets" />
            微页面制作
            <img :class="$style.triangle" :src="iconTriangleDown" alt="triangle down" />
          </div>
        </fx-popover>
      </div>
      <div :class="$style.vibeMarketingHeader__full_screen" @click="handleFullScreen">
        <img :src="isFullScreen ? iconCloseFullScreen : iconOpenFullScreen" alt="full screen" />
        <span>{{ isFullScreen ? '退出全屏' : '全屏' }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import iconOpenFullScreen from '@/assets/images/icon-open-fullscreen.svg'
import iconCloseFullScreen from '@/assets/images/icon-close-fullscreen.svg'
import iconWorkspaceSite from '@/assets/images/icon-workspace-site.svg'
import iconTriangleDown from '@/assets/images/icon-triangle-down.svg'

const assetsIconMap = {
  micropage: iconWorkspaceSite,
}

export default {
  name: 'NewHeader',
  props: {
    isChatOpen: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      iconOpenFullScreen,
      iconCloseFullScreen,
      iconTriangleDown,
      assetsIconMap,
      isFullScreen: false,
    }
  },
  methods: {
    handleFullScreen() {
      console.log('handleFullScreen')
      this.isFullScreen = !this.isFullScreen

      const menuDom = document.querySelector('.yxt-app .g-menu-wrapper')
      if (menuDom) {
        menuDom.style.display = this.isFullScreen ? 'none' : 'block'
      }
    },
  }
}
</script>

<style lang="less" module>
.vibeMarketingHeader {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 12px;

  &.isChatOpen {
    .vibeMarketingHeader__left {
      width: 375px;
    }
  }

  .vibeMarketingHeader__left {
    width: auto;
    font-size: 15px;
    color: #181C25;
    line-height: 24px;
    display: flex;
    align-items: center;
  }

  .vibeMarketingHeader__right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .vibeMarketingHeader__assets_content {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      color: #181C25;
      line-height: 24px;

      img {
        width: 16px;
        height: 16px;
      }

      .icon {
        margin-right: 4px;
      }

      .triangle {
        margin-left: 4px;
      }
    }

    .vibeMarketingHeader__full_screen {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: absolute;
      right: 0;
      height: 50px;
      font-size: 13px;
      color: #181C25;
      line-height: 18px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
      }
    }
  }
}
</style>