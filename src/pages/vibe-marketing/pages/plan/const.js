export const menus = [
  {
    title: '市场活动',
    items: [
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '活动策划',
        desc: '描述描述描述',
      },
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '广告计划',
        desc: '描述描述描述',
      },
    ],
  },
  {
    title: '营销创意（收藏）',
    items: [
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '打造品牌故事',
        desc: '描述描述描述',
        tag: '分步骤',
      },
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '制作信息图',
        desc: '描述描述描述',
        tag: '分步骤',
      },
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '小红书爆款笔记',
        desc: '轻松掌握流量密码，打造热门笔记',
        tag: '分步骤',
      },
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '百家号文章',
        desc: '优质内容助力品牌传播',
        tag: '分步骤',
      },
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '朋友圈文案',
        desc: '捕捉生活点滴，分享温暖瞬问',
        tag: '分步骤',
      },
      {
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '公众号推文',
        desc: '事半功倍的宣传助手',
        tag: '分步骤',
      },
    ],
  },
  {
    title: '内容营销',
    items: [
      {
        id: 'seo',
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: 'SEO文章生成',
        desc: '描述描述描述',
        tag: '分步骤',
      },
      {
        id: 'article',
        workspaceType: 'content',
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '文章改写',
        desc: '描述描述描述',
        tag: '分步骤',
      },
      {
        id: 'poster',
        workspaceType: 'campaign',
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '海报生成',
        showTooltip: true,
        desc: '让AI为您打造专业级企业海报：一键生成，只需输入主题，AI即可智能生成多款海报设计；专业模板，涵盖企业宣传、产品推广、活动营销等多种场景；智能优化，自动调整布局、配色、字体，确保视觉效果；品牌定制，支持企业VI元素融入，保持品牌一致性；快速迭代，支持实时修改和优化，让创意快速落地。告别传统设计流程，让AI助您打造令人印象深刻的企业形象。',
        tag: '分步骤',
        clickEventName: 'handleCreateContent',
      },
      {
        id: 'site',
        workspaceType: 'content',
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '微页面制作',
        showTooltip: true,
        desc: '可以生成各种类型的落地页，包括但不限于：表单页面（如：线索收集、调查问卷等）、落地页（如：产品介绍、活动宣传等）、展示页面（如：团队介绍、公司简介等）。',
        tag: '分步骤',
        clickEventName: 'handleCreateContent',
      },
      {
        id: 'dashboard',
        icon: 'iconyonghuyunying',
        iconColor: '#7341de',
        name: '运营任务制作',
        desc: '描述描述描述',
        clickEventName: 'handleCreateDashboard',
      },
    ],
  },
]
