<template>
  <div :class="$style.vibeMarketingPlan">
    <div :class="$style.title">
      Vibe Marketing
    </div>
    <div :class="$style.tips">
      Idea to marketing in seconds, with your personal full stack expert
    </div>
    <div :class="$style.chatInput">
      <textarea
        v-model="chatInput"
        rows="5"
        cols="30"
        :class="$style.chatInputInput"
        placeholder="您可以问我任何问题"
      />
      <img
        :class="$style.chatInputButton"
        src="@/assets/images/vibe-marketing/send.png"
        @click="handleSend"
        @keydown.enter="handleSend"
      >
    </div>
    <div :class="$style.menus">
      <div
        v-for="menu in menus"
        :key="menu.title"
        :class="$style.menuItems"
      >
        <div :class="$style.menuItemTitle">
          {{ menu.title }}
        </div>
        <div :class="$style.itemWrapper">
          <div
            v-for="item in menu.items"
            :key="item.name"
            :class="$style.item"
            @click="handleMenuClick(item)"
          >
            <div :class="$style.itemTitle">
              <i
                :class="['iconfont', item.icon, $style.itemIcon]"
                :style="{ color: item.iconColor }"
              />
              <span>
                {{ item.name }}
              </span>
            </div>
            <fx-tooltip
              v-if="item.showTooltip"
              effect="light"
              :content="item.desc"
              placement="bottom"
            >
              <div :class="$style.itemDesc">
                {{ item.desc }}
              </div>
            </fx-tooltip>
            <div
              v-else
              :class="$style.itemDesc"
            >
              {{ item.desc }}
            </div>
            <div
              v-if="item.tag"
              :class="$style.itemTag"
            >
              {{ item.tag }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <ChooseTemplateDialog
      :visible="isShowChooseTemplateDialog"
      @update:visible="isShowChooseTemplateDialog = false"
      @update:submit="handleChooseTemplateSubmit"
    />
    <CreateKanbanDialog
      :default-data="createKanbanDefaultData"
      :visible="isShowCreateKanbanDialog"
      :updated-callback="handleCreateKanban"
      @update:visible="isShowCreateKanbanDialog = false"
    />
  </div>
</template>

<script>

import { mapActions } from 'vuex'
import CreateKanbanDialog from '@/pages/kanban/components/create-kanban-dialog/index.vue'
import ChooseTemplateDialog from '@/pages/kanban/components/choose-template-dialog/index.vue'

import { vibeMarketingWorkSpaceIconMap } from '@/utils/constant.js'

import http from '@/services/http/index.js'

import { menus } from './const.js'

export default {
  name: 'VibeMarketingPlan',
  components: {
    CreateKanbanDialog,
    ChooseTemplateDialog,
  },
  data() {
    const { enterpriseAccount } = FS.contacts.getCurrentEmployee() || {}
    return {
      isShowChooseTemplateDialog: false,
      isShowCreateKanbanDialog: false,
      createKanbanDefaultData: {},
      enterpriseAccount,
      chatSessionId: '',
      chatInput: '',
      menus,
    }
  },
  mounted() {
    this.listBoardTemplate()
    this.listEnterpriseBoardTemplate()
  },
  beforeDestroy() {
    if (this.marktingAIHelper) {
      this.marktingAIHelper.destroy()
    }
  },
  methods: {
    ...mapActions('Kanban', [
      'listBoardTemplate',
      'listEnterpriseBoardTemplate',
    ]),
    handleCreateDashboard() {
      this.isShowChooseTemplateDialog = true
    },
    // 新建看板
    handleChooseTemplateSubmit(data) {
      this.createKanbanDefaultData = { ...data, isTemplate: true }
      this.isShowCreateKanbanDialog = true
      this.$nextTick(() => {
        this.isShowChooseTemplateDialog = false
      })
    },
    // 新建看板完毕
    handleCreateKanban(kanbanId) {
      this.isShowCreateKanbanDialog = false
      this.$router.push({
        name: 'vibe-marketing-agent',
        query: {
          name: 'dashboard',
          id: kanbanId,
        },
      })
    },
    getChatCompleteResult(sessionId) {
      const url = `${window.location.origin}/FHS/EM8HAPPMARKETING/web/aiChat/sse/getChatCompleteResult`
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'text/event-stream',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: sessionId,
        }),
      })
        .then(async response => {
          if (!response.ok) {
            throw new Error(`连接失败：${response.status}`)
          }

          const reader = response.body.getReader()
          const decoder = new TextDecoder('utf-8')

          let buffer = ''

          while (true) {
            // eslint-disable-next-line no-await-in-loop
            const result = await reader.read()
            const { done, value } = result
            if (done) {
              console.log('SSE 连接已关闭')
              break
            }

            const chunk = decoder.decode(value, { stream: true })
            buffer += chunk

            // 拆分为多行，每行可能是 data: xxx
            const lines = buffer.split('\n')

            // eslint-disable-next-line no-restricted-syntax
            for (const line of lines) {
              if (line.startsWith('data:')) {
                const data = line.replace(/^data:\s*/, '')
                console.log('data', JSON.parse(data))
                // 你可以在这里 JSON.parse(data) 来解析
              }
            }

            // 如果最后一行不完整，保留它
            buffer = lines[lines.length - 1].includes('data:') ? '' : lines[lines.length - 1]
          }
        })
        .catch(error => {
          console.error('连接失败：', error)
        })
    },
    handleSessionResult(type) {
      console.log('type', type)
      const typeNumber = Number(type)
      if (typeNumber === 1) {
        this.$router.push({
          name: 'vibe-marketing-agent',
          query: {
            name: 'site',
          },
        })
      }
    },
    handleSend() {
      if (!this.chatInput) {
        this.$message.warning('请输入内容')
        return
      }
      http.chatCompleteWithoutSessionApp({
        prompt: this.chatInput,
        property: {
          businessName: 'vibe-marketing',
          debug: true,
          defaultHelperName: 'Copilot_829Y1__c',
          isNewApi: true,
          isStreaming: true,
        },
      }).then(res => {
        if (res.errCode === 0 && res.data) {
          this.getChatCompleteResult(res.data)
        }
      })
    },
    getWorkSpaceId(menu) {
      const {
        name, desc, workspaceType,
      } = menu

      if (menu.workspaceType) {
        return new Promise(resolve => {
          const employee = window.Fx.contacts.getCurrentEmployee()
          const employeeID = (employee && employee.employeeID) || ''
          const departmentIds = (employee && employee.departmentIds) || []

          const payload = {
            object_data: {
              object_describe_api_name: 'VMWorkspaceObj__c',
              record_type: 'default__c',
              icon__c: vibeMarketingWorkSpaceIconMap[workspaceType],
              summary__c: desc,
              works_space_type__c: workspaceType,
              name: `${name}_${Date.now()}`,
              owner: employeeID ? [`${employeeID}`] : [],
              created_by: employeeID ? [`${employeeID}`] : [],
              data_own_department: departmentIds.map(el => `${el}`),
            },
          }
          http.addVMWorkspaceObj(payload).then(res => {
            if (res.Result.StatusCode === 0) {
              const {
                objectData: {
                  _id,
                } = {},
              } = res.Value

              resolve(_id)
            }
            resolve('')
          })
        })
      }
      return Promise.resolve('')
    },
    async handleCreateContent(menu) {
      const { id } = menu

      const workspaceId = await this.getWorkSpaceId(menu)
      this.$router.push({
        name: 'vibe-marketing-agent',
        query: {
          name: id || 'site',
          workspaceId,
        },
      })
    },
    handleMenuClick(menu) {
      if (menu.clickEventName) {
        this[menu.clickEventName](menu)
      }
    },
  },
}
</script>

<style lang="less" module>
.vibeMarketingPlan {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 16px;

  .title {
    color: var(--color-neutrals19, #181C25);
    font-size: 28px;
    font-style: normal;
    font-weight: 700;
    line-height: 32px;
  }

  .tips {
    margin-top: 16px;
    color: var(--color-neutrals15, #545861);
    text-align: center;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .chatInput {
    position: relative;
    width: 100%;
    margin-top: 16px;
    border: 1px solid transparent;
    border-radius: 16px;
    padding: 12px;
    box-sizing: border-box;
    max-width: 1000px;
    /* 👇 双背景实现渐变边框 + 白色内部 */
    background: linear-gradient(white, white) padding-box,  /* 内层白色 */
      radial-gradient(110% 110% at 16.75% 100%, #0099FF 0%, #A033FF 60%, #FF5280 90%, #FF7061 100%) border-box;  /* 外层渐变边框 */
    background-origin: padding-box, border-box;
    background-clip: padding-box, border-box;

    .chatInputInput {
      width: calc(100% - 100px);
      border: 0;
    }

    .chatInputButton {
      position: absolute;
      cursor: pointer;
      right: 12px;
      bottom: 12px;
      width: 24px;
      height: 24px;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.12);
      }

      &:active {
        transform: scale(0.96);
      }
    }
  }

  .menus {
    margin-top: 40px;
    width: 100%;
    max-width: 1000px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .menuItems {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .menuItemTitle {
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 160% */
        color: var(--color-neutrals19, #181C25);

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 14px;
          background-color: var(--color-primary06,#ff8000);
          border-radius: 2px;
          margin-right: 6px;
        }
      }

      .itemWrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .item {
          position: relative;
          display: flex;
          width: 240px;
          padding: 16px;
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
          border-radius: 8px;
          border: 1px solid var(--color-neutrals04, #EAEBEE);
          background: #FFF;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            // background: #F8F9FF;
            // border-color: var(--color-primary06, #ff8000);
            box-shadow: 0 4px 12px rgba(255, 128, 0, 0.08);
            transform: translateY(-2px);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(255, 128, 0, 0.06);
          }

          .itemTitle {
            color: var(--color-neutrals19, #181C25);
            font-size: 15px;
            height: 24px;
            line-height: 1;
            font-style: normal;
            font-weight: 500;
            display: flex;
            align-items: center;
          }

          .itemIcon {
            margin-right: 8px;
            font-size: 18px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
          }

          .itemDesc {
            width: 100%;
            color: var(--color-neutrals11, #91959E);
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .itemTag {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            width: 52px;
            height: 25px;
            padding: 1px 4px;
            justify-content: center;
            align-items: center;
            border-radius: 0 8px 0 8px;
            background: #E6F4FF;
            color: #0C6CFF;
            font-size: 12px;
            line-height: 18px;
          }
        }
      }
    }
  }
}
</style>
