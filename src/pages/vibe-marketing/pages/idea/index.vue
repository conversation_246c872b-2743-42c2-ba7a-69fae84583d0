<template>
  <div :class="classPrefix">
    <div :class="`${classPrefix}__header`">
      <div :class="`${classPrefix}__header-wrap`">
        <div
          class="input-wrapper"
        >
          <div class="input-content">
            <fx-input
              v-model="keyword"
              class="input"
              :placeholder="$t('marketing_taro.commons.ss_e5f71f')"
              :size="size"
              clearable
              is-search
              :is-round="isRound"
              @on-search="handleConfirm"
              @keyup.native.enter="handleConfirm"
            />
          </div>
        </div>
        <fx-button
          size="small"
          type="primary"
          @click="handleCreateTmp"
        >
          新建创意
        </fx-button>
      </div>

      <div
        v-if="categoryList.length > 0"
        class="category"
      >
        <div
          v-for="item in categoryList"
          :key="item.itemCode"
          :class="['item', { active: item.itemCode === category }]"
          @click="handleCategorySelect(item)"
        >
          <img
            v-if="item.icon"
            class="icon"
            :src="item.icon"
          >
          {{ item.itemName }}
        </div>
      </div>
    </div>

    <div
      v-loading="showLoading"
      :class="[`${classPrefix}__content`, 'marketing-scrollbar-theme']"
    >
      <template v-if="resultData && resultData.length > 0">
        <div :class="`${classPrefix}__list`">
          <template-item
            v-for="item in resultData"
            :key="item.objectData._id"
            :prompt-detail="item"
            @openAiPosterDialog="handleOpenAiPosterDialog"
          />
        </div>
      </template>

      <Empty
        v-if="resultData.length === 0"
        class="empty"
        :title="$t('marketing.commons.zwsj_21efd8')"
      />
    </div>
    <div :class="`${classPrefix}__pagination`">
      <v-pagen
        :pagedata.sync="pageData"
        @change="handlePageChange"
      />
    </div>
    <AiPosterDialog ref="aiPosterDialog" />
  </div>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import http from '@/services/http/index.js'
import AiPosterDialog from '../../components/AiPosterDialog.vue'
import Empty from '@/components/common/empty.vue'
import VPagen from '@/components/kitty/pagen.vue'
import TemplateItem from '../../components/template-item.vue'

export default {
  components: {
    Empty,
    TemplateItem,
    VPagen,
    AiPosterDialog,
  },
  data() {
    return {
      classPrefix: 'ai-template__page',
      keyword: '',
      category: this.$route.query.category || 'ALL',
      categoryList: [],
      iconCollected: `${$cdnPath}/images/ai-icon-collected2.svg`,
      iconDocument: `${$cdnPath}/images/ai-icon-document2.svg`,
      showLoading: true,
      resultData: [],
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 40,
        pageSizes: [10, 20, 30, 40],
      },
    }
  },
  computed: {
    templateQuery() {
      return {
        selectFields: [
          'name',
          'category',
          'title',
          'content',
          'status',
          'last_modified_time',
          'aihelper_id',
          'step_split',
          'created_by',
          'number_of_collections',
          'scope_type',
        ],
        category: this.category,
        keyword: this.keyword,
      }
    },
  },
  async created() {
    await this.initCategoryList()
    this.initQueryList()
  },
  methods: {
    async initCategoryList() {
      const res = await http.queryObjFiledInfo({
        objectAPIName: 'PromptObj',
        objectFiledAPIName: 'category',
      })

      if (res && res.errCode === 0) {
        const { enumDetails = [] } = res.data || {}
        const defaultCate = [
          { itemName: $t('marketing_taro.commons.qb_a8b0c2'), itemCode: 'ALL' },
          { itemName: $t('marketing_taro.pkgs.wscd_47b582'), itemCode: 'COLLECTION', icon: this.iconCollected },
          { itemName: $t('marketing_taro.pkgs.wcjd_1b7ffa'), itemCode: 'CREATION', icon: this.iconDocument },
        ]
        this.categoryList = defaultCate.concat(enumDetails)
      }
    },
    async initQueryList() {
      this.showLoading = true
      try {
        const res = await http.aiChatPageQueryPrompt({
          pageSize: this.pageData.pageSize,
          pageNum: this.pageData.pageNum,
          ...this.templateQuery,
        })

        const {
          errCode,
          data: {
            pageNum = 1,
            result = [],
            totalCount = 0,
          } = {},
        } = res || {}
        if (errCode === 0) {
          this.resultData = result
          this.pageData.pageNum = pageNum
          this.pageData.totalCount = totalCount
        }

        this.showLoading = false
      } catch (error) {
        this.showLoading = false
      }
    },
    handleCategorySelect(activeCategory) {
      const { itemCode } = activeCategory
      if (itemCode !== this.category) {
        this.category = itemCode
        this.initQueryList()
      }
    },
    handleConfirm(e) {
      this.initQueryList()
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.initQueryList()
    },
    handleCreateTmp() {
      this.$router.push({
        name: 'vibe-marketing-create-tmp',
      })
    },
    handleOpenAiPosterDialog(objectData) {
      this.$refs.aiPosterDialog.show(objectData)
    }
  },
}
</script>

<style lang="less" scoped>
.ai-template__page {
  height: 100%;
  background: linear-gradient(15deg, #fcf8ff .43%, #f0f9ff 46.4%, #f2f7ff 66.11%, #ebe8f9 98.52%);
  display: flex;
  flex-direction: column;

  &__header {
    padding: 20px 12px 12px;

    .input-wrapper {
      flex: 1;
      border-radius: 6px;
      background: linear-gradient(270deg, #ACB0FA 0%, #98CCFC 49.5%, #C58EF8 100%);
      padding: 1px;
      max-width: 800px;
      margin-right: 40px;
    }
    .input-content {
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFF;
      border-radius: 6px;
      height: 36px;
      flex: 1;

      .input {
        font-size: 14px;
        flex: 1;

        /deep/ .el-input__inner {
          border: none;
          background: transparent;
        }
      }
    }

    .category {
      font-size: 0;
      margin-top: 2px;
      display: flex;
      flex-wrap: wrap;

      .item {
        border: 0.5px solid #FFF;
        background: #FFF;
        border-radius: 6px;
        height: 28px;
        line-height: 28px;
        padding: 0 9px;
        display: flex;
        align-items: center;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        color: #181C25;
        margin: 10px 10px 0 0;
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

        &:hover {
          color: #455EEE;
        }

        &.active {
          color: #455EEE;
          background: #E6EDFF;
          border-color: #5B70EA;
        }

        .icon {
          width: 16px;
          height: 16px;
          margin: 0 4px 0 0;
        }
      }
    }
  }

  &__header-wrap {
    display: flex;
    justify-content: space-between;
  }

  &__content {
    overflow-y: auto;
    flex: 1;

    &.center {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .empty {
      position: static;
      height: 25vh;
      background-color: transparent;
    }

    .ai-template__page__list {
      display: flex;
      flex-wrap: wrap;
      margin: 12px 12px 0;
    }
  }
}
</style>
