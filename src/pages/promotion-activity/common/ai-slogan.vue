<template>
  <div :class="$style['ai-slogan']">
    <div
      :class="{
        [$style.content]: true,
        [$style.active]: isGenerating,
      }"
    >
      <div :class="$style['textarea-wrap']">
        <fx-input
          :value="value"
          type="textarea"
          :rows="3"
          resize="none"
          :placeholder="placeholder"
          :maxlength="maxLength"
          :readonly="isGenerating"
          @input="handleInput"
        />
        <span :class="$style.count">{{ value.length }}/{{ maxLength }}</span>
        <div
          v-if="isGenerating"
          :class="$style['ai-slogan-loading']"
        >
          <img
            :src="avatarIcon"
            :class="$style.avatar"
          >
          <span>{{ $t('marketing.pages.promotion_activity.zzsc_6cd6a8') }}</span>
          <img
            :src="aiLoading"
            :class="$style.loading"
          >
        </div>
      </div>
    </div>
    <div
      v-if="vDatas.uinfo.marketingAIPluginEnable"
      :class="$style.footer"
    >
      <div
        v-if="!refreshVisible"
        :class="$style.option"
        @click="handleGenerate"
      >
        <img :src="avatarIcon">
        <span :class="$style.name">{{ $t('marketing.pages.promotion_activity.bx_864467') }}</span>
      </div>
      <div
        v-else
        :class="$style.option"
        @click="handleGenerate"
      >
        <img :src="refreshIcon">
        <span :class="$style.name">
          {{ $t('marketing.pages.promotion_activity.hyj_a56f58') }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import kisvData from '@/modules/kisv-data.js'
import { $cdnPath } from '@/utils/constant.js'

const TIMEOUT = 3 * 60 * 1000 // 3分钟

export default {
  name: 'AiSlogan',
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: $t('marketing.commons.qsr_02cc4f'),
    },
    maxLength: {
      type: Number,
      default: 255,
    },
    sloganMaterial: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      avatarIcon: `${$cdnPath}/images/ai-slogan-avatar.svg`,
      refreshIcon: `${$cdnPath}/images/ai-slogan-refresh.svg`,
      aiLoading: `${$cdnPath}/images/ai-loading.gif`,
      refreshVisible: false,
      isGenerating: false,
      vDatas: kisvData.datas,
    }
  },
  mounted() {
    this.pollingStart = Date.now()
    this.pollingRef = null
  },
  methods: {
    handleInput(event) {
      this.$emit('input', event)
    },
    handleGenerate() {
      if (!this.sloganMaterial) {
        FxUI.Message.warning($t('marketing.pages.promotion_activity.qxxznr_39f3a2'))
        return
      }

      const {
        materialInfos = [],
        campaignName = '',
        objectId = '',
        objectType = '',
      } = this.sloganMaterial || {}

      if (
        !(materialInfos && materialInfos.length)
        && !objectId
      ) {
        FxUI.Message.warning($t('marketing.pages.promotion_activity.qxxznr_39f3a2'))
        return
      }

      if (this.isGenerating) return

      this.isGenerating = true
      const payload = {
        business: 'marketing',
        campaignName,
        promotionSlogan: this.value,
      }

      if (materialInfos && materialInfos.length) {
        payload.materialInfos = [...materialInfos]
      }

      if (objectId) {
        payload.objectId = objectId
        payload.objectType = objectType
      }

      http.aiChatPromptCompletions(payload)
        .then(res => {
          const { errCode, errMsg = '', data } = res
          const { taskId } = data || {}
          if (errCode !== 0 || !taskId) {
            this.isGenerating = false
            FxUI.Message.warning(errMsg || $t('marketing.pages.promotion_activity.sctghssb_92c5ce'))
            return
          }

          this.pollingStart = Date.now()
          this.polling(taskId)
        })
        .catch(() => {
          this.isGenerating = false
          FxUI.Message.warning($t('marketing.pages.promotion_activity.sctghssb_92c5ce'))
        })
    },
    polling(taskId) {
      const now = Date.now()
      if (now - this.pollingStart > TIMEOUT) {
        this.isGenerating = false
        FxUI.Message.warning($t('marketing.pages.promotion_activity.qqcs_bdf53f'))
        clearTimeout(this.pollingRef)
        return
      }

      http.aiChatPromptCompletions({ taskId })
        .then(res => {
          const { errCode, data } = res
          if (errCode === 0) {
            const { status, message = '' } = data || {}
            if (status === 2 || (status === 3 && !message)) {
              FxUI.Message.warning($t('marketing.pages.promotion_activity.sctghssb_92c5ce'))
              this.isGenerating = false
              return
            }

            if (status === 3 && message) {
              this.isGenerating = false
              this.refreshVisible = true
              this.$emit('input', message)
              return
            }

            this.pollingRef = setTimeout(() => {
              this.polling(taskId)
            }, 2000)
          }
        })
        .catch(() => {
          this.pollingRef = setTimeout(() => {
            this.polling(taskId)
          }, 2000)
        })
    },
  },
}
</script>

<style lang="less" module>
@keyframes bg {
  0% {
    --rotate: 0deg;
  }
  100% {
    --rotate: 360deg;
  }
}

@property --rotate {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

.ai-slogan {

  .content {
    background: #C1C5CE;
    // background: var(--color-neutrals05, #dee1e8);
    border-radius: 4px;
    padding: 1px;

    &:hover {
      background: #A3A7B0;
    }

    &:focus-within {
      background: #ff8000;
      background: var(--color-primary06, #ff8000);
    }

    &.active {
      background: linear-gradient(var(--rotate), #0099FF 0%, #A033FF 57.04%, #FF5280 87.77%, #FF7061 100%);
      animation: bg 2.5s linear infinite;
      animation-delay: 0s;
    }

    .textarea-wrap {
      position: relative;
      background: #FFF;
      border-radius: 4px;
      overflow: hidden;

      textarea {
        border: none;
      }

      .count {
        font-size: 13px;
        color: #c1c5ce;
        position: absolute;
        right: 5px;
        top: 45px;
      }

      .ai-slogan-loading {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: #FFF;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #181C25;
        font-size: 12px;
        line-height: 24px;

        .avatar {
          width: 20px;
          height: 20px;
          margin-right: 4px;
          margin-top: -2px;
        }

        .loading {
          margin-left: 4px;
          width: 34px;
          height: 6px;
        }
      }
    }
  }

  .footer {
    height: 20px;
    margin-top: 6px;
    display: flex;

    .option {
      display: flex;
      align-items: center;
      cursor: pointer;

      img {
        width: 16px;
        height: 16px;
      }

      span {
        margin-left: 4px;
        background: radial-gradient(110% 110% at 16.75% 100%, #0099FF 0%, #A033FF 60%, #FF5280 90%, #FF7061 100%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        font-size: 14px;
        line-height: 14px;
      }
    }
  }
}
</style>
