import * as urlConfig from './url-config/index.js'
import { message, alert } from '@/utils/globals.js'

/**
 * 跳转主站方法
 * @param {String} url
 * @param {String} target _return:返回地址 manage:兼容新版跳转主站管理后台
 * @returns
 */
function redirectToFS(url, target) {
  if (!url) {
    return
  }
  const char0 = url.charAt(0)
  // const localHref = window.location.href;
  // let letBase = localHref.replace(/(:\/\/+)[^.]+/, "$1www");
  let letBase = window.location.href
  if (/^[^:]+:\/\/+/.test(url)) {
    letBase = ''
  } else if (char0 === '#') {
    // # 开头时，保留 location.search 参数
    letBase = letBase.replace(/#.*/, '')
  } else if (char0 === '?') {
    // ? 开头时，只保留 location.pathname
    letBase = letBase.replace(/[#?].*/, '')
    if (url.charAt(1) === '#') {
      // ?# 开头时，转成 pathname + hash 省略无用的 ?
      url = url.slice(1)
    }
  } else if (char0 === '&') {
    // & 开头时，简单合并 search 参数
    letBase = letBase.replace(/#.*/, '')
    if (letBase.indexOf('?') < 0) {
      url = `?${url.slice(1)}`
    }
  } else if (char0 === '/') {
    // / 开头时，只保留 location.host 即 hostname + port
    letBase = letBase.replace(/(.+:\/\/+[^/]+).*/, '$1')
  } else {
    // 其它情况，包括 ./ 和 ../ 开头时，认定为 location.pathname 相对路径
    letBase = letBase.replace(/([^?#]*\/).*/, '$1')
  }
  if (target === '_return') {
    // eslint-disable-next-line consistent-return
    return `${letBase}${url}`
  }

  let openUrl = letBase + url

  // 兼容新版主站跳转管理后台时修改路由地址
  if (openUrl.indexOf('/XV/UI/Home') !== -1 && target === 'manage') {
    openUrl = openUrl.replace(/\/XV\/UI\/Home/g, '/XV/UI/manage')
  }
  window.open(openUrl, target)
}

const isDevEnv = WDP_DEV_ENV === 'DEV' // webpack.DefinePlugin 环境变量

const interfaceMap = {}

/**
 * http函数名定义重名检测
 * @param  {[type]} subConfig [description]
 * @return {[type]}           [description]
 */
function duplicateCheck(subConfig) {
  const keys = Object.keys(subConfig)
  keys.forEach(methodKey => {
    if (interfaceMap[methodKey]) {
      console.error(`重复定义：${methodKey}`)
    } else {
      interfaceMap[methodKey] = subConfig[methodKey]
    }
  })
}

const cNameList = Object.keys(urlConfig)
cNameList.forEach(cName => duplicateCheck(urlConfig[cName]))

// 严重错误，需要跳到登录页
function alertError(mesg) {
  alert(mesg, () => {
    redirectToFS('?#app/manage/myapps', '_self') // 考虑有 FS 权限而仅无营销通权限的用户，先离开营销通，再刷新页面
    window.location.reload() // 考虑全无 FS 权限的用户，如果只跳 hash 的话，页面不会刷新，不能触发登录页
  })
}

// 业务自定义报错情况处理
function apiWarnings(response, nameKey) {
  const requestConfig = interfaceMap[nameKey]
  const warnings = (requestConfig || {}).warnings || {} // 返回码报错白名单
  if (warnings === '*') {
    return true // 业务方要求自行处理所有 errCode，则不再调用统一报错逻辑
  }
  let errMesg = warnings[response.errCode]
  let errType = ''
  if (!(`${response.errCode}` in warnings)) {
    return false
  } // 如果业务方没有处理该错误，由后继统一处理
  if (errMesg.match(/^[!_]/)) {
    errType = errMesg.charAt(0)
    errMesg = errMesg.slice(1)
  }
  if (errType === '_' && errMesg) {
    message({ message: errMesg, duration: 2000, type: 'error' }) // 较低级别的错误，用 message 方式提示
  } else if (errMesg) {
    alert(errMesg) // 重要的错误，弹 alert 提示
  }
  return true
}

/**
 * 业务报错处理
 * @param  {[type]} response [description]
 * @return {[type]}          [description]
 */
function onError(response, nameKey) {
  if (
    'Value' in response
    && 'Result' in response
    && response.Result.StatusCode === 0
  ) {
    return // 主站风格的返回数据，StatusCode 为 0 表示未出错
  }
  if (response.errCode === 0 || response.errCode === 's106240000') {
    // by 大雄 去掉了 && 'data' in response
    return // 营销通风格的返回数据，errCode 为 0 表示未出错
  }
  if (apiWarnings(response, nameKey)) {
    // 如果业务方配置了错误容忍，或者自定义错误处理，则不再弹统一提示
    return
  }
  console.error('错误：', nameKey, isDevEnv && interfaceMap[nameKey])
  switch (response.errCode) {
    case 60004: {
      break // do nothing, but WHY
    }
    case 2001: // 需要重新登录
    case 50000:
    case 11006:
    case 13000:
    case 2000: {
      alertError($t('marketing.services.dlgqqzxdl_589077'))
      break
    }
    case 50001: {
      alertError($t('marketing.services.nmyglyqxql_4b06ec'))
      break
    }
    case 26002: {
      alert($t('marketing.services.qkjglyznzr_88c6e6'))
      break
    }
    case 52017: {
      alert($t('marketing.services.xjddjywqqb_191d1e'))
      break
    }
    case 52018: {
      alert($t('marketing.services.xjfxdjjeyd_6de851'))
      break
    }
    default: {
      alert((response && response.errMsg) || $t('marketing.commons.sjwcxdqsxy_f37acc'))
      break
    }
  }
}

/**
 * 系统错误，网络错误处理
 * @return {[type]} [description]
 */
function onSystemError(response, nameKey) {
  console.error('错误：', nameKey)
  console.log(response)
}

// 将 $.ajax 调用封装成 promise
function requestPromise(requestOpts) {
  if (requestOpts.onprogress) {
    // 支持订阅 onprogress 事件，用于文件上传进度条
    requestOpts.xhr = () => {
      const xhr = $.ajaxSettings.xhr()
      xhr.upload.onprogress = requestOpts.onprogress
      return xhr
    }
  }
  return new Promise((resolve, reject) => {
    $.ajax(requestOpts).then(resolve, reject)
  })
}

// 将 FHHApi 调用封装成 promise
function FHHApiPromise(requestOpts) {
  return new Promise((resolve, reject) => {
    requestOpts.error = reject
    requestOpts.success = resolve
    FS.util.FHHApi(requestOpts, {})
  })
}

// 业务方 response hook 处理
function responseThen(response, nameKey, isUseEm8) {
  const requestConfig = interfaceMap[nameKey]

  // 兼容CEP接口返回值
  if (isUseEm8 && response && response.Value) {
    response = response.Value
  }

  // 错误处理
  onError(response, nameKey)

  if (requestConfig.onResponse) {
    response = requestConfig.onResponse(response, nameKey) || response
  }
  return response
}
// request hook
function beforeRequest(requestOpts, nameKey) {
  const requestConfig = interfaceMap[nameKey]
  if (requestConfig.onRequest) {
    requestOpts = requestConfig.onRequest(requestOpts, nameKey) || requestOpts
  }
  return requestOpts
}

// 给 url 添加 get 参数：traceId
function setTraceId(url) {
  const traceId = `traceId=${FS.util.getTraceId()}`
  const segments = url.split('?')
  segments[1] = traceId + (!segments[1] ? '' : `&${segments[1]}`)
  return segments.join('?')
}

// 请求参数、数据格式化
function reqDataTransfer(requestOpts, nameKey) {
  requestOpts = $.extend({}, requestOpts)
  const item = interfaceMap[nameKey]
  console.log('requestOpts', item)
  if (
    Object.prototype.toString.call(requestOpts.data) === '[object FormData]'
  ) {
    // 支持文件上传的 FormData 型表单数据
    requestOpts.processData = false // 要求 jQuery 不对数据作格式化
    requestOpts.contentType = false // 要求 jQuery 不显式指定 contentType
  } else if (requestOpts.type === 'POST') {
    // POST 数据默认 json 序列化
    if (requestOpts.contentType.indexOf('application/json') === 0) {
      requestOpts.data = JSON.stringify(
        $.extend({}, item.default, requestOpts.data),
      )
    }
  } else if (item.default) {
    // GET 数据不格式化，交由 jQuery 处理
    requestOpts.data = $.extend({}, item.default, requestOpts.data)
  }
  requestOpts.url = setTraceId(requestOpts.url) // 给 url 添加 get 参数：traceId
  return requestOpts
}

const defaultHeaders = {
  'accept-language': FS.util.getAcceptLanguage() || navigator.language,
}
const defaultPrefix = '/FHH/EM8HMARKETING'

const prefixIgnoreUrls = [
  '/FHH/',
  '/wechatunion/',
  '/EM1HNCRM',
  '/marketing/',
  '/EM1AWECHATUNION2',
  '/EM1APAY',
  '/EM1HSMSPLATFORM'
]

// 将 $.get, $.post 调用参数形式转换成 $.ajax 调用参数形式
function requestTransfer(nameKey, data, optsFromeConfig, opts = {}) {
  let item = interfaceMap[nameKey]
  const needPrefix = !prefixIgnoreUrls.some(url => String(item.url).startsWith(url))

  // 在body内默认新增userAgent和referer两个参数
  try {
    if (data) {
      if (!data.referer) {
        data.referer = window.location.href
      }
      if (!data.userAgent) {
        data.userAgent = navigator.userAgent
      }
    } else {
      data = {
        referer: window.location.href,
        userAgent: navigator.userAgent,
      }
    }
  } catch (error) {
    console.log(error)
  }
  let requestOpts = $.extend(
    {
      timeout: 6 * 6000,
      type: (item.type || 'POST').toUpperCase(),
      url: `${needPrefix && !item.noPrefix ? (item.prefix || opts.baseUrl || defaultPrefix) : ''}${item.url}`,
      data,
      contentType: 'application/json',
      headers: { ...defaultHeaders, ...item.headers },
    },
    optsFromeConfig || {},
    opts,
  )
  // 是否使用是em8
  const isUseEm8 = requestOpts.url.match(/FHH\/EM8HMARKETING|FHH\/EM8HQYWXMARKETING|EM8HAPPMARKETING|EM1HSMSPLATFORM/) !== null
  requestOpts = reqDataTransfer(requestOpts, nameKey)
  requestOpts = beforeRequest(requestOpts, nameKey) // request hook
  if (requestOpts.then && typeof requestOpts.then === 'function') {
    return requestOpts
  }
  let reqPromise
  if (item.urlType === 'FHH') {
    reqPromise = FHHApiPromise(requestOpts)
  } else {
    reqPromise = requestPromise(requestOpts)
  }
  item = 0
  return reqPromise
    .then(response => responseThen(response, nameKey, isUseEm8), // 业务方 response hook 处理
    )
    .catch(response => {
      onSystemError(response, nameKey)
      return response
    })
}

const ApiService = {}
const _interfaceList = Object.keys(interfaceMap)
_interfaceList.forEach(_interface => {
  ApiService[_interface] = (nameKey => {
    const func = (data, opts) => requestTransfer(nameKey, data, interfaceMap[_interface].opts, opts)
    func.getUrl = () => interfaceMap[nameKey].url
    return func
  })(_interface)
})

// 本模块导出对象（ApiService）的所有 key 都被设计为 apiName
// 这里占用 $ 符号用以挂载静态工具方法
ApiService.$ = {
  alertError,
  getDef(name) {
    return interfaceMap[name]
  },
}

ApiService.$.kk = function (kk, data) {
  const result = {}
  const keys = Object.keys(data)
  keys.forEach(key => {
    if (key in kk) {
      if (kk[key].sk) {
        result[kk[key].k] = ApiService.$.kk(kk[key].sk, data[key])
      } else {
        result[kk[key]] = data[key]
      }
    } else {
      result[key] = data[key]
    }
  })

  return result
}

export default ApiService
