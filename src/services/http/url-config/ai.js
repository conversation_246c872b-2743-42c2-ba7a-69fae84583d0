import cookie from '@/utils/cookie.js'

function qsToken(requestOpts) {
  requestOpts.url += `&_fs_token=${cookie.get('fs_token') || ''}`
}

export default {
  addTemplate: {
    url: '/aiChat/addTemplate', // 新增模板
    headers: {
      identityCheckType: 0,
    },
  },
  chatCompleteWithSession: {
    url: '/aiChat/chatCompleteWithSession', // AI对话
    headers: {
      identityCheckType: 0,
    },
  },
  chatCompleteWithoutSession: {
    url: '/aiChat/chatCompleteWithoutSession', // AI对话
    headers: {
      identityCheckType: 0,
    },
  },
  chatCompleteWithoutSessionApp: {
    url: '/web/aiChat/chatCompleteWithoutSession', // AI对话
    prefix: '/FHH/EM8HAPPMARKETING',
    headers: {
      identityCheckType: 0,
    },
  },
  getChatCompleteResult: {
    url: '/aiChat/getChatCompleteResult', // 获取AI对话结果
    headers: {
      identityCheckType: 0,
    },
  },
  deleteRecord: {
    url: '/aiChat/deleteRecord', // 删除会话记录
    headers: {
      identityCheckType: 0,
    },
  },
  deleteSession: {
    url: '/aiChat/deleteSession', // 删除会话
    headers: {
      identityCheckType: 0,
    },
  },
  editSession: {
    url: '/aiChat/editSession', // 会话改名
    headers: {
      identityCheckType: 0,
    },
  },
  setSessionTop: {
    url: '/aiChat/setSessionTop', // 会话置顶
    headers: {
      identityCheckType: 0,
    },
  },
  deleteTemplate: {
    url: '/aiChat/deleteTemplate', // 删除系统模板
    headers: {
      identityCheckType: 0,
    },
  },
  getAllSystemTemplates: {
    url: '/aiChat/getAllSystemTemplates', // 获取系统模板
    headers: {
      identityCheckType: 0,
    },
  },
  pageQueryObjDatas: {
    url: '/aiChat/pageQueryObjDatas', // 分页查询对象数据
    headers: {
      identityCheckType: 0,
    },
  },
  queryObjDataById: {
    url: '/aiChat/queryObjDataById', // 根据ID查询对象数据
    headers: {
      identityCheckType: 0,
    },
  },
  queryObjFiledInfo: {
    url: '/aiChat/queryObjFiledInfo', // 查询对象字段数据
    headers: {
      identityCheckType: 0,
    },
  },
  pageQueryRecords: {
    url: '/aiChat/pageQueryRecords', // 获取会话记录
    headers: {
      identityCheckType: 0,
    },
  },
  pageQuerySessions: {
    url: '/aiChat/pageQuerySessions', // 获取会话
    headers: {
      identityCheckType: 0,
    },
  },
  pageQueryTemplates: {
    url: '/aiChat/pageQueryTemplates', // 获取非系统模板
    headers: {
      identityCheckType: 0,
    },
  },
  reChatCompleteWithSessionAndRecord: {
    url: '/aiChat/reChatCompleteWithSessionAndRecord', // 重新生成
    headers: {
      identityCheckType: 0,
    },
  },
  updateTemplate: {
    url: '/aiChat/updateTemplate', // 修改模板
    headers: {
      identityCheckType: 0,
    },
  },
  getCurrentUserAvatar: {
    url: '/aiChat/getCurrentUserAvatar', // 获取当前用户头像
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatGetRecord: {
    url: '/aiChat/getRecord', // 根据recordId获取会话,若重新生成中,会返回[-2:系统繁忙],须轮询调用
    headers: {
      identityCheckType: 0,
    },
  },
  aiChartCreateShare: {
    url: '/aiChat/createShare', // 创建分享快照
    headers: {
      identityCheckType: 0,
    },
  },
  aiChartGetShareDetail: {
    url: '/aiChat/getShareDetail', // 获取分享详情
    headers: {
      identityCheckType: 0,
    },
  },
  markdownToHTML: {
    url: '/aiChat/markdownToHTML', // markdownToHTML
    headers: {
      identityCheckType: 0,
    },
  },
  aiChartEditRecord: {
    url: '/aiChat/editRecord', // 修改单个会话内容
    headers: {
      identityCheckType: 0,
    },
  },
  aiChartEditRecordV2: {
    url: '/aiChat/editRecordV2', // 修改单个会话内容
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatMatchScene: {
    url: '/aiChat/matchScene', // 匹配场景
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatCreatePrompt: {
    url: '/aiChat/createPrompt', // 新建模板
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatEditPrompt: {
    url: '/aiChat/editPrompt', // 编辑模板
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatPageQueryPrompt: {
    url: '/aiChat/pageQueryPrompt', // 查询模板
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatMarkPrompt: {
    url: '/aiChat/markPrompt', // 收藏/取消收藏模板
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatReport: {
    url: '/aiChat/report', // 用户操作埋点上报
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatAgentSimpleInfo: {
    url: '/FHH/EM1HAIGC/agent/simpleInfo', // 通过agent apiName获取agent的简要信息，包含按钮
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatCreateReport: {
    url: '/aiChat/createRecord', // 创建消息记录
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatQueryLayoutAndData: {
    url: '/aiChat/queryObjAbstractLayoutAndData', // 获取对象摘要和数据
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatAssembleRequestUrl: {
    url: '/aiChat/assembleRequestUrl', // 获取语音识别链接
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatGetAgentWelcome: {
    url: '/aiChat/getAgentWelcome', // 获取agent欢迎语
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatStreamingRequestUrl: {
    url: '/aiChat/streamingRequestUrl', // 获取socket地址
    headers: {
      identityCheckType: 0,
    },
  },
  aiChatPromptCompletions: {
    url: '/aiChat/promptCompletions',
    warnings: '*',
    headers: {
      identityCheckType: 0,
    },
  },
  // 新增Vibe Marketing工作空间对象
  addVMWorkspaceObj: {
    url: '/FHH/EM1HNCRM/API/v1/object/VMWorkspaceObj__c/action/Add',
    default: {},
    onRequest: qsToken,
  },
  // 新增Vibe Marketing工作空间用户对象
  addVMWorkspaceUserObj: {
    url: '/FHH/EM1HNCRM/API/v1/object/VMWorkspaceUserObj__c/action/Add',
    default: {},
    onRequest: qsToken,
  },
  // 新增Vibe Marketing工作空间资产对象
  addVMWorkspaceAssetObj: {
    url: '/FHH/EM1HNCRM/API/v1/object/VMWorkspaceAssetObj__c/action/Add',
    default: {},
    onRequest: qsToken,
  },
}
