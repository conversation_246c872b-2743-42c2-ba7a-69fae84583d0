/*
 * @name 菜单配置
 *
 * @params id - Strring, 路由唯一标识，对应路由模块的name属性
 *         name - Strring, 路由名称，菜单中显示的名称
 *         icon - Strring, 菜单图标
 *         params - Object, 路由跳转带参
 *         children - Array, 子路由
 *         hide - <PERSON><PERSON><PERSON>, 是否隐藏菜单，默认为false
 *
 * @methods setMenuById(String) => Object 根据ID获取相应的菜单。
 *          setMenuById(String, Object) 根据ID重置相应的菜单。
 */
import _ from 'lodash'

const menuDic = {}
const mDatas = {
  selected: {},
  unfold: {},
  folded: false,
}

const menus = [
  {
    id: 'home',
    name: $t('marketing.commons.sy_db1c89'),
    iconfont: 'iconshouye',
    iconColor: '#FF8000',
    hide: false,
  },
  {
    id: 'vibe-marketing-init',
    name: 'Vibe Marketing',
    iconfont: 'iconyunyingjihua',
    iconColor: '#0C6CFF',
    hide: false,
  },
  {
    id: 'kanban',
    name: $t('marketing.commons.yyjh_942f80'),
    iconfont: 'iconyunyingjihua',
    iconColor: '#0C6CFF',
    hide: true,
  },
  {
    id: 'marketing-calendar',
    name: $t('marketing.commons.yxrl_09e6dd'),
    iconfont: 'iconyingxiaorili',
    iconColor: '#30C776',
  },
  {
    id: 'user-marketing',
    name: $t('marketing.commons.yxyh_e1034b'),
    iconfont: 'iconyingxiaoyonghu',
    iconColor: '#189DFF',
    children: [
      {
        id: 'user',
        name: $t('marketing.commons.yhhz_43d8a0'),
      },
      {
        id: 'lead-workbench',
        name: $t('marketing.commons.xs_ad46a9'),
      },
      {
        id: 'AccountObj',
        name: $t('marketing.commons.kh_ff0b20'),
        path: 'marketing-user-object-template',
        query: {
          crmApiName: 'AccountObj',
          crmName: $t('marketing.commons.kh_ff0b20'),
        },
      },
      {
        id: 'ContactObj',
        name: $t('marketing.commons.lxr_52409d'),
        path: 'marketing-user-object-template',
        query: {
          crmApiName: 'ContactObj',
          crmName: $t('marketing.commons.lxr_52409d'),
        },
      },
      {
        id: 'enterprise-base',
        name: $t('marketing.commons.qyk_ac3d26'),
        hide: true,
      },
    ],

  },
  {
    id: 'marketing-user',
    name: $t('marketing.commons.yhyy_3cae1d'),
    iconfont: 'iconyonghuyunying',
    iconColor: '#7341DE',
    children: [
      {
        id: 'target-population',
        name: $t('marketing.commons.mbrq_08808d'),
      },
      {
        id: 'user-picture',
        name: $t('marketing.commons.bqhx_defc14'),
      },
      // {
      //   id: "tags-manage-index",
      //   name: "标签管理"
      // },
    ],
  },
  {
    name: $t('marketing.modules.hk_2dd563'),
    separate: true,
  },
  {
    id: 'content-marketing',
    name: $t('marketing.commons.hdyx_f631fa'),
    iconfont: 'iconhuodongyingxiao',
    iconColor: '#FF522A',
  },
  {
    id: 'live-marketing',
    name: $t('marketing.commons.zbyx_a9fa5d'),
    iconfont: 'iconzhiboyingxiao',
    hide: true,
    iconColor: '#16B4AB',
  },
  {
    id: 'meeting-marketing-init',
    name: $t('marketing.commons.hyyx_5f60fd'),
    hide: true,
    iconfont: 'iconhuiyiyingxiao',
    iconColor: '#0C6CFF',
  },
  {
    id: 'ad-marketing',
    name: $t('marketing.commons.ggyx_0271c7'),
    hide: true,
    iconfont: 'iconguanggaoyingxiao',
    iconColor: '#0C6CFF',
    children: [
      {
        id: 'ad-dashboard',
        name: $t('marketing.modules.ggsjdp_ca7971'),
        hide: false,
      },
      {
        id: 'ad-bytedance',
        name: $t('marketing.commons.jlyqgg_6d988a'),
        hide: true,
      },
      {
        id: 'ad-baidu',
        name: $t('marketing.commons.bdgg_694aaa'),
        hide: true,
      },
      {
        id: 'ad-tencent',
        name: $t('marketing.commons.txgg_22382a'),
        hide: true,
      },
      {
        id: 'ad-google',
        name: $t('marketing.commons.gggg_ca13ab'),
        hide: true,
      },
    ],
  },
  {
    id: 'employee-promotion',
    name: $t('marketing.commons.qyyx_4c4100'),
    iconfont: 'iconyuangongyingxiao',
    iconColor: '#FF522A',
    hide: true,
    children: [
      {
        id: 'promotion',
        name: $t('marketing.pages.setting.ygyx_f7ac22'),
        hide: true,
      },
      {
        id: 'member-promotion',
        name: $t('marketing.commons.hyyx_b017e9'),
        hide: true,
      },
      {
        id: 'partner-marketing',
        name: $t('marketing.commons.hbyx_bd40f9'),
        hide: true,
      },
    ],
  },
  {
    id: 'wechat-home',
    icon: 'wechat',
    name: $t('marketing.commons.gzhyx_8bd4fe'),
    iconfont: 'iconweixingongzhonghaoyingxiao',
    iconColor: '#30C776',
    hide: true,
  },
  {
    id: 'qywx-manage',
    name: $t('marketing.commons.qwyx_79ce25'),
    hide: true,
    iconfont: 'iconqiyeweixin3',
    iconColor: '#189DFF',
    children: [
      {
        id: 'qywx-dashboard',
        name: $t('marketing.pages.qywx_manage.yykb_c212cc'),
        hide: true,
      },
      {
        id: 'customers',
        name: $t('marketing.commons.qywxkh_000b5c'),
        hide: true,
      },
      // {
      //   id: 'customers',
      //   name: $t('marketing.commons.qywxkh_000b5c'),
      //   path: 'object-template',
      //   query: {
      //     crmApiName: 'WechatWorkExternalUserObj',
      //     crmName: $t('marketing.commons.qywxkh_000b5c'),
      //   },
      //   hide: true,
      // },
      {
        id: 'qywx-friends-record',
        name: $t('marketing.commons.qwhyjl_6dd33b'),
        query: {
          crmApiName: 'WechatFriendsRecordObj',
          crmName: $t('marketing.commons.qwhyjl_6dd33b'),
        },
        hide: true,
      },
      {
        id: 'qywx-group',
        name: $t('marketing.commons.qwkhq_fe57c8'),
        query: {
          crmApiName: 'WechatGroupObj',
          crmName: $t('marketing.commons.qwkhq_fe57c8'),
          resetTag: false,
        },
        hide: true,
      },
      {
        id: 'customer-circle',
        name: $t('marketing.commons.khpyq_40eac2'),
        hide: true,
      },
      {
        id: 'groups-messaging',
        name: $t('marketing.commons.xxqf_1e6652'),
        hide: true,
      },
      {
        id: 'promote-qrcode',
        name: $t('marketing.commons.yghm_cacfe9'),
        hide: true,
      },
      {
        id: 'group-qrcode',
        name: $t('marketing.commons.qhm_ffe253'),
        hide: true,
      },
      {
        id: 'welcome-lang',
        name: $t('marketing.modules.hyy_45eddf'),
        hide: true,
      },
      {
        id: 'qywx-trigger',
        name: $t('marketing.commons.qw_c19df2'),
        hide: true,
      },
      {
        id: 'qywx-employee',
        name: $t('marketing.commons.qwyg_e3b2e4'),
        params: {
          id: 2,
        },
        hide: true,
      },
      // {
      //   id: "infomation",
      //   name: "信息配置",
      //   hide: true
      // }
    ],
  },
  {
    id: 'sms-marketing-init',
    name: $t('marketing.commons.dxyx_de4a39'),
    icon: 'sms-marketing',
    params: {
      page: 'welcome',
    },
    hide: true,
    iconfont: 'iconduanxin2',
    iconColor: '#FF8000',
    children: [
      {
        id: 'sms-group-send',
        name: $t('marketing.commons.dxtg_9a6fde'),
        hide: true,
      },
      {
        id: 'sms-template',
        name: $t('marketing.commons.dxmb_dbe8ba'),
        params: {
          id: 1,
        },
        hide: true,
      },
      {
        id: 'sms-group-send-record',
        name: $t('marketing.modules.fsjl_4865e7'),
        hide: true,
      },
      // {
      //   id: 'sms-marketing-setting',
      //   name: '短信设置',
      //   params: { page: 'quota' },
      //   hide: true,
      // },
    ],
  },
  {
    id: 'mail-welcome',
    name: $t('marketing.commons.yjyx_194e1d'),
    icon: 'mail-marketing',
    params: {
      page: 'welcome',
    },
    hide: true,
    iconfont: 'iconyoujian1',
    iconColor: '#189DFF',
    children: [
      {
        id: 'mail-home',
        name: $t('marketing.modules.yjqf_ea63d0'),
        hide: false,
      },
      {
        id: 'mail-template',
        name: $t('marketing.commons.yjmb_532e4a'),
        params: {
          id: 1,
        },
        hide: false,
      },
      {
        id: 'mail-trigger',
        name: $t('marketing.commons.yj_99df29'),
        hide: false,
      },
      {
        id: 'mail-problem',
        name: $t('marketing.commons.wtyx_12d5c6'),
        hide: false,
      },
      {
        id: 'mail-send-detail',
        name: $t('marketing.components.market_promotion_details.fsmx_3a0ab4'),
        hide: false,
      },
    ],
  },
  {
    id: 'whatsappmenu',
    name: $t('marketing.commons.yx_144714'),
    hide: true,
    iconfont: 'iconwhatsapp1',
    iconColor: '#a4d963',
    children: [
      {
        id: 'whatsapp',
        name: $t('marketing.modules.xxqf_8443da'),
        hide: false,
      },
      {
        id: 'whatsapp-user',
        name: $t('marketing.modules.yh_86b7cf'),
        hide: false,
        path: 'marketing-user-object-template',
        query: {
          crmApiName: 'WechatWorkExternalUserObj',
          crmName: $t('marketing.modules.yh_86b7cf'),
          type: 'whatsapp',
        },
      },
      {
        id: 'whatsapp-friend',
        name: $t('marketing.modules.hy_8d2a32'),
        hide: false,
        path: 'marketing-user-object-template',
        query: {
          crmApiName: 'WechatFriendsRecordObj',
          crmName: $t('marketing.modules.hy_8d2a32'),
          type: 'whatsapp',
        },
      },
      {
        id: 'whatsapp-usergroup',
        name: $t('marketing.modules.yhq_44d880'),
        hide: false,
        path: 'marketing-user-object-template',
        query: {
          crmApiName: 'WechatGroupObj',
          crmName: $t('marketing.modules.yhq_44d880'),
          type: 'whatsapp',
        },
      },
      {
        id: 'whatsapp-employee',
        name: $t('marketing.modules.yg_954d01'),
        hide: false,
        path: 'marketing-user-object-template',
        query: {
          crmApiName: 'WechatEmployeeObj',
          crmName: $t('marketing.modules.yg_954d01'),
          type: 'whatsapp',
        },
      },
    ],
  },
  {
    id: 'social-distribution-global-index',
    params: {
      page: 'planindex',
    },
    // id: 'social-distribution-initial',
    name: $t('marketing.commons.shhfx_cf27f2'),
    icon: 'social-distribution',
    hide: true,
    iconfont: 'iconshehuihuafenxiao',
    iconColor: '#FFB602',
    children_delete: [
      {
        id: 'social-distribution-global-index',
        name: $t('marketing.modules.ggsy_2580fb'),
        hide: false,
      },
      {
        id: 'social-distribution-index',
        name: $t('marketing.commons.fxgl_d9aa36'),
        hide: true,
      },
      {
        id: 'distribution-distributors',
        name: $t('marketing.commons.shfxy_1f81a8'),
        hide: true,
      },
      {
        id: 'distribution-lead',
        name: $t('marketing.commons.fxxs_ad8fe7'),
        hide: true,
      },
      {
        id: 'distribution-data',
        name: $t('marketing.commons.fxsc_9be0e2'),
        params: {
          origin: 'product',
        },
        hide: true,
      },
      {
        id: 'distribution-operators',
        name: $t('marketing.commons.fxgly_5de89d'),
        hide: true,
      },
      {
        id: 'distribution-setting',
        name: $t('marketing.commons.fxdj_ef918b'),
        hide: true,
        params: {
          page: 'level',
        },
      },
      {
        id: 'distribution-plan',
        name: $t('marketing.commons.fxjh_f90dd4'),
        hide: true,
      },
    ],
  },
  {
    id: 'marketing-sdr',
    name: $t('marketing.modules.sb_72eff8'),
    separate: true,
    hide: true,
  },
  {
    id: 'callcenter',
    name: $t('marketing.modules.kfgzt_951fb4'),
    iconfont: 'iconlianxiwo7hui',
    iconColor: '#189DFF',
    hide: true,
    outlink: `${window.location.origin}/XV/UI/Home#/app/callcenter/index/=/home`,
  },
  {
    id: 'clue-workbench',
    name: $t('marketing.modules.xsgzt_9b28b0'),
    iconfont: 'hicon-yingxiaohui12',
    hide: true,
    iconColor: '#30C776',
  },
  {
    id: 'separate-customer',
    // 分销管理员无权使用以下菜单项，也就同时隐藏掉分组名称
    name: $t('marketing.modules.py_4375f8'),
    separate: true,
    hide: false, // 用于vue数据响应占位key，非管理员账号不显示此分类标题
  },
  {
    id: 'marketing-automation',
    name: $t('marketing.commons.yxzdh_78d728'),
    iconfont: 'iconyingxiaozidonghua',
    iconColor: '#16B4AB',
    children: [
      {
        id: 'trigger',
        name: $t('marketing.commons.mbzx_d436d1'),
      },
      {
        id: 'marketing-process',
        name: $t('marketing.commons.yxlc_e347db'),
        hide: true,
      },
      {
        id: 'webhook',
        name: 'Webhook',
        hide: true,
      },
    ],
  },
  {
    id: 'target-marketing-init',
    name: $t('marketing.commons.mbrqyy_edf1b4'),
    iconfont: 'iconshichangROIfenxi',
    iconColor: '#FF8000',
  },
  {
    id: 'member-center',
    name: $t('marketing.commons.hyzx_12688a'),
    hide: true,
    iconfont: 'iconhuiyuanzhongxin1',
    iconColor: '#FF522A',
    children: [
      {
        id: 'member',
        name: $t('marketing.modules.hylb_5386bb'),
        hide: true,
      },
      {
        id: 'member-content',
        name: $t('marketing.commons.hynr_e2436c'),
        hide: true,
      },
      {
        id: 'member-page',
        name: $t('marketing.commons.hyym_1b5d85'),
        hide: true,
      },
      {
        id: 'member-setting',
        name: $t('marketing.commons.hysz_88f187'),
        hide: true,
      },
    ],
  },
  {
    id: 'separate-manages',
    name: $t('marketing.commons.gl_08b55f'),
    separate: true,
    hide: false, // 用于vue数据响应占位key，非管理员账号不显示此分类标题
  },
  {
    id: 'promotion-apps',
    name: $t('marketing.modules.nrzx_2ba00f'),
    iconfont: 'iconneirongzhongxin',
    iconColor: '#FF8000',
    children: [
      {
        id: 'site-list',
        name: $t('marketing.commons.wym_5fd4fb'),
        params: {
          type: 'index',
        },
      },
      {
        id: 'article',
        name: $t('marketing.commons.hkwz_ea2e34'),
      },
      // {
      //   id: 'invitation',
      //   name: '活动邀请',
      // },
      {
        id: 'product',
        name: $t('marketing.commons.gscp_4bd0a5'),
      },
      {
        id: 'form',
        name: $t('marketing.commons.xsbd_102b1f'),
      },
      {
        id: 'poster-gallery',
        name: $t('marketing.commons.qyhb_a11919'),
        // params: { type: 'list' },
      },
      {
        id: 'picture',
        name: $t('marketing.commons.tpk_db7625'),
      },
      {
        id: 'file',
        name: $t('marketing.commons.gswj_553574'),
      },
      {
        id: 'video',
        name: $t('marketing.commons.sp_7fcf42'),
      },
      {
        id: 'cta-list',
        name: $t('marketing.pages.cta.zj_e540d1'),
        iconfont: 'iconcta',
        iconColor: '#7342de',
      },
      {
        id: 'fs-coupons',
        name: $t('marketing.commons.fxyhq_ee2620'),
        hide: true,
      },
      {
        id: 'coupons-template',
        name: $t('marketing.pages.Coupons.wxsjq_d1392d'),
        hide: true,
      },
      // {
      //   id: "external-content",
      //   name: "外部内容"
      // }
    ],
  },
  {
    id: 'website',
    name: $t('marketing.commons.gw_847652'),
    icon: 'website-access-init',
    hide: true,
    iconfont: 'iconguanwang',
    iconColor: '#30C776',
    children: [
      {
        id: 'website-access-init',
        name: $t('marketing.commons.gwyy_884dde'),
      },
      {
        id: 'website-access-qrcode',
        name: $t('marketing.pages.website_access.jkewm_79e61a'),
      },
    ],
  },
  {
    id: 'vapp-setting',
    name: $t('marketing.modules.szzt_b97c15'),
    iconfont: 'iconxiaochengxuweizhan',
    iconColor: '#87CC3B',
    hide: true,
    children: [
      {
        id: 'mini-app-index',
        name: $t('marketing.modules.qywz_8547c6'),
      },
      {
        id: 'miniapp-setting',
        name: $t('marketing.commons.xcxsz_cc2d92'),
      },
    ],
  },
  {
    id: 'marketing-collaboration',
    name: $t('marketing.modules.yxxt_7dabbb'),
    iconfont: 'iconyingxiaoxietong',
    iconColor: '#fc8000',
    children: [{
      id: 'marketing-assistant',
      name: $t('marketing.commons.yxzs_e6976d'),
      params: {
        path: 'employee',
      },
    },
    {
      id: 'setting-channel',
      name: $t('marketing.commons.ygmp_375a20'),
      params: {
        id: 1,
      },
    }],
  },
  {
    id: 'report',
    name: $t('marketing.commons.sjfx_6450d8'),
    iconfont: 'iconshujufenxi',
    iconColor: '#FF522A',
    children: [
      {
        id: 'report-summary',
        name: $t('marketing.commons.qytgtj_a88f39'),
      },
      {
        id: 'report-qywx-moments',
        name: $t('marketing.commons.qwpyqtj_945a64'),
        hide: true,
      },
      {
        id: 'report-qywx-group-message',
        name: $t('marketing.commons.qwxxqftj_3bb1c7'),
        hide: true,
      },
      {
        id: 'report-forward',
        name: $t('marketing.commons.yhzftj_396468'),
      },
      {
        id: 'report-partner',
        name: $t('marketing.commons.hbtgtj_07431f'),
        hide: true,
      },
      {
        id: 'report-analysis',
        name: $t('marketing.commons.nrtgxgfx_d3827d'),
      },
      // {
      //   id: "report-employee",
      //   name: "员工推广统计"
      // },
    ],
  },
  {
    id: 'setting-setitems',
    name: $t('marketing.modules.xtsz_140976'),
    hide: true,
    iconfont: 'iconxitongshezhi',
    iconColor: '#189DFF',
  },
  // {
  //   id: 'setting',
  //   name: '设置',
  //   hide: true,
  //   children: [
  //     {
  //       id: 'horn-create',
  //       name: '系统公告',
  //     },
  //   ],
  // },
]

function setMenuDic() {
  _.forEach(menus, item => {
    if (!item.icon && item.icon !== false && item.id) {
      item.icon = item.id
    }
    if (item.id) {
      menuDic[item.id] = item
    }
    if (!('unfold' in item)) {
      item.unfold = false
    }
  })
  _.forEach(menus, item => {
    const childDic = {}
    _.forEach(item.children, child => {
      child.parentId = item.id
      if (child.id) {
        childDic[child.id] = child
        menuDic[child.id] = child
      }
      if (!('unfold' in child)) {
        child.unfold = false
      }
    })
    item.childDic = childDic
  })

  console.log('setMenuDicdone', new Date().getTime())
}

function getMenuById(id) {
  return menuDic[id]
}

function setMenuById(id, config) {
  const found = getMenuById(id)
  if (found) {
    _.extend(found, config)
  }
}

/**
 * 新增菜单
 * @param {*} newMenu
 * @param {*} parentId
 * @param {*} preId 插入前一个id
 */
function addMenu(newMenu, parentId, preId) {
  const parentMenu = (parentId ? menus.find(item => item.id === parentId).children : menus)
    || []
  if (preId) {
    const index = parentMenu.findIndex(m => m.id === preId)
    parentMenu.splice(index + 1, 0, newMenu)
  } else {
    parentMenu.push(newMenu)
  }

  menuDic[newMenu.id] = { ...newMenu, parentId }
}

function removeMenu(id) {
  const found = getMenuById(id)
  const parentMenu = (found.parentId
    ? menus.find(item => item.id === found.parentId)?.children
    : menus) || []
  const index = parentMenu.findIndex(item => item.id === id)
  parentMenu.splice(index, 1)
  menuDic[id] = null
}

function toggle(menu, flag) {
  if (typeof menu === 'string') {
    menu = getMenuById(menu) || {}
  }
  menu.unfold = !menu.unfold
  if (typeof flag !== 'undefined') {
    menu.unfold = !!flag
  }
}

function highLight(menu) {
  if (typeof menu === 'string') {
    menu = getMenuById(menu) || {}
  }
  mDatas.selected = menu
  if (menu.parentId) {
    toggle(menu.parentId, true) // 子菜单高亮时，自动展开其对应的父菜单
  }
}

setMenuDic()

export {
  menus,
  mDatas,
  highLight,
  toggle,
  getMenuById,
  setMenuById,
  setMenuDic,
  addMenu,
  removeMenu,
}
